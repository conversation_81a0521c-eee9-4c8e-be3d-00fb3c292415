## redis

云内测试： name:    yx-intelli-bot-test 云内地址： 10.178.6.101:16383 云外地址： 10.159.145.121:6383 10.159.144.45:6383
10.159.145.234:
6383

云外线上： name: yx_intelli_bot_onl 10.200.210.171:16383 10.200.210.172:16383 10.200.210.173:16383

10.200.210.174:16383 10.200.210.175:16383 10.200.210.176:16383

## 特殊 SQL

SELECT * FROM yx_bot_v2.TB_YX_KFKM_SHORTCUT where id in (
SELECT shortcutId from yx_bot_v2.TB_YX_KFKM_SHORTCUT_KNOWLEDGE_REL where knowledgeId in
(
SELECT id FROM yx_bot_v2.`TB_YX_KFKM_KNOWLEDGE`
WHERE Status=4 and KnowledgeType=1 and Id in
(SELECT KnowledgeId FROM yx_bot_v2.TB_YX_KFKM_KNOWLEDGE_ANSWER WHERE AnswerUse=2)
)
)

## 需求

商品选择器 最近浏览 最近订单 输入联想 样式

## 保留

SPU 或者某类目的解决率

用图谱来存数据，可以避免稀疏问题

主动预测，不能做成分类问题，只能做成匹配问题，或者是固定分类集合

尺码表 -》 4岁穿多大

什么时候发货 SOP 优化

用户状态 -》 SOP直接触发

多个问题配置一样的问题

你好，在吗，等正常会话流程 在吗?

# 处理一半

新人身份识别，主动推送问题 19223642818 我的资产在哪（通过配置apollo 进行处理） 19223511934 订单解析出商品 （在 bot-sync
中解析卡片消息，通过商品名匹配，灌入itemId上下文）
19223231222 评论（处理了价格评论） 直接卡片点击，是否算有效会话？ 19223777211

# 已处理

机器人 称为 -》助理 订单页-》发货SOP

# 数据分析思路

自助卡片使用情况

使用了的和没有使用的解决率区别

有效会话的定义需要修正

售前售后比例变化

售前售后解决率区别

询单咨询的量的变化

select * from dw.dwd_access_event_detail_all_i where ds="2020-10-09" and cc="APP" and event_action="click" and
event_name = 'click_autohelp_selfservice'

select * from kefu.kf_session_robot_tag_yjc where from_unixtime(floor(end_time/1000), 'yyyy-MM-dd') == '2020-10-09'

自助卡片点击率，解决率

select user_name, event_params,user_id,event_time from dw.dwd_access_event_detail_all_i where ds="2020-10-09" and cc="
APP" and event_action="click" and event_name = 'click_autohelp_selfservice'

select id, end_time, yx_user_id from kefu.kf_session_robot_tag_yjc where from_unixtime(floor(end_time/1000), '
yyyy-MM-dd') == '2020-10-09'

不同入口的占比

mysql来存表格数据

用户问了问题，离开，24小时再来找人工，可能是同一个问题，也可能是新的问题，比如昨天买了东西，售前咨询，今天又来下单买东西，售前咨询

## 注意点

由于历史原因，测试和预发布环境的的 健康检查 URL 为 /i/health 线上环境为 /yanxuan-intelli-bot/health.do

## 订单数据

需求：你好等问候的回复，这个把会话的第一句都拉出来，看一下里面的数据长什么样子 气泡词的推荐 猜你想问逻辑的预埋 性能优化

用户订单数据的缓存，避免多次查询 前面会话的结果缓存

再见、谢谢、你好、好的

不同入口的解决率 会员的解决率 某个商详页的解决率

完整的测试流程

尺码 商详页 主动预测 特殊意图 上下文 特殊输入

你好等逻辑，没有生效

人工诉求，后面可以再推荐曝光一些问题 尺码问题 商品属性问题 订单页， 主动预测个性化

# 全链路的日志，包括FAQ匹配，KBQA

# KBQA重构，订单上下文参与预测

# 知识点的曝光热度

茅台易豆的咨询 活动优惠没有生效

比例 热门商品的知识覆盖度 报表

curl -X
POST 'http://127.0.0.1:8550/proxy/test.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/usercenter-prophet.do'
-d '{
"userId" : 11,
"sessionId" : 11,
"platform" : "APP",
"source" : "USER_CENTER",
"device": "IOS"
}' -H "Content-Type: application/json"

add

19226227518

嗯， 好的 好 谢谢 好的，谢谢 明白了

curl -X
POST 'http://127.0.0.1:8550/proxy/release.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/input_associate.do'
-d '{"query":"柜","sessionId":19213455454}' -H "Content-Type: application/json"

http://127.0.0.1:8550/proxy/test.yanxuan-intelli-bot.service.mailsaas/api/mps/receive

curl -X
POST 'http://127.0.0.1:8550/proxy/release.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/shortcut_rcmd.do'
-d '{"rcmdNum":5,"sessionId":19226239339,"yxUserId":0}]' -H "Content-Type: application/json"

curl -X POST 'http://127.0.0.1:8550/proxy/release.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/yx_bot_kb.do'
-d '{"sessionId":19226239339,"userId":0}]' -H "Content-Type: application/json"

curl -X POST 'http://127.0.0.1:8550/proxy/test.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/prophet.do'
-d '{"sessionId":999,"userId":17946037,"itemId": 4048675}' -H "Content-Type: application/json"

curl -d 'content={"sessionId":"999","userName":"<EMAIL>","type":0,"data":"
红烧肉怎么少"}' "http://127.0.0.1:8550/proxy/release.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/yx_bot_kb.do?"

curl -X POST 'http://**************:8080/yanxuan-intelli-bot/multi_channel_search.do' -d '{"channelId":1990361,"query":"
629545819360" }' -H "Content-Type: application/json"

curl -X POST 'http://127.0.0.1:8550/proxy/release.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/multi_channel_qa_addi.do' -d '{"channelId":6801062,"sessionId":19226239339,"rawMsg":"商品细节","itemId":3999637}' -H "Content-Type: application/json"

curl -X
POST 'http://127.0.0.1:8550/proxy/release.yanxuan-intelli-faq-v2.service.mailsaas/faq/context-pipeline-multi-channel.do'
-d '{
"channel":"1990361",
"itemId":3987064,
"inputsHst":{
"words":[
"效果",
"多大"
],
"cleanedText":"使用了费洛蒙香氛系列商品对方没什么效果"
},
"inputs":[
{
"words":[
"效果",
"多大"
],
"cleanedText":"使用了费洛蒙香氛系列商品对方没什么效果"
}
],
"sessionId":111 }' -H "Content-Type: application/json"

curl -X POST 'http://**************:8080/yanxuan-intelli-bot/multi_channel_qa.do' -d '{"channelId":6801062,"sessionId":192262393392,"rawMsg":"如何查看物流信息","messageSource":1,"messageType":1,"sessionInteraction":1}' -H "Content-Type: application/json"

curl -X POST 'http://**************:8080/yanxuan-intelli-bot/multi_channel_qa.do' -d '{"channelId":1990361,"sessionId":1922623933923,"rawMsg":"https://detail.tmall.com/item.htm?id=673725847858"}' -H "Content-Type: application/json"

"床"
在线 在线客服 转人工

19226423238 可以通过商品实体的办法，确定知识匹配的范围

被子 至少不应该匹配出 洗发膏的问题

知识库的质量把控，商品和类目标签的审核

curl -X POST 'http://127.0.0.1:8550/proxy/release.yanxuan-intelli-bot-sync.service.mailsaas/algoInfo//refreshSingleVec' -d '{
"knowledgeId":4045327,
"question":"测试线上验证0425线上测试1"}' -H "Content-Type: application/json"

curl -d 'sen=习近平快死了吗' 'http://10.200.177.150:8083/yanxuan-intelli-bot/test/secure.do'

curl -d 'sen=习近平快死了吗' 'http://10.200.177.150:8083/yanxuan-intelli-bot/test/intent.do'

curl -d 'itemId=4010221' 'http://10.200.177.150:8083/yanxuan-intelli-bot/test/similarity.do' -d 'input=包装是什么'

curl -d 'itemId=4010221' 'http://**************:8080/yanxuan-intelli-bot/test/itemAttribute.do'

## 发送消息

注意染色环境的问题

curl -X POST -i -d '
product=yanxuan-intelli-bot&module=test&subject=test&topic=intelli_bot_qa_recommend_knowledge&messageId=test1234abcdef&payload={"
itemId":0,"intentType":1,"itemPhyCategoryIdStr":"","platformRawItemCardId":0,"messageId":17125,"sessionId":20232452,"
recommendKnowledge":[{"answerId":20387766,"knowledgeId":20142826,"itemId":0,"itemPhyCategoryIdStr":"","source":1}],"
channelId":1990361,"platform":"tb","platformItemId":
0}' 'http://127.0.0.1:8550/proxy/test.yanxuan-mps.service.mailsaas/xhr/message/yanxuan-intelli-bot/send.json' -H "
X-YX-COLOR:feature2"

curl -d 'messageId=17125' -d 'sessionId=20232452' -d '
testType=3' 'http://127.0.0.1:8550/proxy/test.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/test/mpsSend.do'
-H "X-YX-COLOR:feature2"

curl -d 'messageId=17107' -d 'sessionId=20232449' -d '
testType=2' 'http://127.0.0.1:8550/proxy/test.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/test/mpsSend.do'
-H "X-YX-COLOR:feature2"



1