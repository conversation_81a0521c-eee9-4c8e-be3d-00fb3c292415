## 依赖环境说明

### 普通ES

线上环境
http://kibana4.mail.netease.com/
测试环境
http://kibana8.mail.netease.com/

测试环境
https://vectorsearch.yx.netease.com/app/home#/
线上环境
http://logsearch.hz.netease.com/yanxuan-vector-search-v8-online

### 向量ES

## 接口说明

### DMController

#### /yanxuan-intelli-bot/yx_bot_kb.do

机器人交互主接口，根据用户当前轮次输入，结合该次会话其他信息，综合返回知识匹配、商品推荐结果

### 升级日志

#### 2021.05.07-商品推荐升级

1. 添加 推荐语配置文件-conf/data/guide/guideMultiItemRCMDFixText.txt
2. 将原来的GuideRslt 单个推荐结果扩展到 GuideRCMDListRslt 多个推荐结果

#### 2023.9.1

1. 实时问答接口改造
   a. 意图分类 的answer id 需要查询 1
   b. 需要返回answer id 1
   c. 商品属性需要返回id 1
   d. 商品属性问答改造 1
   e. 记录messageId 到 sessionId的cache，1
2. 会话结束消息监听
   a. 根据messageId，platformMessageId 的map查询 platformSessionId 1
   b. 缓存查询历史推荐结果 1
   b. 发送消息 1
3. 无结果会话聚合
   a. 日志查询历史推荐结果，聚合消息
   b. 跟以前的任务去重 天粒度处理
   c. 发送推荐上下文
4. 线上训练任务变更
   a. 根据taskId查询消息内容 1
   b. 根据taskId查询更改指向 1

返回商品各种id 1

#### 2024.2.23

加入 answerUse
知识双写双读
删除 bot-sync的依赖

实时问答里面对于导购类、聊天类的需要不返回

bot 工程重构
实时问答-过滤 sop答案类型

遗留问题： 网页端会话， 没有猜你想问，商品卡片会发过来，19251031077

data 为URL 存在， 比如 https://act.you.163.com/act/pub/ssr/c86PS2KCVJE5.html