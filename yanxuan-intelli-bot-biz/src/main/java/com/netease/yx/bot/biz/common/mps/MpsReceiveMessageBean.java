/**
 * @(#)MpsSubscribeMessageBean.java, 2017/11/17.
 * <p/>
 * Copyright 2017 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.common.mps;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;

/**
 * MPS消息接收Bean
 *
 * <AUTHOR> @ corp.netease.com)
 */
public class MpsReceiveMessageBean implements Serializable {
    private static final long serialVersionUID = -1177422636515459048L;

    /**
     * 产品号
     */
    public String product;

    /**
     * 消息主题
     */
    public String topic;

    /**
     * 消息ID（同一个topic下唯一：用于消费者做幂等）
     */
    public String messageId;

    /**
     * 请求JSON String
     */
    public String payload;

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
