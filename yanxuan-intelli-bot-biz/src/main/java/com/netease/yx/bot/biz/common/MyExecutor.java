package com.netease.yx.bot.biz.common;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 多线程支持，不要删除
 */
@Configuration
public class MyExecutor {
    @Bean
    public ExecutorService executor() {
        int count = Runtime.getRuntime().availableProcessors();
        return Executors.newFixedThreadPool(count * 2);
    }
}

