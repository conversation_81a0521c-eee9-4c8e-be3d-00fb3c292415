/**
 * @(#)FaqApolloConfig.java, 2020/3/16.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.applloconfig;

import com.ctrip.framework.apollo.spring.annotation.EnableAutoUpdateApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ValueMapping;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaRslt;
import com.netease.yx.bot.core.model.entity.check.CheckKnowledge;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Component
@EnableAutoUpdateApolloConfig("channelQa")
@Slf4j
@Getter
public class ChannelQaApolloConfig {
    @ValueMapping("${test.channel.qa.map.v3:{}}")
    private Map<String, List<ChannelQaRslt>> channelQaRsltMapV3;

    @ValueMapping("${intent.core.knowledge.map:{}}")
    private Map<String, Map<String, Long>> intentCoreKnowledgeMap;

    @ValueMapping("${id2question.map:{}}")
    private Map<Long, String> id2QuestionMap;

    @ValueMapping("${item.id.regex.list:[]}")
    private List<String> itemIdRegexList;

    @ValueMapping("${item.attribute.flag:0}")
    private int itemAttributeFlag;

    @ValueMapping("${faq.threshold:0.7}")
    private double faqThreshold;

    @ValueMapping("${item.faq.threshold:0.85}")
    private double itemFaqThreshold;

    @ValueMapping("${intent.threshold:0.8}")
    private double intentThreshold;

    @ValueMapping("${similarity.threshold:0.9}")
    private double similarityThreshold;

    @ValueMapping("${common.faq.similarity.threshold:0.75}")
    private double commonFaqSimilarityThreshold;

    @ValueMapping("${exact.threshold:0.98}")
    private double exactThreshold;

    @ValueMapping("${gpt.use.flag:1}")
    private int gptUseFLag;

    @ValueMapping("${ignore.knowledge.ids:[20787929,1]}")
    private Set<Long> ignoreKnowledges;

    @ValueMapping("${train.ignore.contents:[\"人工\",\"订单编号\",\"订单号\"]}")
    private List<String> trainIgnoreContents;

    @ValueMapping("${format.old.platforms:[\"jd\"]}")
    private List<String> formatOldPlatforms;

    @ValueMapping("${ignore.contents:[\"售后咨询组\"]}")
    private Set<String> ignoreContents;

    // 非卡片消息需要等待的时间
    @ValueMapping("${msg.wait.millisecond:50}")
    private int msgWaitMilliSecond;

    // 是否开启会话结束发送消息
    @ValueMapping("${session.close.mps.flag:1}")
    private int sessionCloseMpsFlag;

    @ValueMapping("${test.intentId.knowledgeIds:[20776799, 20776807,20776788,20776802]}")
    private Set<Long> testIntentIdKnowledgeIds;

    @ValueMapping("${test.check.knowledge.mock:{}}")
    private Map<Integer, List<CheckKnowledge>> mockCheckKnowledgeMap;

    // 是否使用新版的匹配
    @ValueMapping("${faq.match.v2.flag:1}")
    private int faqMatchV2Flag;

    @ValueMapping("${knowledge.es.index:algo_knowledge_es_index_v5}")
    private String knowledgeEsIndex;

    @ValueMapping("${knowledge.es.index.answeruse:algo_knowledge_es_index_with_answeruse}")
    private String knowledgeEsIndexWithAnswerUse;

    @ValueMapping("${sessionRcmdCates:[101591,101588,101696,101674,101520]}")
    private List<Long> sessionRcmdCates;

    @ValueMapping("${qiyuSessionCloseFlag:1}")
    private int qiyuSessionCloseFlag;

    // 商品卡片直出
    @ValueMapping("${goods.resp.knowledgeId:20792873}")
    private long goodsRespKnowledgeId;

    // 订单商品卡片
    @ValueMapping("${order.goods.resp.knowledgeId:20794315}")
    private long orderGoodsRespKnowledgeId;
}