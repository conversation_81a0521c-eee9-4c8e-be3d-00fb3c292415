/**
 * @(#)FeatureStoreService.java, 2020/12/15.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.common;

import com.netease.yanxuan.feast.sdk.FeatureStoreClient;
import com.netease.yanxuan.feast.sdk.vo.FeatureRequest;
import com.netease.yanxuan.feast.sdk.vo.FeatureResult;
import com.netease.yanxuan.feast.sdk.vo.FeaturesBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Component
@Slf4j
public class FeatureStoreService {
    private FeatureStoreClient featureStoreClient;
    @Autowired
    private EnvService envService;

    @PostConstruct
    private void init() {
        featureStoreClient = FeatureStoreClient.getInstance(envService.getEnv());
    }

    public List<FeatureResult> getFeature(FeatureRequest featureRequest) {
        return featureStoreClient.getFeatures(featureRequest, 500, TimeUnit.MILLISECONDS);
    }


    public List<FeatureResult> getFeature(FeatureRequest featureRequest, int timeout) {
        List<FeatureResult> results = new ArrayList<>();
        try {
            results.addAll(featureStoreClient.getFeatures(featureRequest, timeout, TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            log.error("getFeature", e);
        }
        return results;
    }

    public int insertFeature(String featureGroup, FeaturesBean featuresBean) {
        int result = 0;
        try {
            featureStoreClient.insertFeatureGroup(featureGroup, featuresBean);
            result = 1;
        } catch (RuntimeException e) {
            log.error("insertFeature", e);
        }
        return result;
    }

    public List<String> getItemNames(String relatedItemId) {
        FeatureStoreClient featureStoreClient = FeatureStoreClient.getInstance("online");
        List<String> names = new ArrayList<>();
        if (!StringUtils.isEmpty(relatedItemId)) {

            List<String> itemIds = Arrays.asList(StringUtils.split(relatedItemId, ","));
            FeatureRequest featureRequest = new FeatureRequest();
            featureRequest.setIdType("itemid");
            featureRequest.setFeatureNames(new HashSet<String>() {{
                add("item_name");
            }});
            featureRequest.setIds(new HashSet<>(itemIds));

            List<FeatureResult> result = featureStoreClient.getFeatures(featureRequest);
            names.addAll(result.stream().filter(x -> !MapUtils.isEmpty(x.getFeatures())).map(x -> x.getFeatures().get("item_name")).collect(Collectors.toList()));
        }
        return names;
    }
}