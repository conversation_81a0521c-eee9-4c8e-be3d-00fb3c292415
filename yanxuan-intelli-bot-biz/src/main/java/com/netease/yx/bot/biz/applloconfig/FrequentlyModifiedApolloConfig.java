package com.netease.yx.bot.biz.applloconfig;

import com.ctrip.framework.apollo.spring.annotation.EnableAutoUpdateApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ValueMapping;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 可能会被频繁更改的配置
 */
@Component
@EnableAutoUpdateApolloConfig("frequentlyModified")
@Slf4j
@Getter
public class FrequentlyModifiedApolloConfig {
    @ValueMapping("${prophet.top.faq.list:20248512}")
    private String prophetTopFaqList;

    public List<Long> getProphetTopFaqListConfig() {
        List<Long> ids = new ArrayList<>();
        try {
            if (!StringUtils.isEmpty(prophetTopFaqList)) {
                ids.addAll(Arrays.stream(prophetTopFaqList.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("getProphetTopFaqListConfig {}", prophetTopFaqList);
        }

        return ids;
    }
}
