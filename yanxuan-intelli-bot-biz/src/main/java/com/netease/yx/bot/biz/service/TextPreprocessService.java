/**
 * @(#)FormalizeService.java, 2020/4/9.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.Segment;
import com.hankcs.hanlp.seg.common.Term;
import com.netease.yx.bot.common.util.CustomLangUtils;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Service
@Slf4j
public class TextPreprocessService {
    public static final Map<String, String> SIMILAR_REPLACE_MAP = new HashMap<String, String>() {
        {
            put("1111", "1111");
            put("11.11", "1111");
            put("双11", "1111");
            put("双十一", "1111");
            put("11月11日，", "1111");
            put("1212", "1212");
            put("12月12日", "1212");
            put("12.12", "1212");
            put("双十二", "1212");
            put("双12", "1212");
            put("六一八", "618");
            put("四一一", "411");

            put("1米2", "1.2m");
            put("1米5", "1.5m");
            put("1米8", "1.8m");
            put("一米2", "1.2m");
            put("一米5", "1.5m");
            put("一米8", "1.8m");
            put("一米二", "1.2m");
            put("一米五", "1.5m");
            put("一米八", "1.8m");
        }
    };
    private static final Pattern PATTERN_ORDER_NUM = Pattern.compile("^[-\\+]?[\\d]*$");
    private static final Pattern PATTERN_URL = Pattern.compile("(((http|ftp|https)://)|(www\\.))[a-zA-Z0-9\\._-]+\\.[a-zA-Z]{2,6}(:[0-9]{1,4})?(/[a-zA-Z0-9\\&%_\\./-~-]*)?");
    private static final Pattern PATTERN_PUNCTUATION = Pattern.compile("[\\p{P}+~$`^=|<>～｀＄＾＋＝｜＜＞￥× 。\\.？\\?，,]");
    private static final int MAX_LENGTH = 100;

    public static void main(String[] args) {
        String x = "我要退货.)lkj http://www.baidu.com 好的";
        TextPreprocessService manager = new TextPreprocessService();
//        System.out.println(manager.preprocess(x));
        System.out.println(manager.clean("\n"));
        System.out.println(manager.clean("a"));

    }

    public TextBasicData preprocess(String query) {
        return preprocess(query, false);
    }

    public TextBasicData preprocess(String query, boolean needNer) {
        TextBasicData tbd = new TextBasicData();

        if (query == null || query.trim().length() == 0) {
            return tbd;
        }

        String cleanedQuery = clean(query);
        String formalizedQuery = formalize(cleanedQuery);
        List<Term> terms = parseText(formalizedQuery);

        List<String> words = new ArrayList<String>();
        List<String> tags = new ArrayList<String>();
        for (Term term : terms) {
            String word = term.word;
            String tag = term.nature.toString();

            if (StringUtils.trimToEmpty(word).length() > 0) {
                words.add(word);
                tags.add(tag);
            }
        }
        return TextBasicData.builder().rawText(query).cleanedText(formalizedQuery).words(words).tags(tags).build();
    }

    private List<Term> parseText(String sentence) {
        Segment segment = HanLP.newSegment().enableCustomDictionaryForcing(true);
        List<Term> termList = segment.seg(sentence);
        for (Term elem : termList) {
            String word = elem.word;
            if (word.trim().length() == 8) {
                if (PATTERN_ORDER_NUM.matcher(word).matches()) {
                    elem.word = "ORDER_NUM";
                }
            }
        }

        return termList;
    }

    public String clean(String rawQuery) {
        if (StringUtils.isEmpty(rawQuery)) {
            return rawQuery;
        }
        String result = rawQuery;
        // 删除两侧空格
        result = StringUtils.trimToEmpty(rawQuery);
        // 可能已经是空格了，直接返回
        if (StringUtils.length(result) == 0) {
            return result;
        }
        // 繁体转简体
        result = HanLP.convertToSimplifiedChinese(result);
        // 大写转小写
        result = result.toLowerCase();
        // 全角转半角
        result = CustomLangUtils.qj2bj(result);
        // 长度截断，最多20个字
        if (result.length() > MAX_LENGTH) {
            result = result.substring(0, MAX_LENGTH);
        }

        return result;
    }

    /**
     * 3. 文本规范化
     * 3.1 url识别
     * 3.2  过滤无用字符
     * 3.3   同义词替换，包括双十一，双11之类的归一化，1.4m，一米四的归一
     * 3.4   常见错别字的更正
     *
     * @param sentence
     * @return
     */
    private String formalize(String sentence) {

        if (sentence == null || sentence.trim().length() == 0) {
            return sentence;
        }

        // 替换URL
        String ret = PATTERN_URL.matcher(sentence).replaceAll("URL");
        ret = ret.replace(".", "π");
        // 替换部分特殊符号
        ret = PATTERN_PUNCTUATION.matcher(ret).replaceAll(StringUtils.SPACE);
        ret = ret.replace("π", ".");

        // 统一部分说法
        for (Map.Entry<String, String> entry : SIMILAR_REPLACE_MAP.entrySet()) {
            String oldWord = entry.getKey();
            String newWord = entry.getValue();
            ret = ret.replace(oldWord, newWord);
        }

        return ret;
    }
}