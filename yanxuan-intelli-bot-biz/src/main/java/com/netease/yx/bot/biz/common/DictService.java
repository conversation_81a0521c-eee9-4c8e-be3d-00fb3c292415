/**
 * @(#)DictManager.java, 2020/6/4.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.common;

import com.netease.yx.bot.common.util.IoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Service
@Slf4j
public class DictService {
    private static final Set<String> SPECIAL_STOPWORDS = new HashSet<String>() {{
        add("\t");
        add("\n");
    }};
    @Value("${dict.stop_words}")
    private String stopWordsPath;
    @Value("${dict.synonyms_words}")
    private String synonymsWordsPath;
    @Value("${dict.highlight_words}")
    private String highlightWordsPath;
    @Value("${dict.related_words}")
    private String relatedWordsPath;
    private Set<String> stopWords;

    @PostConstruct
    private void init() {
        try {
            stopWords = IoUtils.getLineSetFromFile(stopWordsPath);
            log.info("stopwords :{}", stopWords.size());
        } catch (IOException e) {
            log.error("load stopWordsPath error", e);
        }
    }

    public boolean isStopWord(String word) {
        return stopWords.contains(word) || SPECIAL_STOPWORDS.contains(word);
    }
}