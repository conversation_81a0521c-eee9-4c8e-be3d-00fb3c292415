/**
 * @(#)RedisService.java, 2021/12/20.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.common;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.common.util.GuavaCacheUtil;
import com.netease.yx.bot.common.util.IoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;


/**
 * 功能：存储所有的持久化数据在Redis上
 */
@Service
@Slf4j
public class RedisService {

    private static final int DEFAULT_SAVE_MINUTES = 60 * 24;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private EnvService envService;

    // 如果key不存在，会返回为null
    public String get(String key) {
        // 本地dev环境没有redis，使用LRU内存缓存代替
        if (envService.isDev()) {
            return GuavaCacheUtil.getStr(key);
        }
        String value = stringRedisTemplate.opsForValue().get(key);
        // 避免超时取不到，再试一次
        if (StringUtils.isNotEmpty(value)) {
            return value;
        }
        return stringRedisTemplate.opsForValue().get(key);
    }


    public boolean set(String key, String value) {
        return set(key, value, DEFAULT_SAVE_MINUTES);
    }

    public boolean set(String key, String value, int sessionMinutes) {
        if (envService.isDev()) {
            return GuavaCacheUtil.setStr(key, value);
        }
        try {
            stringRedisTemplate.opsForValue().set(key, value, sessionMinutes, TimeUnit.MINUTES);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean delete(String key) {
        if (envService.isDev()) {
            return GuavaCacheUtil.deleteStr(key);
        }
        return stringRedisTemplate.opsForValue().getOperations().delete(key);
    }

    public boolean publish(String key, String value) {
        if (envService.isDev()) {
            return GuavaCacheUtil.setStr(key, value);
        }
        try {
            stringRedisTemplate.convertAndSend(key, value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public <T> T getObj(String key, TypeReference<T> typeReference) {
        try {
            String value = get(key);
            if (StringUtils.isNotEmpty(value)) {
                return IoUtils.parseJson(get(key), typeReference);
            }
        } catch (IOException e) {
            log.error("getObj", e);
        }
        return null;
    }
}