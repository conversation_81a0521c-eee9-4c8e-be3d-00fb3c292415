/**
 * @(#)VhIntentApolloConfig.java, 2022/3/8.
 * <p/>
 * Copyright 2022 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.applloconfig;

import com.ctrip.framework.apollo.spring.annotation.EnableAutoUpdateApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ValueMapping;
import com.netease.yx.bot.core.model.entity.search.SearchItemInfo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品搜索配置
 */
@Component
@EnableAutoUpdateApolloConfig("itemsearch")
@Slf4j
@Getter
public class ItemSearchApolloConfig {
    @ValueMapping("${dumpIndex:dim_yx_goods_fx_sku_relation_v2}")
    private String dumpIndex;

    @ValueMapping("${dumpIndexForApp:tb_yx_dump_item_info}")
    private String dumpIndexForApp;

    // 使用主站索引的渠道
    @ValueMapping("${useAppIndexChannels:[]}")
    private Set<String> useAppIndexChannels;

    @ValueMapping("${mockSearchItem:[]}")
    private List<SearchItemInfo> mockSearchItemInfo;

    /**
     * 不同渠道的推荐理由
     * 渠道1为主站
     * map第一层为item_id， 第二层为channelId
     */
    @ValueMapping("${channel.rcmd.reason.map:{}}")
    private Map<Long, Map<Long, String>> channelRcmdReasonMap;
}