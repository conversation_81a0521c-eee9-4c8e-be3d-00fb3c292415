/**
 * @(#)SessionAnalysisMsg.java, 4/18/23.
 * <p/>
 * Copyright 2023 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service.mps;

import com.netease.yx.bot.core.model.train.MsgIdMap;
import lombok.Data;

import java.util.List;

/**
 * 会话消息payload
 *
 * <AUTHOR>
 */
@Data
public class SessionAnalysisMsg {

    /**
     * 后端会话ID
     */
    private Long sessionId;

    // 前端消息id和后端消息id的映射关系
    private List<MsgIdMap> messageIds;

    // 会话交互类型，0-人工，1-机器人
    private int sessionInteraction;
}