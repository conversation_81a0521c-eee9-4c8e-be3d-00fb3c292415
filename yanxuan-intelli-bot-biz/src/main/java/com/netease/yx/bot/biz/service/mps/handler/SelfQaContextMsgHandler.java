/**
 * @(#)SessionAnalysisMsgHandler.java, 4/18/23.
 * <p/>
 * Copyright 2023 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service.mps.handler;

import com.netease.yx.bot.biz.common.mps.MpsBaseHandler;
import com.netease.yx.bot.biz.common.mps.MpsHandler;
import com.netease.yx.bot.biz.common.mps.MpsReceiveMessageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 百灵会话消息处理, 不需要，可以下线
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@MpsHandler(product = "yanxuan-intelli-bot", topic = "intelli_bot_qa_recommend_knowledge")
public class SelfQaContextMsgHandler extends MpsBase<PERSON>andler {
    @Override
    public void process(MpsReceiveMessageBean mpsMessage) {
        log.info("SelfQaContextMsgHandler {}", mpsMessage.getPayload());
    }
}