/**
 * @(#)SessionAnalysisMsgHandler.java, 4/18/23.
 * <p/>
 * Copyright 2023 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service.mps.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.biz.common.mps.MpsBaseHandler;
import com.netease.yx.bot.biz.common.mps.MpsHandler;
import com.netease.yx.bot.biz.common.mps.MpsReceiveMessageBean;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.label.ItemLabelTask;
import com.netease.yx.bot.core.model.mapper.algo.ItemLabelTaskMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 商品详情页 抽取FAQ，数据标注任务
 * 不要删除
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@MpsHandler(product = "yanxuan-data-labeling", topic = "kefu_label_task_item_detail_create")
public class ItemChangeMsgHandler extends MpsBaseHandler {

    @Autowired
    private ItemLabelTaskMapper itemLabelTaskMapper;

    @SneakyThrows
    @Override
    public void process(MpsReceiveMessageBean mpsMessage) {
        log.info("ItemChangeMsgHandler {}", mpsMessage.getPayload());
        ItemLabelTask itemLabelTask = IoUtils.parseJson(mpsMessage.getPayload(), new TypeReference<ItemLabelTask>() {
        });
        if (ObjectUtils.isNotEmpty(itemLabelTask.getItemPreviousLabelTask())) {
            itemLabelTask.setItemPreviousLabelTaskStr(IoUtils.toJsonString(itemLabelTask.getItemPreviousLabelTask()));
        }
        itemLabelTaskMapper.insert(itemLabelTask);
    }
}