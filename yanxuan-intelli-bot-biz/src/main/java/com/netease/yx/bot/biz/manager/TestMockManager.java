package com.netease.yx.bot.biz.manager;

import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.biz.applloconfig.ItemSearchApolloConfig;
import com.netease.yx.bot.biz.service.GuideService;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.constant.PlatformType;
import com.netease.yx.bot.core.model.constant.ReplyState;
import com.netease.yx.bot.core.model.constant.SourceType;
import com.netease.yx.bot.core.model.constant.StatType;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.BotReq;
import com.netease.yx.bot.core.model.entity.BotResp;
import com.netease.yx.bot.core.model.entity.UserOrder;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaAddiReq;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaReq;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaRslt;
import com.netease.yx.bot.core.model.entity.check.CheckKnowledge;
import com.netease.yx.bot.core.model.entity.check.CheckResp;
import com.netease.yx.bot.core.model.entity.check.CheckSimilarKnowledgeReq;
import com.netease.yx.bot.core.model.entity.check.NeedCheckKnowledge;
import com.netease.yx.bot.core.model.entity.faq.FaqIdRslt;
import com.netease.yx.bot.core.model.entity.inputAssociate.InputAssociateReq;
import com.netease.yx.bot.core.model.entity.inputAssociate.InputAssociateResp;
import com.netease.yx.bot.core.model.entity.prophet.ProphetReq;
import com.netease.yx.bot.core.model.entity.search.SearchReq;
import com.netease.yx.bot.core.model.entity.search.SearchResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.netease.yx.bot.core.model.entity.UserOrder.USER_CREDIT_LEVEL_R1;

@Service
@Component
@Slf4j
public class TestMockManager {

    @Autowired
    private EnvService envService;
    @Autowired
    private GuideService guideService;

    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;

    @Autowired
    private ItemSearchApolloConfig itemSearchApolloConfig;


    public BotResp mockPipelineService(BotReq botReq) {
        log.debug("TestMockManager.mockPipelineService, {}", botReq);
        // 测试sop
        if ("测试SOP".equals(botReq.getData())) {
            List<Long> mocksopFaqIds = Arrays.stream("20238461".split(",")).map(Long::parseLong).collect(Collectors.toList());
            FaqIdRslt faqIdRslt = new FaqIdRslt(mocksopFaqIds);
            BotResp botResp = BotResp.buildFaqIdRslt(faqIdRslt, null);
            return botResp;
        }
        // 测试SOP 精确
        if ("测试SOP精准".equals(botReq.getData())) {
            List<Long> mocksopFaqIds = Arrays.stream("20238461".split(",")).map(Long::parseLong).collect(Collectors.toList());
            FaqIdRslt faqIdRslt = new FaqIdRslt(mocksopFaqIds);
            faqIdRslt.setType(FaqIdRslt.TYPE_FAQ_PRECISE);
            BotResp botResp = BotResp.builder().faqRslt(faqIdRslt).statType(StatType.FAQ_UNIQUE).state(ReplyState.SUCCESS).build();
            ;
            return botResp;
        }

        // 测试 精确
        if ("测试精准匹配".equals(botReq.getData())) {
            List<Long> mocksopFaqIds = Arrays.stream("20251395".split(",")).map(Long::parseLong).collect(Collectors.toList());
            FaqIdRslt faqIdRslt = new FaqIdRslt(mocksopFaqIds);
            faqIdRslt.setType(FaqIdRslt.TYPE_FAQ_PRECISE);
            BotResp botResp = BotResp.builder().faqRslt(faqIdRslt).statType(StatType.FAQ_UNIQUE).state(ReplyState.SUCCESS).build();
            ;
            return botResp;
        }

        // 测试faq 模糊匹配
        if ("测试模糊匹配".equals(botReq.getData())) {
            List<Long> mocksopFaqIds = Arrays.stream("20254625,20254626,20254624,20357380".split(",")).map(Long::parseLong).collect(Collectors.toList());
            FaqIdRslt faqIdRslt = new FaqIdRslt(mocksopFaqIds);

            return BotResp.buildFaqIdRslt(faqIdRslt, null);
        }

        if ("测试尺码".equals(botReq.getData())) {
            String sizeURL = "<b>请点击查看如下尺码表，找到适合您的尺码 </b> <HR> <p><img src=\"http://yanxuan.nos.netease.com/ocr_size-4004900_48013345.jpg\" /></p>";
            return BotResp.buildTextRslt(sizeURL, StatType.TEXT_PIC_KBQA, null, false);
        }

        // pro会员0元购提醒
        // 用户测试
        if ((envService.isTest() && "测试0元购".equals(botReq.getData()))) {
            BotResp botResp = BotResp.buildTextRslt("0元购", StatType.TEXT_PIC_KBQA, null, false);
            botResp.genProZeroActiveRslt(guideService.buildProZeroActivity());
            return botResp;
        }

        if ("测试被动推荐".equals(botReq.getData())) {
            BotResp botResp = new BotResp();
            botResp.genGuideRslt(guideService.TEST_MOCK_GUIDE_RES);
            botResp.setGuideRcmdReason("小选为您精心挑选~");
            return botResp;
        }

        if ("测试多商品的被动推荐".equals(botReq.getData())) {
            BotResp botResp = new BotResp();
            botResp.genGuideRslt(GuideService.MOCK_GUIDE_MULTI_ITEM_RES);
            botResp.setGuideRcmdReason("小选为您精心挑选~");
            return botResp;
        }

        return null;
    }

    public List<ChannelQaRslt> mockMultiChannelPipelineLog(ChannelQaReq channelQaReq) {

        List<ChannelQaRslt> channelQaRslts = new ArrayList<>();
        Map<String, List<ChannelQaRslt>> channelQaRsltMap = channelQaApolloConfig.getChannelQaRsltMapV3();
        log.info(IoUtils.toJsonString(channelQaApolloConfig.getChannelQaRsltMapV3()));
        if (MapUtils.isNotEmpty(channelQaRsltMap)) {
            for (Map.Entry<String, List<ChannelQaRslt>> entry : channelQaRsltMap.entrySet()) {
                // 处理多条件
                String key = entry.getKey();
                boolean flag = true;
                for (String split : key.split("-")) {
                    if (!StringUtils.contains(channelQaReq.getRawMsg(), split)) {
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    for (ChannelQaRslt channelQaRslt : entry.getValue()) {
                        if (channelQaRslt.getKnowledgeId() <= 0) {
                            continue;
                        }
                        // 适配一下格式
                        if (channelQaRslt.getKnowledgeSource() != 0) {
                            channelQaRslt.setKnowledgeType(channelQaRslt.getKnowledgeSource() - 1);
                        }
                        channelQaRslts.add(channelQaRslt);
                    }
                    break;
                }
            }
        }
        return channelQaRslts;
    }

    public ChannelQaAddiReq mockChannelQaAddiReq(ChannelQaReq channelQaReq) {
        Map<String, List<ChannelQaRslt>> channelQaRsltMap = channelQaApolloConfig.getChannelQaRsltMapV3();
        log.info("mockChannelQaAddiReq {}", channelQaRsltMap);
        ChannelQaAddiReq channelQaAddiReq = new ChannelQaAddiReq();
        channelQaAddiReq.setSessionId(channelQaReq.getSessionId());
        channelQaAddiReq.setChannelId(channelQaReq.getChannelId());
        if (MapUtils.isNotEmpty(channelQaRsltMap)) {
            for (Map.Entry<String, List<ChannelQaRslt>> entry : channelQaRsltMap.entrySet()) {
                if (StringUtils.contains(channelQaReq.getMessageContent().getText(), entry.getKey())) {
                    for (ChannelQaRslt channelQaRslt : entry.getValue()) {
                        if (channelQaRslt.getItemId() != 0) {
                            channelQaAddiReq.setItemId(channelQaRslt.getItemId());
                        }
                        if (StringUtils.isNotEmpty(channelQaRslt.getItemPhyCategoryIdStr())) {
                            channelQaAddiReq.setItemPhyCategoryIdStr(channelQaRslt.getItemPhyCategoryIdStr());
                        }
                        if (StringUtils.isNotEmpty(channelQaRslt.getPlatformItemId())) {
                            channelQaAddiReq.setPlatformItemId(channelQaRslt.getPlatformItemId());
                        }
                        if (StringUtils.isNotEmpty(channelQaRslt.getPlatformRawItemCardId())) {
                            channelQaAddiReq.setPlatformRawItemCardId(channelQaRslt.getPlatformRawItemCardId());
                        }
                        if (channelQaAddiReq.getItemId() != 0) {
                            break;
                        }
                    }
                }
            }
        }
        return channelQaAddiReq;
    }

    public CheckResp mockCheckSimilarKnowledgeResp(CheckSimilarKnowledgeReq checkSimilarKnowledgeReq) {
        Map<Long, List<CheckKnowledge>> checkMap = new HashMap<>();

        for (Map.Entry<Long, NeedCheckKnowledge> entry : checkSimilarKnowledgeReq.getNeedCheckKnowledgeMap().entrySet()) {
            Map<Integer, List<CheckKnowledge>> checkKnowledgeMap = channelQaApolloConfig.getMockCheckKnowledgeMap();
            if (MapUtils.isNotEmpty(checkKnowledgeMap)) {
                if (checkKnowledgeMap.containsKey(entry.getValue().getKnowledgeUse())) {
                    checkMap.put(entry.getKey(), checkKnowledgeMap.get(entry.getValue().getKnowledgeUse()));
                }
            }
        }
        return new CheckResp(checkMap);
    }

    // 后端要求，测试环境也通线上数据，所以只能通过smartwork进行一次中转
    public SearchResp mockItemSearch(SearchReq searchReq) {
        SearchResp searchResp = new SearchResp(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(itemSearchApolloConfig.getMockSearchItemInfo())) {
            searchResp.getItemInfos().addAll(0, itemSearchApolloConfig.getMockSearchItemInfo());
        }
        return searchResp;
    }

    public InputAssociateResp mockInputAssociate(InputAssociateReq inputAssociateReq) {
        List<String> result = new ArrayList<>();
        result.add("测试输入联想mock 1");
        result.add("测试输入联想mock 2");
        result.add("多答案测试111");
        return new InputAssociateResp(result);
    }


    public BotContext testMockBotContext(ProphetReq req) {
        BotContext botContext = BotContext.builder().userId(req.getUserId()).sessionId(req.getSessionId())
                .platformType(PlatformType.get(req.getPlatform())).sourceType(SourceType.get(req.getSource())).build();

        switch ((int) req.getUserId()) {
            case 50041000:
            case 50108407:
            case 50100103:
            case 50108409:
            case 50108410:
            case 50108412:
            case 50108413:
            case 50108414:

            case 10609000:
//                botContext.setPreSale(true);
            case 50108408:
            case 2402000:
                botContext.setPreSale(false);
                botContext.setUserV(1);
                break;
            case 10050001:
                botContext.setPreSale(true);
                botContext.setNewUser(true);
                break;
            case 2402001:
                botContext.setUserV(2);
                break;
            case 2470002:
                botContext.setUserV(3);
                break;
            case 2470003:
                botContext.setUserV(4);
                break;
            case 2470004:
                botContext.setUserV(5);
                break;
            case 1667001:
                botContext.setUserV(6);
                break;
            default:
        }
        return botContext;
    }

    public UserOrder testMockUserOrder(ProphetReq req) {
        UserOrder userOrder = UserOrder.builder().build();
        switch ((int) req.getUserId()) {
            case 50041000:
                userOrder.setTicketStatus(1);
                break;
            case 50108407:
                userOrder.setOrderStatus(1);
                userOrder.setApplyTypeCode(2);
                userOrder.setApplyStatus(11);
                break;
            case 50100103:
                userOrder.setApplyTypeCode(2);
                userOrder.setApplyStatus(2);
                userOrder.setSpmcStatus(3);
                break;
            case 50108409:
                userOrder.setApplyTypeCode(1);
                userOrder.setApplyStatus(1);
                userOrder.setSpmcStatus(3);
                break;
            case 50108410:
                userOrder.setUserCreditLevel("r5");
                break;
            case 50108412:
                userOrder.setTrackingStatus(7);
                userOrder.setApplyTypeCode(5);
                userOrder.setApplyStatus(5);
                break;

            case 50108413:
                userOrder.setApplyTypeCode(4);
                userOrder.setApplyStatus(8);
                break;
            case 50108414:
                userOrder.setOrderStatus(1);
                userOrder.setTrackingStatus(7);
                break;
            case 2402001:
                userOrder.setOrderStatus(1);
                break;
            case 2470002:
                userOrder.setTicketStatus(1);
                break;
            case 2470003:
                userOrder.setUserCreditLevel(USER_CREDIT_LEVEL_R1);
                break;
            case 2470004:
                userOrder.setTrackingStatus(7);
                break;
            case 10609000:
            case 50108408:

            default:

        }
        if (req.getUserId() == 40014333327L) {
            userOrder.setApplyTypeCode(3);
            userOrder.setApplyStatus(3);
        }
        return userOrder;
    }
}
