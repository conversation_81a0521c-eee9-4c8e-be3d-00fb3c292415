/**
 * @(#)FaqApolloConfig.java, 2020/3/16.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.applloconfig;

import com.ctrip.framework.apollo.spring.annotation.EnableAutoUpdateApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ValueMapping;
import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.prophet.ProphetDefaultConfig;
import com.netease.yx.bot.core.model.entity.rgVisible.SmartRGVisibleRule;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Component
@EnableAutoUpdateApolloConfig("application")
@Slf4j
@Getter
public class BotApolloConfig {
    private static final String ODD = "x";
    private static final String SPECIAL = "y";
    private static final String ALL = "*";

    private static final int ENABLED = 1;
    private static final double MIN_THRESHOLD = 0.8;
    private static final double MAX_THRESHOLD = 0.94;

    @ValueMapping("${prophet.default.json:{}}")
    private String prophetDefaultJson;
    @ValueMapping("${prophet.source.default.json:{}}")
    private String prophetSourceDefaultJson;
    @ValueMapping("${prophet.bottom.default.json:{}}")
    private String prophetBottomDefaultJson;
    @ValueMapping("${prophet.top.shortcut.list:20143185}")
    private String prophetTopShortcutList;

    @ValueMapping("${prophet.top.shortcut.list.v2:[20143520,20143511]}")
    private List<Long> prophetTopShortcutListV2;
    @ValueMapping("${prophet.newflag.faq.list:20183648,20183481}")
    private String prophetNewFlagFaqList;
    @ValueMapping("${prophet.newflag.shortcut.list:20143089}")
    private String prophetNewFlagShortcutList;
    @ValueMapping("${abt.white.userName.list:<EMAIL>}")
    private String whiteUserNameList;
    @ValueMapping("${abt.sessionId.suffix:y}")
    private String abtSessionIdSuffix;
    /**
     * 意图
     */
    @ValueMapping("${intent.core.knowledge.map:{}}")
    private String intentCoreKnowledgeMapJson;
    @ValueMapping("${intent.core.ignore:[]}")
    private List<String> intentCoreIgnoreList;
    @ValueMapping("${shortcut.defaultList:20143093,20143107,20143159,20143089}")
    private String shortcutDefaultList;
    @ValueMapping("${shortcut.topList:[20143520,20143511]}")
    private List<Long> shortcutTopList;
    @ValueMapping("${faqid.filter.item.rule:{}}")
    private String faqIdFilterItemRuleJson;
    /**
     * 导购V2
     */
    @ValueMapping("${guide.enable:0}")
    private int guideUse;
    @ValueMapping("${abt.guide.sessionid.suffix:y}")
    private String abtGuideSidSuffix;
    @ValueMapping("${guide.aftersale.enable:0}")
    private int guideAftersaleUse;
    @ValueMapping("${guide.active.enable:0}")
    private int guideActiveUse;
    @ValueMapping("${abt.guide.active.sessionid.suffix:y}")
    private String abtGuideActiveSidSuffix;
    @ValueMapping("${guide.active.rule.turn.count:-1}")
    private int guideActiveRuleTurnCount;
    @ValueMapping("${guide.active.rule.precision.enable:0}")
    private int guideActiveRulePrecision;
    @ValueMapping("${guide.active.rmcd.count:1}")
    private int guideActiveRcmdCount;
    @ValueMapping("${guide.pro.zero.activity.enable:0}")
    private int guideProZeroActivityEnable;
    @ValueMapping("${guide.pro.zero.activity.interval:0,3}")
    private String guideProZeroActivityInterval;
    @ValueMapping("${guide.pro.rcmd.text:尊贵的pro会员，小选提醒您别忘记领取本月的0元领福利~自营仓商品满99元还能免邮费哦~}")
    private String guideProZeroActivityRCMDText;
    @ValueMapping("${guide.active.rmcd.item.max:3}")
    private int guideRcmdItemMax;
    /**
     * 关键词
     */
    @ValueMapping("${faq.keyword:{}}")
    private String faqKeywordStringJSON;
    /**
     * 新人置顶
     */
    @ValueMapping("${shortcut.NewFlag.topList:20143181}")
    private String shortcutNewFlagTopList;
    /**
     * 会员置顶
     */
    @ValueMapping("${shortcut.vip.topList:20143120}")
    private String shortcutVipTopList;
    @ValueMapping("${prophet.user.stat.json:{}}")
    private String prophetUserStatJson;
    @ValueMapping("${prophet.user.stat.flag:0}")
    private int prophetUserStatFlag;
    @ValueMapping("${intent.greeting.list:你好,您好,在吗,客服在吗,有人吗,亲}")
    private String intentGreetingList;
    /**
     * 主动预测根据用户状态推荐ABT设置
     */
    @ValueMapping("${abt.prophet.user.stat.sessionId.suffix:y}")
    private String abtProphetUserStatSessionIdSuffix;

    @ValueMapping("${abt.intent.sessionId.suffix:y}")
    private String abtIntentSessionIdSuffix;
    /**
     * 匹配模型
     */
    @ValueMapping("${abt.match.sessionId.suffix:y}")
    private String abtMatchSessionIdSuffix;
    @ValueMapping("${context.match.enable:0}")
    private int contextMatchEnable;
    @ValueMapping("${context.human.match.enable:0}")
    private int contextHumanMatchEnable;
    /**
     * 订单推荐问题配置
     */
    @ValueMapping("${order.rcmd.default.faq:20181435,20168648,20168693,20168682,20168643}")
    private String orderRcmdDefaultFAQs;
    /**
     * 智能人工按钮
     */
    @ValueMapping("${smart.rg.visible.presale.rule:{}}")
    private String smartRGVisiblePresaleRuleJSON;
    @ValueMapping("${smart.rg.visible.aftersale.rule:{}}")
    private String smartRGVisibleAftersaleRuleJSON;
    @ValueMapping("${smart.rg.visible.threshold:1.5,1.7}")
    private String smartRGVisibleThreshold;
    @ValueMapping("${smart.rg.visible.enable:0}")
    private int smartRGVisibleEnable;
    @ValueMapping("${smart.rg.visible.session.count:-10}")
    private float smartRGVisibleSessionCount;
    @ValueMapping("${smart.rg.visible.session.coefficient:0.5}")
    private float smartRGVisibleSessionCoefficient;
    @ValueMapping("${smart.rg.visible.kfgroup.map:264307589}")
    private String smartRGVisibleSessionKFGroupIdsString;
    @ValueMapping("${knowledge.faq.pic.template:<b>请点击查看如下尺码表，找到适合您的尺码 </b> <HR> <p><img src=\"%s\" /></p>}")
    private String pickUrlTemplate;
    /**
     * 在通用入口里面要忽略的商品知识id
     */
    @ValueMapping("${faq.item.ignore.list:[]}")
    private List<Long> faqIgnoreList;
    @ValueMapping("${abt.faq.session.suffix:4}")
    private String abtFaqSessionIdSuffix;

    @ValueMapping("${match.exact.threshold:0.91}")
    private double matchExactThreshold;

    @ValueMapping("${abt.match.exact.threshold:0.85}")
    private double abtMatchExactThreshold;

    @ValueMapping("${abt.white.userId.list:26220394605,17946037}")
    private String whiteUserIdList;

    /**
     * 人工侧辅助
     */
    @ValueMapping("${rg.assist.rcmd.items.default:[]}")
    private String rgAssistRcmdItemIdsStr;

    @ValueMapping("${rg.assist.rcmd.items.reason.default:[]}")
    private String rgAssistRcmdItemIdReasonStr;

    @ValueMapping("${faq2ChatThreshold:0.75}")
    private double faq2ChatThreshold;

    public static void main(String[] args) {
        System.out.println(102 % 10);
    }

    public double getMatchExactThreshold() {
        return Math.min(MAX_THRESHOLD, Math.max(matchExactThreshold, MIN_THRESHOLD));
    }

    public double getAbtMatchExactThreshold() {
        return Math.min(MAX_THRESHOLD, Math.max(abtMatchExactThreshold, MIN_THRESHOLD));
    }

    public boolean checkAbt(String sessionId, String config) {
        try {
            if (sessionId != null && sessionId.length() > 1) {
                String suffix = sessionId.substring(sessionId.length() - 1);

                switch (config) {
                    case ODD:
                        if (StringUtils.isNumeric(suffix)) {
                            int id = Integer.parseInt(suffix);
                            return id % 2 == 0;
                        } else {
                            return false;
                        }
                    case SPECIAL:
                        return SPECIAL.equals(suffix);
                    case ALL:
                        return true;
                    default:
                        return StringUtils.isNumeric(suffix) && config.equals(suffix);
                }
            }
        } catch (Exception e) {
            log.error("checkAbt", e);
        }

        return false;
    }

    public List<String> getProZeroActivityRcmdText() {
        List<String> words = new ArrayList<>();
        if (!StringUtils.isEmpty(guideProZeroActivityRCMDText)) {
            words.addAll(Arrays.asList(guideProZeroActivityRCMDText.split(",")));
        }
        return words;
    }

    public boolean checkProZeroActivityIntervalEnable() {
        List<String> proZeroActicityIntervalConfig = new ArrayList<>();
        if (!StringUtils.isEmpty(guideProZeroActivityInterval)) {
            proZeroActicityIntervalConfig.addAll(Arrays.asList(guideProZeroActivityInterval.split(",")));

            return Integer.parseInt(proZeroActicityIntervalConfig.get(0)) == ENABLED;
        }

        return false;
    }

    public int getProZeroActivityIntervalDays() {
        List<String> proZeroActicityIntervalConfig = new ArrayList<>();
        if (!StringUtils.isEmpty(guideProZeroActivityInterval)) {
            proZeroActicityIntervalConfig.addAll(Arrays.asList(guideProZeroActivityInterval.split(",")));

            if (proZeroActicityIntervalConfig.size() == 2) {
                return Integer.parseInt(proZeroActicityIntervalConfig.get(1));
            }

        }

        return 3;
    }

    public Map<String, List<Long>> getProphetItemDefaultConfig() {
        Map<String, List<Long>> prophetDefaultConfigMap = new HashMap<>();
        try {
            Map<String, List<Long>> configMap = IoUtils.parseJson(prophetDefaultJson, new TypeReference<Map<String, List<Long>>>() {
            });
            prophetDefaultConfigMap.putAll(configMap);
        } catch (Exception e) {
            log.error("getProphetDefaultConfig {}", prophetDefaultJson);
        }
        return prophetDefaultConfigMap;
    }

    public List<Long> getRGAssistItemIdDefault() {
        List<Long> rgAssistItemDefault = new ArrayList<>();
        try {
            rgAssistItemDefault = IoUtils.parseJson(rgAssistRcmdItemIdsStr, new TypeReference<List<Long>>() {
            });
        } catch (Exception e) {
            log.error("getRGAssistItemDefault {}", rgAssistRcmdItemIdsStr);
        }

        return rgAssistItemDefault;
    }

    public List<String> getRGAssistItemReasonDefault() {
        List<String> rgAssistItemReasonDefault = new ArrayList<>();
        try {
            rgAssistItemReasonDefault = IoUtils.parseJson(rgAssistRcmdItemIdReasonStr, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.error("getRGAssistItemDefault {}", rgAssistItemReasonDefault);
        }
        return rgAssistItemReasonDefault;
    }

    public List<Long> getProphetNewFlagFaqListConfig() {
        List<Long> ids = new ArrayList<>();
        try {
            if (!StringUtils.isEmpty(prophetNewFlagFaqList)) {
                ids.addAll(Arrays.stream(prophetNewFlagFaqList.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("getProphetNewFlagFaqListConfig {}", prophetNewFlagFaqList);
        }

        return ids;
    }

    public List<Long> getSmartRGVisibleKFGroupIds() {
        List<Long> ids = new ArrayList<>();
        try {
            if (!StringUtils.isEmpty(smartRGVisibleSessionKFGroupIdsString)) {
                ids.addAll(Arrays.stream(smartRGVisibleSessionKFGroupIdsString.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("getSmartRGVisibleKFGroupIds {}", prophetNewFlagFaqList);
        }

        return ids;
    }

    public List<Long> getProphetNewFlagShortcutListConfig() {
        List<Long> ids = new ArrayList<>();
        try {
            if (!StringUtils.isEmpty(prophetNewFlagShortcutList)) {
                ids.addAll(Arrays.stream(prophetNewFlagShortcutList.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("getProphetNewFlagShortcutListConfig {}", prophetNewFlagShortcutList);
        }

        return ids;
    }

    public List<Long> getProphetTopShortcutListConfig() {
        List<Long> ids = new ArrayList<>();
        try {
            if (!StringUtils.isEmpty(prophetTopShortcutList)) {
                ids.addAll(Arrays.stream(prophetTopShortcutList.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("getProphetTopShortcutListConfig {}", prophetTopShortcutList);
        }

        return ids;
    }

    public List<String> getWhiteUserIdList() {
        if (!StringUtils.isEmpty(whiteUserIdList)) {
            return Arrays.asList(whiteUserIdList.split(","));
        } else {
            return new ArrayList<>();
        }
    }

    public List<Long> getOrderRcmdFaqIdList() {
        return parseLongFromStr(orderRcmdDefaultFAQs);
    }

    public Map<String, Long> getIntentCoreKnowledgeMap() {
        Map<String, Long> map = new HashMap<>(100);
        if (intentCoreKnowledgeMapJson != null && intentCoreKnowledgeMapJson.length() > 2) {
            Map<String, Long> apolloMap = null;
            try {
                apolloMap = IoUtils.parseJson(intentCoreKnowledgeMapJson, new TypeReference<Map<String, Long>>() {
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (apolloMap != null) {
                map.putAll(apolloMap);
            }
        }
        return map;
    }

    public List<Long> getDefaultShortcutList() {
        return parseLongFromStr(shortcutDefaultList);
    }

    public List<Long> getNewFlagTopShortcutList() {
        return parseLongFromStr(shortcutNewFlagTopList);
    }

    public List<Long> getVipTopShortcutList() {
        return parseLongFromStr(shortcutVipTopList);
    }

    private List<Long> parseLongFromStr(String config) {
        List<Long> result = new ArrayList<>();
        try {
            if (!StringUtils.isEmpty(config)) {
                result.addAll(Arrays.stream(config.split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("parseLongFromStr", e);
        }
        return result;
    }

    public boolean useProphetUserState() {
        return prophetUserStatFlag == ENABLED;
    }

    public Map<String, ProphetDefaultConfig> getSourceDefaultConfig() {
        Map<String, ProphetDefaultConfig> config = new HashMap<>();
        if (prophetSourceDefaultJson != null && prophetSourceDefaultJson.length() > 2) {
            Map<String, ProphetDefaultConfig> list = null;
            try {
                list = IoUtils.parseJson(prophetSourceDefaultJson, new TypeReference<Map<String, ProphetDefaultConfig>>() {
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (list != null && list.size() > 0) {
                for (Map.Entry<String, ProphetDefaultConfig> entry : list.entrySet()) {
                    ProphetDefaultConfig prophetDefaultConfig = entry.getValue();
                    if (prophetDefaultConfig != null && !CollectionUtils.isEmpty(prophetDefaultConfig.getKnowledgeIds())
                            && !CollectionUtils.isEmpty(prophetDefaultConfig.getShortcutIds())
                            && !CollectionUtils.isEmpty(prophetDefaultConfig.getCardIds())) {
                        config.put(entry.getKey(), entry.getValue());
                    }
                }
                config.putAll(list);
            }
        }
        return config;
    }

    public ProphetDefaultConfig getBottomDefaultConfig() {
        ProphetDefaultConfig config = null;
        if (prophetBottomDefaultJson != null && prophetBottomDefaultJson.length() > 2) {
            ProphetDefaultConfig bottomConfig = null;
            try {
                bottomConfig = IoUtils.parseJson(prophetBottomDefaultJson, new TypeReference<ProphetDefaultConfig>() {
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (bottomConfig != null && !CollectionUtils.isEmpty(bottomConfig.getKnowledgeIds())
                    && !CollectionUtils.isEmpty(bottomConfig.getShortcutIds())
                    && !CollectionUtils.isEmpty(bottomConfig.getCardIds())) {
                config = bottomConfig;
            }
        }
        return config;
    }

    public boolean isGuideEnable() {
        return guideUse == ENABLED;
    }

    public boolean isGuideActiveEnable() {
        return guideActiveUse == ENABLED;
    }

    public boolean isGuideActiveRulePrecisionEnable() {
        return guideActiveRulePrecision == ENABLED;
    }

    public boolean isGuideActiveRuleRcmdCountEnable(int rcmdCount) {
        return rcmdCount < guideActiveRcmdCount;
    }

    public boolean isGuideProZeroActivityEnable() {
        return guideProZeroActivityEnable == ENABLED;
    }

    public boolean isGuideAftersaleEnable() {
        return guideAftersaleUse == ENABLED;
    }

    public boolean isGuideActiveTurnCount(int turnCount) {
        if (guideActiveRuleTurnCount == -1) {
            return false;
        }
        return (turnCount >= guideActiveRuleTurnCount);
    }

    public SmartRGVisibleRule getSmartRGVisibleRule(boolean isPresale) {
        SmartRGVisibleRule smartRGVisibleRule = null;
        if (smartRGVisiblePresaleRuleJSON != null && smartRGVisiblePresaleRuleJSON.length() > 2) {
            if (isPresale) {
                try {
                    smartRGVisibleRule = IoUtils.parseJson(smartRGVisiblePresaleRuleJSON, new TypeReference<SmartRGVisibleRule>() {
                    });
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                try {
                    smartRGVisibleRule = IoUtils.parseJson(smartRGVisibleAftersaleRuleJSON, new TypeReference<SmartRGVisibleRule>() {
                    });
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return smartRGVisibleRule;
    }

    public List<Float> getSmartRGVisibleThreshold() {
        List<Float> threshold = new ArrayList<>();
        if (smartRGVisibleThreshold != null) {
            threshold.addAll(Arrays.stream(smartRGVisibleThreshold.split(",")).map(Float::parseFloat).collect(Collectors.toList()));
        }
        return threshold;
    }

    public boolean isSmartRGVisibleEnable() {
        return smartRGVisibleEnable == ENABLED;
    }
}