package com.netease.yx.bot.biz.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.preprocess.Clean;
import com.netease.yx.bot.common.util.preprocess.berttokenizer.FullTokenizer;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.CoreIntent;
import com.netease.yx.bot.core.model.entity.intentProduct.ClassifyHierarchyTFResp;
import com.netease.yx.bot.core.model.entity.intentProduct.FewShotTFResp;
import com.netease.yx.bot.core.model.entity.intentProduct.TFInputDataReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IntentProductService {

    private static final String HEADER_KEY_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_VALUE_CONTENT_TYPE = "application/json";
    private static final String HEADER_KEY_HOST_TYPE = "host";
    private static final String HEADER_VALUE_HOST_CLASSIFY_TYPE = "aibot-intent-classify-v2.yx-serving.svc";
    private static final String HEADER_VALUE_HOST_FEWSHOT_TYPE = "aibot-intent-few-shot-v2.yx-serving.svc";
    private static final String CLASSIFY_SMARTWORK_URL = "http://smart-infer.hz.infra.mail:31938/v1/models/aibot-intent-classify-v2:predict";
    private static final String FEWSHOT_SMARTWORK_URL = "http://smart-infer.hz.infra.mail:31938/v1/models/aibot-intent-few-shot-v2:predict";
    private static final Integer MAX_LENGTH = 32;
    private static final String REQUEST_OBJ_KEY = "instances";
    private static final String CONFIDENCE_STRONG = "strong";
    private static final String CONFIDENCE_MEDIAN = "median";
    private static final String INTENT_OTHER = "other";

    private static final String PARAM_SEN_KEY = "sen";
    private static final String PARAM_VERSION = "version";
    private static final String PARAM_VALUE_A = "A";
    private static final String PARAM_VALUE_B = "B";

    private static final CoreIntent OTHER_RESPONSE_DATA = CoreIntent.buildDefault();
    private static final int SEPARATOR_TOKEN_ID = 102;
    private static final int START_TOKEN_ID = 101;
    private static final long TIME_OUT = 200;
    private Map<String, String> ChatIntentZHMap;
    @Autowired
    private ExecutorService executor;
    @Autowired
    private BotApolloConfig botApolloConfig;

    @Value("${intent.bert.vocab}")
    private String bertVocabPath;

    @Value("${chat.intent.zh.map.path}")
    private String chatIntentZHMapPath;

    private FullTokenizer tokenizer;

    @PostConstruct
    private void init() {
        try {
            tokenizer = new FullTokenizer(bertVocabPath);
            ChatIntentZHMap = IoUtils.loadJsonFromFile(chatIntentZHMapPath, new TypeReference<Map<String, String>>() {
            });
        } catch (Exception e) {
            log.error("load TextProcessor4BertMatch error", e);
        }
    }

    private String requestSmartwork(String requestContent, Map<String, String> header, String url) {
        String result = "";

        try {
            log.info("request url: " + url + " request content: " + requestContent + " request header: " + IoUtils.toJsonString(header));
            result = HttpUtils.executePost(url, requestContent, null, header);
        } catch (Exception e) {
            log.error("error in IntentProductManager: smartwork request error", e);

        }

        return result;
    }

    public String genRequestInput(String text) {
        text = clean(text);
        Map<String, List<TFInputDataReq>> reqObj = new HashMap<>(1);
        TFInputDataReq tfInputDataReq = tokenizer(text);
        reqObj.put(REQUEST_OBJ_KEY, new ArrayList<>(Collections.nCopies(1, tfInputDataReq)));
        String input = IoUtils.toJsonString(reqObj);
        log.info("IntentProduct request: " + input);
        return input;
    }

    public List<CoreIntent> getIntentProduct(String text) {
        List<CoreIntent> result = new ArrayList<>();

        String input = genRequestInput(text);
        if ("".equals(input)) {
            return result;
        }

        ClassifyHierarchyTFResp classifyHierarchyTFResp = new ClassifyHierarchyTFResp();
        FewShotTFResp fewShotTFResp = new FewShotTFResp();

        try {
            CompletableFuture<ClassifyHierarchyTFResp> classifyCompletableFuture;
            classifyCompletableFuture = CompletableFuture.supplyAsync(() -> getClassifyTFRes(input), executor);
            CompletableFuture<FewShotTFResp> fewshotCompletableFuture;
            fewshotCompletableFuture = CompletableFuture.supplyAsync(() -> getFewShotTFRes(input), executor);

            CompletableFuture.allOf(classifyCompletableFuture, fewshotCompletableFuture);

            try {
                classifyHierarchyTFResp = classifyCompletableFuture.get(TIME_OUT, TimeUnit.MILLISECONDS);
                fewShotTFResp = fewshotCompletableFuture.get(TIME_OUT, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.error("multi thread ", e);
            }

            result = mergeResult(classifyHierarchyTFResp, fewShotTFResp);
        } catch (Exception e) {
            log.error("error in IntentProductManager", e);
        }

        return result;

    }

    public ClassifyHierarchyTFResp getClassifyTFRes(String reqObj) {
        Map<String, String> classifyHeaders = buildClassifyHeaders();
        String clssifyServiceRes = requestSmartwork(reqObj, classifyHeaders, CLASSIFY_SMARTWORK_URL);
        log.info("IntentProduct classify response: " + clssifyServiceRes);
        ClassifyHierarchyTFResp classifyHierarchyTFResp = new ClassifyHierarchyTFResp();
        try {
            classifyHierarchyTFResp = IoUtils.parseJson(clssifyServiceRes, ClassifyHierarchyTFResp.class);
        } catch (Exception e) {
            log.error("error in IntentProductManager", e);
        }
        return classifyHierarchyTFResp;
    }

    public FewShotTFResp getFewShotTFRes(String reqObj) {
        Map<String, String> fewshotHeader = buildFewshotHeaders();
        String fewshotServiceRes = requestSmartwork(reqObj, fewshotHeader, FEWSHOT_SMARTWORK_URL);
        log.info("IntentProduct few shot response: " + fewshotServiceRes);
        FewShotTFResp fewShotTFResp = new FewShotTFResp();
        try {
            fewShotTFResp = IoUtils.parseJson(fewshotServiceRes, FewShotTFResp.class);
        } catch (Exception e) {
            log.error("error in IntentProductManager", e);
        }
        return fewShotTFResp;
    }

    private List<CoreIntent> mergeResult(ClassifyHierarchyTFResp classifyHierarchyTFResp, FewShotTFResp fewShotTFResp) {
//        若小样本存在多个，则内部降级
        fewShotTFResp.sort();
        fewShotTFResp.resetConf();

        List<CoreIntent> result = new ArrayList<>();
        Integer idx = 1;
        if (INTENT_OTHER.equals(classifyHierarchyTFResp.getIntent()) && fewShotTFResp.getIntentList().get(0).equals(INTENT_OTHER)) {
            result.add(OTHER_RESPONSE_DATA);
        } else if (INTENT_OTHER.equals(classifyHierarchyTFResp.getIntent())) {
//            若分类模型结果为other，小样本模型结果降级处理
            for (int i = 0; i < fewShotTFResp.getRecallNums(); i++) {
                String confidence = degradeConf(fewShotTFResp.getConfidenceList().get(i));
                result.add(new CoreIntent(idx, fewShotTFResp.getIntentCHList().get(i),
                        fewShotTFResp.getScoreList().get(i),
                        fewShotTFResp.getIntentList().get(i), confidence, "", -1
                ));
                idx++;
            }
        } else if (INTENT_OTHER.equals(fewShotTFResp.getIntentList().get(0))) {
//            若小样本模型结果为other， 分类模型结果降级处理
            String confidence = degradeConf(classifyHierarchyTFResp.getConfidence());
            result.add(new CoreIntent(idx, classifyHierarchyTFResp.getIntentCH(),
                    classifyHierarchyTFResp.getScore(),
                    classifyHierarchyTFResp.getIntent(), confidence, "", -1
            ));
        } else {

            List<CoreIntent> degradeRes = new ArrayList<>();

            for (int i = 0; i < fewShotTFResp.getRecallNums(); i++) {
                String intent = fewShotTFResp.getIntentList().get(i);
                if (intent.equals(classifyHierarchyTFResp.getIntent())) {
//                    若分类结果和小样本结果相同，升级处理，并返回唯一strong
                    result.add(new CoreIntent(idx, classifyHierarchyTFResp.getIntentCH(),
                            classifyHierarchyTFResp.getScore(),
                            classifyHierarchyTFResp.getIntent(),
                            CONFIDENCE_STRONG, "", 0));
                    return result;
                } else {
                    degradeRes.add(new CoreIntent(idx, fewShotTFResp.getIntentCHList().get(i),
                            fewShotTFResp.getScoreList().get(i),
                            intent,
                            fewShotTFResp.getConfidenceList().get(i), "", 0));
                    idx++;
                }
            }

            result.add(new CoreIntent(idx, classifyHierarchyTFResp.getIntentCH(),
                    classifyHierarchyTFResp.getScore(),
                    classifyHierarchyTFResp.getIntent(),
                    CONFIDENCE_MEDIAN, "", 0));
            if (degradeRes.size() != 0) {
                result.addAll(degradeRes);
            }
            if (result.size() > 3) {
                result = result.subList(0, 3);
            }
        }
        return result;
    }

    public List<CoreIntent> getCoreIntent(String sentence, long sessionId) {
        return getCoreIntentByVersion(sentence);
    }

    public List<CoreIntent> getCoreIntentByVersion(String sentence) {
        List<CoreIntent> coreIntents = new ArrayList<>();
        if (StringUtils.isEmpty(sentence)) {
            return coreIntents;
        }

        coreIntents = getIntentProduct(sentence);
        log.info("coreIntents: {}", coreIntents);
        log.info("chatintentzhe: {}", ChatIntentZHMap);
        // 过滤闲聊分类结果
        coreIntents = coreIntents.stream().filter(x -> !ChatIntentZHMap.containsKey(x.getIntentCh())).collect(Collectors.toList());
        if (coreIntents.size() == 0) {
            coreIntents.add(CoreIntent.buildDefault());
        }
        return coreIntents;
    }

    private String degradeConf(String confidence) {
        if (confidence.equals(CONFIDENCE_STRONG)) {
            return CONFIDENCE_MEDIAN;
        }
        return confidence;
    }


    public TFInputDataReq tokenizer(String text) {
        String[] tokens = tokenizer.tokenize(text);
        int[] ids = tokenizer.convert(tokens);
        int length = 2;
        List<Integer> inputIds = new ArrayList<>(Collections.nCopies(MAX_LENGTH, 0));
        inputIds.set(0, START_TOKEN_ID);
        for (int j = 0; j < ids.length && j < MAX_LENGTH - 2; j++) {
            inputIds.set(j + 1, ids[j]);
            length++;
        }

        inputIds.set(length - 1, SEPARATOR_TOKEN_ID);
        return new TFInputDataReq(inputIds, length);
    }


    public String clean(String text) {
        text = StringUtils.strip(text);
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        text = Clean.toSimple(text);
        text = Clean.toDBC(text);
        text = Clean.removeEmoji(text);
        text = Clean.removePunc(text);
        text = Clean.replaceDigit(text);
        return text;
    }


    private Map<String, String> buildClassifyHeaders() {
        Map<String, String> headers = new HashMap<>(2);
        headers.put(HEADER_KEY_CONTENT_TYPE, HEADER_VALUE_CONTENT_TYPE);
        headers.put(HEADER_KEY_HOST_TYPE, HEADER_VALUE_HOST_CLASSIFY_TYPE);
        return headers;
    }

    private Map<String, String> buildFewshotHeaders() {
        Map<String, String> headers = new HashMap<>(2);
        headers.put(HEADER_KEY_CONTENT_TYPE, HEADER_VALUE_CONTENT_TYPE);
        headers.put(HEADER_KEY_HOST_TYPE, HEADER_VALUE_HOST_FEWSHOT_TYPE);
        return headers;
    }

}
