package com.netease.yx.bot.biz.applloconfig;

import com.ctrip.framework.apollo.spring.annotation.EnableAutoUpdateApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ValueMapping;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 主动预测配置
 */
@Component
@EnableAutoUpdateApolloConfig("prophet")
@Slf4j
@Getter
public class ProphetConfig {
    @ValueMapping("${prophet.default.json:{}}")
    private String prophetDefaultJson;
    @ValueMapping("${prophet.source.default.json:{}}")
    private String prophetSourceDefaultJson;
    @ValueMapping("${prophet.bottom.default.json:{}}")
    private String prophetBottomDefaultJson;
    @ValueMapping("${prophet.top.faq.list:20248512}")
    private String prophetTopFaqList;
    @ValueMapping("${prophet.top.shortcut.list:20143185}")
    private String prophetTopShortcutList;
    @ValueMapping("${prophet.newflag.faq.list:20183648,20183481}")
    private String prophetNewFlagFaqList;
    @ValueMapping("${prophet.newflag.shortcut.list:20143089}")
    private String prophetNewFlagShortcutList;
    @ValueMapping("${abt.prophet.model.rcmd.sessionId.suffix:y}")
    private String abtProphetModelRcmdSessionSuffix;
    @ValueMapping("${prophet.model.rcmd.use:0}")
    private int prophetModelRcmdUseInteger;
}
