/**
 * @(#)ItemManager.java, 2020/9/27.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service;

/**
 * <AUTHOR> @ corp.netease.com)
 */

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Sets;
import com.netease.yanxuan.feast.sdk.vo.FeatureRequest;
import com.netease.yanxuan.feast.sdk.vo.FeatureResult;
import com.netease.yanxuan.icquery.icbasis.SpuQueryService;
import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.common.FeatureStoreService;
import com.netease.yx.bot.common.util.GuavaCacheUtil;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.item.Item;
import com.netease.yx.bot.core.model.entity.item.ItemBaseInfoReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
@EnableScheduling
@Slf4j
public class ItemService {
    private final static String GET_ITEM_SQL = "select item_id, item_name, nick_name, list_pic_url, phy_category1_id,phy_category2_id,phy_category3_id,phy_category4_id from dim.dim_item where phy_category1_id is not null";
    private final static String FEATURE_STORE_DOMAIN = "itemid";
    private final static String FEATURE_STORE_KEY_RCMD_REASON = "rcmd_reason";
    private final static String FEATURE_STORE_KEY_AD_ITEM_NAME = "ad_item_name";
    private final static String FEATURE_STORE_KEY_DESC_ITEM_NAME = "desc_name";
    private final static String FEATURE_STORE_KEY_COMBINE_ITEM_NAME = "combine_desc";
    private final static String FEATURE_STORE_KEY_RCMD_REASON_RG = "rcmd_reason_rg";
    private static final Set<String> FEATURE_NAMES = new HashSet<String>() {{
        add(FEATURE_STORE_KEY_RCMD_REASON);
        add(FEATURE_STORE_KEY_DESC_ITEM_NAME);
        add(FEATURE_STORE_KEY_RCMD_REASON_RG);
    }};
    private static final String PHY_CATE1_ID = "phy_category1_id";
    private static final String PHY_CATE2_ID = "phy_category2_id";
    private static final String PHY_CATE3_ID = "phy_category3_id";
    private static final String PHY_CATE4_ID = "phy_category4_id";
    private SpuQueryService spuQueryService;
    private Map<Long, Item> id2ItemMap;
    private Map<String, Item> nickName2ItemMap;

    @Value("${waiterdb.jdbc.url}")
    private String waiterdbJdbcUrl;

    @Value("${item.base.info.url}")
    private String itemBaseInfoUrl;

    @Autowired
    private EnvService envService;

    @Autowired
    private FeatureStoreService featureStoreService;

    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;

    @PostConstruct
    private void init() {
        update();
    }

    @Scheduled(cron = "0 */${schedule.item.interval.minutes} * * * *")
    private void update() {
        log.info("start getItemInfo");
        Map<Long, Item> newId2GoodsItemMap = new HashMap<>(5000);
        Map<String, Item> newNickName2ItemMap = new HashMap<>(5000);
        if (!envService.isTest()) {
            try {
                Class.forName("com.netease.mail.holmes.waiter.jdbc.WaiterDriver");
                Connection conn = DriverManager.getConnection(waiterdbJdbcUrl);

                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(GET_ITEM_SQL);

                //如果有数据，rs.next()返回true
                while (rs.next()) {
                    long itemId = rs.getLong(Item.KEY_ITEM_ID);
                    String itemName = rs.getString(Item.KEY_ITEM_NAME);
                    String nickName = rs.getString(Item.KEY_ITEM_NICK_NAME);
                    String picUrl = rs.getString(Item.KEY_ITEM_PIC_URL);

                    long phyCategory1Id = rs.getLong(Item.KEY_PHY_CATEGORY1_ID);
                    long phyCategory2Id = rs.getLong(Item.KEY_PHY_CATEGORY2_ID);
                    long phyCategory3Id = rs.getLong(Item.KEY_PHY_CATEGORY3_ID);
                    long phyCategory4Id = rs.getLong(Item.KEY_PHY_CATEGORY4_ID);

                    Item item = new Item(itemId, itemName, nickName, picUrl, phyCategory1Id, phyCategory2Id, phyCategory3Id, phyCategory4Id, false);
                    newId2GoodsItemMap.put(itemId, item);
                    newNickName2ItemMap.put(nickName.replace(" ", StringUtils.SPACE).trim(), item);
                }
                log.info("getItemInfo {}", newId2GoodsItemMap.getOrDefault(3408014L, null));
                log.info("getItemInfo {}", newNickName2ItemMap.getOrDefault("多功能多尺寸收纳架收纳盒 锅具餐具收纳", null));
            } catch (ClassNotFoundException | SQLException e) {
                log.error("getItemInfo", e);
            }
        }

        log.info("getItemInfo {}", newId2GoodsItemMap.size());

        id2ItemMap = newId2GoodsItemMap;
        nickName2ItemMap = newNickName2ItemMap;
    }

    public List<Item> getItemsByIds(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return new ArrayList<>();
        } else {
            return itemIds.stream().map(this::getItemById).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
    }

    public Item getItemById(long itemId) {
        return id2ItemMap.getOrDefault(itemId, null);
    }

    public String getJoinPhyCateId(long itemId) {
        Item item = id2ItemMap.getOrDefault(itemId, null);
        if (ObjectUtils.isNotEmpty(item)) {
            return item.joinPhyCateGoryId();
        } else {
            return null;
        }
    }

    public String getRcmdReason(long itemId) {
        FeatureRequest request = FeatureRequest.builder().idType(FEATURE_STORE_DOMAIN).featureNames(FEATURE_NAMES).ids(Sets.newHashSet(String.valueOf(itemId))).build();
        List<FeatureResult> featureResults = featureStoreService.getFeature(request);

        String rcmdReason = null;
        if (!CollectionUtils.isEmpty(featureResults)) {
            FeatureResult featureResult = featureResults.get(0);

            String rcmdReasonListStr = featureResult.getFeatures().getOrDefault(FEATURE_STORE_KEY_RCMD_REASON, null);
            if (StringUtils.isNotEmpty(rcmdReasonListStr)) {
                List<String> rmcdReasonList = null;
                try {
                    rmcdReasonList = IoUtils.parseJson(rcmdReasonListStr, new TypeReference<List<String>>() {
                    });
                } catch (IOException e) {
                    log.error("getRcmdReason", e);
                }

                if (!CollectionUtils.isEmpty(rmcdReasonList)) {
                    rcmdReason = rmcdReasonList.get(new Random().nextInt(rmcdReasonList.size()));
                }
            }
        }
        return rcmdReason;
    }

    public String getADItemName(long itemId) {
        FeatureRequest request = FeatureRequest.builder().idType(FEATURE_STORE_DOMAIN).featureNames(FEATURE_NAMES).ids(Sets.newHashSet(String.valueOf(itemId))).build();
        List<FeatureResult> featureResults = featureStoreService.getFeature(request);

        String adItemName = null;
        if (!CollectionUtils.isEmpty(featureResults)) {
            FeatureResult featureResult = featureResults.get(0);

            String adItemNameListStr = featureResult.getFeatures().getOrDefault(FEATURE_STORE_KEY_AD_ITEM_NAME, null);
            if (!StringUtils.isEmpty(adItemNameListStr)) {
                adItemName = adItemNameListStr;
            }
        }
        return adItemName;
    }

    public String getDescItemName(long itemId) {
        FeatureRequest request = FeatureRequest.builder().idType(FEATURE_STORE_DOMAIN).featureNames(FEATURE_NAMES).ids(Sets.newHashSet(String.valueOf(itemId))).build();
        List<FeatureResult> featureResults = featureStoreService.getFeature(request);

        String descItemName = null;
        if (!CollectionUtils.isEmpty(featureResults)) {
            FeatureResult featureResult = featureResults.get(0);

            String descItemNameListStr = featureResult.getFeatures().getOrDefault(FEATURE_STORE_KEY_DESC_ITEM_NAME, null);
            if (!StringUtils.isEmpty(descItemNameListStr)) {
                descItemName = descItemNameListStr;
            }
        }
        return descItemName;
    }

    public String getDescCombineItemName(long itemId) {
        FeatureRequest request = FeatureRequest.builder().idType(FEATURE_STORE_DOMAIN).featureNames(FEATURE_NAMES).ids(Sets.newHashSet(String.valueOf(itemId))).build();
        List<FeatureResult> featureResults = featureStoreService.getFeature(request);

        String descItemName = null;
        if (!CollectionUtils.isEmpty(featureResults)) {
            FeatureResult featureResult = featureResults.get(0);

            String descItemNameListStr = featureResult.getFeatures().getOrDefault(FEATURE_STORE_KEY_COMBINE_ITEM_NAME, null);
            if (!StringUtils.isEmpty(descItemNameListStr)) {
                descItemName = descItemNameListStr;
            }
        }
        return descItemName;
    }

    public String getRCMDReasonRG(long itemId) {
        FeatureRequest request = FeatureRequest.builder().idType(FEATURE_STORE_DOMAIN).featureNames(FEATURE_NAMES).ids(Sets.newHashSet(String.valueOf(itemId))).build();
        List<FeatureResult> featureResults = featureStoreService.getFeature(request);

        String rcmdReasonRG = null;
        if (!CollectionUtils.isEmpty(featureResults)) {
            FeatureResult featureResult = featureResults.get(0);

            String descItemNameListStr = featureResult.getFeatures().getOrDefault(FEATURE_STORE_KEY_RCMD_REASON_RG, null);
            if (!StringUtils.isEmpty(descItemNameListStr)) {
                rcmdReasonRG = StringUtils.strip(descItemNameListStr, "\"");
            }
        }
        return rcmdReasonRG;
    }

    // 查询复制品原始的id
    // http://yx.mail.netease.com/wiki#/doc/783630
    public long getSrcId(Long itemId) {
        try {
            String cacheValue = GuavaCacheUtil.getStr(String.valueOf(itemId));
            if (StringUtils.isNotEmpty(cacheValue)) {
                return Long.parseLong(cacheValue);
            }

            String respStr = HttpUtils.executePost(itemBaseInfoUrl, IoUtils.toJsonString(ItemBaseInfoReq.buildSimpleReq(itemId)), null);
            JsonNode resp = IoUtils.parseJson(respStr);
            if (resp.get("code").asInt() == 200) {
                JsonNode propertyMap = resp.get("data").get("propertyMap");
                if (propertyMap.has("spuCopyType") && propertyMap.has("srcSpuId")) {
                    long srcSpuId = propertyMap.get("srcSpuId").asLong();
                    return srcSpuId;
                }
            }
        } catch (IOException e) {
            log.error("getSrcId", e);
        }
        return itemId;
    }

    public List<String> phyCateIds(long itemId) {
        if (envService.isTest()) {
            return new ArrayList<>();
        }
        FeatureRequest featureRequest = new FeatureRequest();
        featureRequest.setIdType("itemid");
        featureRequest.setFeatureNames(new HashSet<String>() {{
            add(PHY_CATE1_ID);
            add(PHY_CATE2_ID);
            add(PHY_CATE3_ID);
            add(PHY_CATE4_ID);
        }});

        featureRequest.setIds(new HashSet<>(Collections.singletonList(String.valueOf(itemId))));

        List<String> results = new ArrayList<>();
        try {
            List<FeatureResult> result = featureStoreService.getFeature(featureRequest);
            Map<String, String> map = result.get(0).getFeatures();
            String phyCate1Id = map.get(PHY_CATE1_ID);
            if (StringUtils.isNotEmpty(phyCate1Id)) {
                results.add(phyCate1Id);
            }

            String phyCate2Id = map.get(PHY_CATE2_ID);
            if (StringUtils.isNotEmpty(phyCate2Id)) {
                results.add(phyCate2Id);
            }

            String phyCate3Id = map.get(PHY_CATE3_ID);
            if (StringUtils.isNotEmpty(phyCate3Id)) {
                results.add(phyCate3Id);
            }

            String phyCate4Id = map.get(PHY_CATE4_ID);
            if (StringUtils.isNotEmpty(phyCate4Id)) {
                results.add(phyCate4Id);
            }
        } catch (Exception e) {
            log.error("");
        }

        return results;
    }
}