package com.netease.yx.bot.biz.service.mps;

import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.common.RedisService;
import com.netease.yx.bot.biz.common.mps.MpsReceiveMessageBean;
import com.netease.yx.bot.biz.common.mps.MpsSender;
import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.constant.IntentTypeV2;
import com.netease.yx.bot.core.model.constant.MultiChannelPlatformType;
import com.netease.yx.bot.core.model.constant.RedisKey;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaResp;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaRslt;
import com.netease.yx.bot.core.model.log.MultiChannelPipelineLog;
import com.netease.yx.bot.core.model.train.MsgIdMap;
import com.netease.yx.bot.core.model.train.RecommendKnowledge;
import com.netease.yx.bot.core.model.train.TrainContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SessionCloseService {

    private static final String TOPIC = "intelli_bot_qa_recommend_knowledge";

    @Autowired
    private RedisService redisService;
    @Autowired
    private MpsSender mpsSender;
    @Autowired
    private EnvService envService;
    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;

    /**
     * 1. 根据前端消息id，查询前端sessionId
     * 2. 遍历消息，获取当时的日志, 处理商品卡片绑定
     * 3. 会话聚合和去重
     * 4. 发送消息
     *
     * @param mpsMessage 消息内容
     */
    @SneakyThrows
    public void process(MpsReceiveMessageBean mpsMessage) {
        SessionAnalysisMsg mpsMsg = IoUtils.parseJson(mpsMessage.getPayload(), SessionAnalysisMsg.class);
        log.info("process {}", mpsMsg);
        // 后端会话id，全局唯一
        Long kefuSessionId = mpsMsg.getSessionId();

        // 消息映射
        List<MsgIdMap> msgIdMaps = mpsMsg.getMessageIds();

        List<TrainContext> trainContexts = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(msgIdMaps)) {
            Set<String> set = new HashSet<>();
            // 结束会话
            String platformSessionId = redisService.get(StringUtils.join(RedisKey.MSG_2_PLATFORM_SESSION_ID, msgIdMaps.get(0).getPlatformMessageId()));
            log.info("platformSessionId {}", platformSessionId);
            redisService.set(BotContext.buildContextKey(platformSessionId), "");
            redisService.delete(BotContext.buildContextKey(platformSessionId));

            for (MsgIdMap msgIdMap : msgIdMaps) {
                String multiChannelPipelineLogStr = redisService.get(
                        StringUtils.join(RedisKey.MSG_2_PIPELINE_LOG, msgIdMap.getPlatformMessageId()));
                log.info("multiChannelPipelineLogStr {}", multiChannelPipelineLogStr);
                if (StringUtils.isNotEmpty(multiChannelPipelineLogStr)) {
                    MultiChannelPipelineLog multiChannelPipelineLog = IoUtils.parseJson(multiChannelPipelineLogStr, MultiChannelPipelineLog.class);

                    ChannelQaResp channelQaResp = multiChannelPipelineLog.getOutput();
                    BotContext botContext = multiChannelPipelineLog.getContext();
                    // 如果是卡片，则不再处理
                    if (channelQaResp.getIntentTypeV2() != null && channelQaResp.getIntentTypeV2().isCard()) {
                        continue;
                    }

                    if (CollectionUtils.isNotEmpty(channelQaResp.getFaqList())) {
                        List<RecommendKnowledge> recommendKnowledges = channelQaResp.getFaqList().stream().map(this::transfer).collect(Collectors.toList());

                        // 过滤无意义的数据
                        if (IntentTypeV2.CHITCHAT.equals(channelQaResp.getIntentTypeV2())) {
                            String input = botContext.getHstTexts().get(0);
                            if (StringUtils.length(input) <= 3) {
                                log.info("filter train {} {}", input, channelQaResp.getFaqList());
                                continue;
                            }
                        }

                        // 过滤数据
                        if (CollectionUtils.isNotEmpty(channelQaApolloConfig.getIgnoreKnowledges()) && channelQaApolloConfig.getIgnoreKnowledges().contains(recommendKnowledges.get(0).getKnowledgeId())) {
                            log.info("ignore {} {}", recommendKnowledges.get(0).getKnowledgeId(), multiChannelPipelineLog.getInput());
                            continue;
                        }
                        // 过滤数据
                        if (CollectionUtils.isNotEmpty(channelQaApolloConfig.getTrainIgnoreContents())) {
                            String input = botContext.getHstTexts().get(0);
                            boolean flag = false;
                            for (String trainIgnoreContent : channelQaApolloConfig.getTrainIgnoreContents()) {
                                if (StringUtils.contains(input, trainIgnoreContent)) {
                                    log.info("ignore {} {}", trainIgnoreContent, input);
                                    flag = true;
                                    break;
                                }
                            }
                            if (flag) {
                                continue;
                            }
                        }

                        String duplicateId = recommendKnowledges.toString();
                        // 去重
                        if (set.contains(duplicateId)) {
                            continue;
                        }

                        set.add(duplicateId);

                        TrainContext trainContext = TrainContext.builder()
                                .platform(multiChannelPipelineLog.getInput().getPlatform())
                                .channelId(multiChannelPipelineLog.getInput().getChannelId())
                                .sessionId(kefuSessionId)
                                .messageId(msgIdMap.getKefuMessageId())
                                .recommendKnowledge(recommendKnowledges)
                                .sessionInteraction(mpsMsg.getSessionInteraction())
                                .build();

                        if (envService.isTest()) {
                            trainContext.setIntentId(channelQaResp.getFaqList().get(0).getIntentId());
                        }

                        if (ObjectUtils.isNotEmpty(channelQaResp.getIntentTypeV2())) {
                            trainContext.setIntentType(channelQaResp.getIntentTypeV2().getCode());
                        }

                        // 当时消息粒度，继承下来的商品id
                        if (botContext.getItemId() != 0 && StringUtils.isNotEmpty(botContext.getPlatformItemId()) && StringUtils.isNotEmpty(botContext.getPlatformRawItemCardId())) {
                            trainContext.setItemId(botContext.getItemId());
                            trainContext.setPlatformItemId(botContext.getPlatformItemId());
                            trainContext.setPlatformRawItemCardId(botContext.getPlatformRawItemCardId());
                            trainContext.setItemPhyCategoryIdStr(botContext.getItemPhyCategoryIdStr());
                        }

                        if (CollectionUtils.isNotEmpty(channelQaApolloConfig.getTestIntentIdKnowledgeIds()) && channelQaApolloConfig.getTestIntentIdKnowledgeIds().contains(recommendKnowledges.get(0).getKnowledgeId())) {
                            log.info("test intentId {} {}", trainContext.getIntentType(), recommendKnowledges.get(0).getKnowledgeId());
                            trainContext.setIntentId(trainContext.getIntentType());
                        }
                        trainContexts.add(trainContext);
                    }
                }
            }
        }

        for (TrainContext trainContext : trainContexts) {
            // 开关控制是否发送
            // 目前七鱼都实时触发
            if (channelQaApolloConfig.getSessionCloseMpsFlag() == 1
                    || MultiChannelPlatformType.QY.getName().equals(trainContext.getPlatform())) {
                String mpsKey = "mps:" + trainContext.getPlatform() + trainContext.getMessageId();
                // 不重复发送
                if (StringUtils.isNotEmpty(redisService.get(mpsKey))) {
                    log.info("duplicate {}", mpsKey);
                    continue;
                }
                mpsSender.sendMps(TOPIC, IoUtils.toJsonString(trainContext));
                redisService.set(mpsKey, "1");
            }
        }
    }

    private RecommendKnowledge transfer(ChannelQaRslt channelQaRslt) {
        RecommendKnowledge recommendKnowledge = RecommendKnowledge
                .builder()
                .knowledgeSource(channelQaRslt.getKnowledgeSource())
                .knowledgeId(channelQaRslt.getKnowledgeId())
                .answerId(channelQaRslt.getAnswerId())
                .itemId(channelQaRslt.getItemId())
                .itemPhyCategoryIdStr(channelQaRslt.getItemPhyCategoryIdStr())
                .build();
        return recommendKnowledge;
    }
}
