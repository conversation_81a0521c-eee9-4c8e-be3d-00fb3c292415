/**
 * @(#)QualityInspectionCorrectionManager.java, 2021/12/15.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service.unuse.assistant;

import com.fasterxml.jackson.databind.JsonNode;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.qualityInspection.CorrectionSetting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR> @ corp.netease.com)
 * 错别字检查，感觉可以被降级
 */
@Component
@Slf4j
public class QualityInspectionCorrectionManager {

    @Autowired
    private CorrectionSetting correctionSetting;

    public boolean correctionRst(String[] inputList) {
        //是否有错别字
        if (inputList.length == 0) return false;
        Map<String, List<String>> data = new HashMap<>();
        ArrayList<String> inputArray = new ArrayList<String>(Arrays.asList(inputList));
        data.put("instances", inputArray);
        String inputjsonstring = IoUtils.toJsonString(data);

        try {
            log.info("correctionRst, request: {}, {}", correctionSetting.getUrl(), inputjsonstring);
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("host", correctionSetting.getHost());
            String outputJson = HttpUtils.executePost(correctionSetting.getUrl(), inputjsonstring, null, headers);
            JsonNode root = IoUtils.parseJson(outputJson);
            log.info("TextPloarAnalysis, response: {}", root);

            JsonNode predictions = root.get("predictions");

            for (int i = 0; i < predictions.size(); i++) {
                JsonNode prediction = predictions.get(i);
                if (prediction.get(1).size() > 0) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("http exception", e);
        }

        return false;

    }


}