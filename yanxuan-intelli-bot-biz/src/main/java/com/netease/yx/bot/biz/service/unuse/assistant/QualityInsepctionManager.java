/**
 * @(#)QualityInsepctionManager.java, 2021/12/10.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service.unuse.assistant;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.hankcs.hanlp.classification.tokenizers.HanLPTokenizer;
import com.netease.yx.bot.biz.service.IntentServiceV2;
import com.netease.yx.bot.core.model.constant.Level1IntentType;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import com.netease.yx.bot.core.model.entity.qualityInspection.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Component
@Data
@Slf4j
public class QualityInsepctionManager {

    private final HanLPTokenizer hanlpTokenizer = new HanLPTokenizer();
    public SpecialTokens specialTokens = new SpecialTokens();
    @Autowired
    private TermWords termWords;
    @Autowired
    private AppeasingTokens appeasingTokens;
    @Autowired
    private ComplaintTokens complaintTokens;
    @Autowired
    private NegativeQaThreshold negativeQaThreshold;
    @Autowired
    private IntentServiceV2 intentServiceV2;
    @Autowired
    private QualityInspectionCorrectionManager qualityInspectionCorrectionManager;
    @Autowired
    private QualityInspectionPloarAnalysisManager qualityInspectionPloarAnalysisManager;
    @Autowired
    private PolarAnalysisSetting polarAnalysisSetting;
    @Autowired
    private CorrectionSetting correctionSetting;

    private void preprocessing(QualityInspectionRequest qualityInspectionRequest) {
        //按时间排序
        if (qualityInspectionRequest.getSessionInfo() != null) {
            qualityInspectionRequest.getSessionInfo().sort(Comparator.comparingLong(TextRoundInfo::getMessageCreateTs));
        }
    }

    // 专业术语匹配
    public boolean termMatching(String inputText) {
        if (inputText.length() == 0) return false;
        //特殊专业词汇
        for (String item : termWords.termwords) {
            if (item.length() == 0) continue;
            if (inputText.toLowerCase().contains(item.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    // 特殊字符判定： 如 "？"，"..."
    public boolean hasSpecialToken(String inputText) {
        if (inputText.length() == 0) return false;
        Pattern p = Pattern.compile(specialTokens.getRegexString());
        Matcher m = p.matcher(inputText);
        return m.find();

    }

    //用户负面情绪判定
    public boolean isNegativeEmotion(String inputs, String uniqueid) {
        if (inputs.length() == 0) return false;
        PolarAnalysisResponse polarities = qualityInspectionPloarAnalysisManager.getPloarScore(inputs, uniqueid);
        if (polarities == null) {
            return false;
        } else {
            return polarities.getGoodScore() < 0.005;
        }
    }

    //客服不耐烦情绪判定
    public boolean isImpatience(String inputs) {
        //TODO
        return false;
    }

    //错别字判定
    public boolean isContainTypo(String[] inputText) {
        if (inputText.length == 0) return false;
        return qualityInspectionCorrectionManager.correctionRst(inputText);
    }

    // 一问一答判定
    public boolean isNegativeQa(QualityInspectionRequest qualityInspectionRequest) {
        int userInputCount = 0;
        int staffInputCount = 0;
        for (TextRoundInfo singleRound : qualityInspectionRequest.getSessionInfo()) {
            // 用户
            if (singleRound.getTextType() == 0) {
                if ((singleRound.getMsgStatus() == 1)) userInputCount++;
            } else {//客服
                if ((singleRound.getStaffId() != -1) &&
                        (singleRound.getMsgStatus() == 1) && (singleRound.getIsAutoReply() == 0)) staffInputCount++;
            }
        }
        if (userInputCount == 0) return false;

        return ((float) (staffInputCount / userInputCount)) < negativeQaThreshold.getThreshold();
    }

    //是否有安抚
    public boolean hasContainAppeasing(String singleRound) {
        if (singleRound.length() == 0) return false;
        for (String singleSample : appeasingTokens.getSamples()) {
            if (singleSample.length() == 0) continue;
            if (singleRound.contains(singleSample)) return true;
        }
        return false;
    }

    //是否有投诉
    public boolean hasComplaint(String singleRound) {
        if (singleRound.length() == 0) return false;
        for (String singleWord : complaintTokens.getWords()) {
            if (singleWord.length() == 0) continue;
            if (singleRound.contains(singleWord)) return true;
        }
        return false;
    }

    //判定是否有购买的意图
    public boolean hasIntent(String text) {
        if (text.length() == 0) return false;
        // 一级意图和人工意图等
        IntentRslt intentRslt = intentServiceV2.getIntentV2(text);
        boolean isGuide = Level1IntentType.GUIDE.equals(intentRslt.getFirstIntent());
        return isGuide;
    }

    // 判定是否包含商品卡片
    public boolean isContainCard(String singleRound) {
        if (singleRound.length() == 0) return false;
        try {
            JSONObject singRoundObject = JSON.parseObject(singleRound);
            if (singRoundObject.containsKey("cardInfo")) {
                JSONObject innerDict = (JSONObject) singRoundObject.get("cardInfo");
                if ((innerDict.containsKey("name") && (innerDict.get("name").equals("小选为您推荐")))) {
                    return true;
                }

            }
        } catch (JSONException e) {
            return false;
        }

        return false;
    }

    //无货场景推荐
    public boolean isRelatedRecommend(QualityInspectionRequest qualityInspectionRequest) {
        //判定是否有商品卡片
        if ((qualityInspectionRequest == null) | (qualityInspectionRequest.getSessionInfo() == null)) return true;

        boolean hasShoppingIntent = false;
        boolean hasRecommend = false;
        for (TextRoundInfo singleRound : qualityInspectionRequest.getSessionInfo()) {
            // 用户
            if ((singleRound.getTextType() == 0) && (hasIntent(singleRound.getInputText()))) {
                hasShoppingIntent = true;
            }
            //客服
            if ((hasShoppingIntent) && (singleRound.getTextType() == 1) && (isContainCard(singleRound.getInputText()))) {
                hasRecommend = true;
                break;
            }
        }
        return (!hasShoppingIntent) | (hasRecommend == hasShoppingIntent);
    }

    //check是否是json字符串
    public boolean isJsonStr(String inputStr) {

        JSONValidator validator = JSONValidator.from(inputStr);
        return validator.validate();
    }

    public QualityInspectionResponse inspectionResult(QualityInspectionRequest qualityInspectionRequest) {


        if ((qualityInspectionRequest == null) | (qualityInspectionRequest.getSessionInfo() == null))
            return new QualityInspectionResponse();

        this.preprocessing(qualityInspectionRequest);
        Set<Integer> problemsId = new HashSet<>();

        //一问一答
        if (isNegativeQa(qualityInspectionRequest)) problemsId.add(InspectionErrorID.ISNEGATIVEQA);
        //关联推荐
        if (!isRelatedRecommend(qualityInspectionRequest)) problemsId.add(InspectionErrorID.NOTRELATEDRECOMMEND);
        //安抚
        boolean isContainAppeasing = true;
        //错别字
        List<String> staffDialog = new ArrayList<>();

        for (TextRoundInfo singleRound : qualityInspectionRequest.getSessionInfo()) {
            // 用户
            if (singleRound.getTextType() == 0) {
                if ((singleRound.getMsgStatus() == 1)) {
                    //用户负面情绪
                    if ((!problemsId.contains(InspectionErrorID.ISNEGATIVEMOTION)) && isNegativeEmotion(singleRound.getInputText(), Long.toString(qualityInspectionRequest.getSid()))) {
                        problemsId.add(InspectionErrorID.ISNEGATIVEMOTION);
                        isContainAppeasing = false;
                    }
                    //是否投诉
                    if ((!problemsId.contains(InspectionErrorID.ISCOMPLAINT)) && hasComplaint(singleRound.getInputText()))
                        problemsId.add(InspectionErrorID.ISCOMPLAINT);
                }
            } else {
                //客服输入
                if ((singleRound.getInputText().length() > 0) && (!isJsonStr(singleRound.getInputText()))) {
                    staffDialog.add(singleRound.getInputText());
                }

                if ((singleRound.getStaffId() != -1) && (singleRound.getMsgStatus() == 1) && (singleRound.getIsAutoReply() == 0)) {
                    //专业词汇
                    if ((!problemsId.contains(InspectionErrorID.ISCONTAINTERM)) && termMatching(singleRound.getInputText()))
                        problemsId.add(InspectionErrorID.ISCONTAINTERM);
                    //特殊字符
                    if ((!problemsId.contains(InspectionErrorID.ISCONTAINSPECIALLCHAR)) && hasSpecialToken(singleRound.getInputText()))
                        problemsId.add(InspectionErrorID.ISCONTAINSPECIALLCHAR);

                    //客服不耐烦情绪
                    if ((!problemsId.contains(InspectionErrorID.ISIMPATIENCE)) && isImpatience(singleRound.getInputText()))
                        problemsId.add(InspectionErrorID.ISIMPATIENCE);
                    //是否包含安抚
                    if ((!isContainAppeasing) && problemsId.contains(InspectionErrorID.ISNEGATIVEMOTION) && (hasContainAppeasing(singleRound.getInputText())))
                        isContainAppeasing = true;

                }
            }
        }
        if (!isContainAppeasing) problemsId.add(InspectionErrorID.NOTCONTAINAPPEASING);

        //错别字
        if (isContainTypo(staffDialog.toArray(new String[0]))) problemsId.add(InspectionErrorID.ISCOMTAINTYPO);

        QualityInspectionResponse qualityInspectionResponse = new QualityInspectionResponse();
        qualityInspectionResponse.setProblemIds(new ArrayList<>(problemsId));
        return qualityInspectionResponse;

    }
}