/**
 * @(#)MpsDispatcher.java, 2017/12/8.
 * <p/>
 * Copyright 2017 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.common.mps;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.netease.yx.bot.biz.common.EnvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MpsDispatcher {

    private Map<String, MpsBaseHandler> handlerMap;

    @Autowired
    private List<MpsBaseHandler> mpsBaseHandlerList;

    @Autowired
    private EnvService envService;

    /**
     * 幂等回滚
     */
    private static void rollBackIdempotent(MpsReceiveMessageBean message) {
        String key = MessageFormat.format("mps_{0}_{1}_{2}", getAppId(), message.getProduct(), message.getMessageId());
        MessageIdempotentUtils.rollBack(key);
    }

    private static String getAppId() {
        return System.getProperty("app.id");
    }

    /**
     * 消息重复接收校验
     *
     * @return 是否首次接收到该消息
     */
    private boolean checkAndInsertIdempotent(MpsReceiveMessageBean message) {
        String key = MessageFormat.format("mps_{0}_{1}_{2}_{3}", getAppId(), message.getProduct(), message.getMessageId(), envService.getEnv());
        return MessageIdempotentUtils.check(key);
    }

    @PostConstruct
    private void init() {
        handlerMap = new HashMap<>();
        if (CollectionUtils.isEmpty(mpsBaseHandlerList)) {
            return;
        }
        for (MpsBaseHandler handler : mpsBaseHandlerList) {
            MpsHandler mpsHandler = AnnotationUtils.findAnnotation(handler.getClass(), MpsHandler.class);
            if (null != mpsHandler) {
                String handlerKey = getHandlerKey(mpsHandler.product(), mpsHandler.topic());
                if (handlerMap.containsKey(handlerKey)) {
                    log.error("[op:initMpsDispatcher] handlerKey={} already implements MpsHandler, please check.", handlerKey);
                    throw new RuntimeException("[op:initMpsDispatcher] handlerKey ={} already implements MpsHandler");
                }
                handlerMap.put(handlerKey, handler);
            } else {
                log.error("[op:initMpsDispatcher] simpleName={} don't implements MpsHandler, please check.", handler.getClass().getSimpleName());
                throw new RuntimeException(handler.getClass().getSimpleName() + " don't implements MpsHandler");
            }
        }
    }

    public void onMessage(List<MpsReceiveMessageBean> messages) {

        if (CollectionUtils.isEmpty(messages)) {
            log.warn("[op:onMessage] messages null");
            return;
        }

        for (MpsReceiveMessageBean mpsMessage : messages) {
            // 幂等检查
            if (!checkAndInsertIdempotent(mpsMessage)) {
                log.warn("[op:onMessage] 此消息已经被处理过，不再重复处理，product={},messageId={}", mpsMessage.getProduct(), mpsMessage.getMessageId());
                return;
            }
            MpsBaseHandler mpsHandler = handlerMap.get(getHandlerKey(mpsMessage.getProduct(), mpsMessage.getTopic()));
            if (mpsHandler == null) {
                log.warn("[op:onMessage] ignore message = {}", JSON.toJSONString(mpsMessage));
                continue;
            }
            try {
                log.info("[op:onMessage] handle message, message={}, messageId={}, topic={}", JSONObject.toJSONString(mpsMessage), mpsMessage.getMessageId(), mpsMessage.getTopic());
                mpsHandler.process(mpsMessage);
            } catch (Exception e) {
                log.error("[op:onMessage] handle message error, message = {}", JSON.toJSONString(mpsMessage), e);
                rollBackIdempotent(mpsMessage);
                throw e;
            } finally {
                log.info("[op:onMessage] handle message done, messageId={}", mpsMessage.getMessageId());
            }
        }
    }

    private String getHandlerKey(String product, String topic) {
        return product + "#" + topic;
    }
}