package com.netease.yx.bot.biz.common.es8;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleKnnRequest {
    private String index;
    private List<String> fields;
    private List<FilterTerm> filterTerms;
    private String knnField;
    private List<Float> vector;
    private int topK;
    private int candidates = 100;
}
