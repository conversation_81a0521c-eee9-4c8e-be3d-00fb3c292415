/**
 * @(#)QualityInspectionPloarAnalysisManager.java, 2021/12/15.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service.unuse.assistant;

import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.qualityInspection.PolarAnalysisResponse;
import com.netease.yx.bot.core.model.entity.qualityInspection.PolarAnalysisSetting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @ corp.netease.com)
 * 客服输入极性检查
 */
@Component
@Slf4j
public class QualityInspectionPloarAnalysisManager {

    @Autowired
    private PolarAnalysisSetting polaranalysissetting;

    public PolarAnalysisResponse getPloarScore(String inputs, String uniqueid) {
        //情感极性的分数
        Map<String, String> data = new HashMap<>();
        data.put("content", inputs);
        data.put("type", "XX");
        data.put("uniqueId", uniqueid);
        String inputjsonstring = IoUtils.toJsonString(data);

        PolarAnalysisResponse response = null;
        try {
            log.info("TextPloarAnalysis, request: {}, {}", polaranalysissetting.getUrl(), inputjsonstring);
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("host", polaranalysissetting.getHost());
            String outputJson = HttpUtils.executePost(polaranalysissetting.getUrl(), inputjsonstring, null, headers);
            response = IoUtils.parseJson(outputJson, PolarAnalysisResponse.class);
            log.info("TextPloarAnalysis, response: {}", response);
        } catch (IOException e) {
            log.info("http exception", e);
        }

        return response;

    }

}