/**
 * @(#)EnvService.java, 2020/9/8.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.common;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Service
@Data
public class EnvService {
    private static final String ONLINE = "online";
    private static final String TEST = "test";
    private static final String RELEASE = "release";
    private static final String DEV = "dev";


    @Value("${spring.profiles.active}")
    private String env;

    public boolean isTest() {
        return StringUtils.equals(TEST, env);
    }

    public boolean isOnline() {
        return StringUtils.equals(ONLINE, env);
    }

    public boolean isDev() {
        return StringUtils.equals(DEV, env);
    }

    public boolean isReleaseOrOnline() {
        return StringUtils.equals(ONLINE, env) || StringUtils.equals(RELEASE, env);
    }

    public boolean isRelease() {
        return StringUtils.equals(RELEASE, env);
    }
}