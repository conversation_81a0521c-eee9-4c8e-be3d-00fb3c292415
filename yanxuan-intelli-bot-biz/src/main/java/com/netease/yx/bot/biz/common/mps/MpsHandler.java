package com.netease.yx.bot.biz.common.mps;

import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Component
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface MpsHandler {
    /**
     * 关联的发送方产品号
     *
     * @return 发送方产品号
     */
    String product();

    /**
     * 关联的主题
     *
     * @return 主题
     */
    String topic();
}
