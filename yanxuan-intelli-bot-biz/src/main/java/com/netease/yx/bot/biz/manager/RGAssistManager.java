package com.netease.yx.bot.biz.manager;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.netease.yanxuan.feast.sdk.vo.FeatureRequest;
import com.netease.yanxuan.feast.sdk.vo.FeatureResult;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.common.FeatureStoreService;
import com.netease.yx.bot.biz.service.ItemService;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.biz.applloconfig.ItemSearchApolloConfig;
import com.netease.yx.bot.biz.service.GuideService;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.guide.GuideRCMDListRslt;
import com.netease.yx.bot.core.model.entity.guide.GuideReq;
import com.netease.yx.bot.core.model.entity.guide.GuideRslt;
import com.netease.yx.bot.core.model.entity.rgAssist.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 人工辅助，导购等
 * 可以废弃
 */
@Service
@Component
@Slf4j
public class RGAssistManager {
    private static final List<Long> mockItemIds = Arrays.stream("1397017,3991032,1132003,1085002,1056001,1081000,1033000,1155000".split(",")).map(Long::parseLong).collect(Collectors.toList());
    private static final List<Integer> mockTimes = Arrays.stream("1,2,3,4,5,6,7".split(",")).map(Integer::parseInt).collect(Collectors.toList());
    private final static String FEATURE_STORE_DOMAIN = "userid";
    private final static String FEATURE_STORE_KEY_USER_ACT = "user_act_v5";
    private final static String USER_EVENT_VIEW_CH_NAME = "访问";
    private final static String USER_EVENT_OTHER_CH_NAME = "其它";
    private static final int MAX_GUIDE_ITEMS = 10;
    private static final int MAX_GUIDE_TRACK_ITEMS = 10;
    private static final String RCMD_REASON = "， 推荐给您选购~";
    private static final String RCMD_EMPTY_REASON = "推荐给您选购~";

    private static final Random random = new Random();
    private static final Set<String> FEATURE_NAMES = new HashSet<String>() {{
        add(FEATURE_STORE_KEY_USER_ACT);
    }};
    @Autowired
    private EnvService envService;
    @Autowired
    private GuideService guideService;
    @Autowired
    private ItemService itemService;
    @Autowired
    private FeatureStoreService featureStoreService;
    @Autowired
    private BotApolloConfig botApolloConfig;

    @Autowired
    private ItemSearchApolloConfig itemSearchApolloConfig;

    public RGAssistGuideResp guidePipeline(RGAssistGuideReq RGAssistGuideReq) {

        if (envService.isTest()) {
            Set<Long> newItemIds = new HashSet<>();
            newItemIds.add(1120000L);
            int count = 0;
            while (count < 6) {
                int randomIdx = random.nextInt(mockItemIds.size());
                newItemIds.add(mockItemIds.get(randomIdx));
                count += 1;
            }
            List<Long> target = new ArrayList<>(newItemIds);

            List<String> mockRCMDReason = new ArrayList<>();
            target = target.stream().limit(MAX_GUIDE_ITEMS).collect(Collectors.toList());
            for (int i = 0; i < target.size(); i++) {
                mockRCMDReason.add("小选为您推荐_" + String.valueOf(i));
            }
            return new RGAssistGuideResp(mockRCMDReason, target);
        }


        // 用户的商品卡片不推
        String itemId = null;
        if (RGAssistGuideReq.getItemId() != null) {
            itemId = String.valueOf(RGAssistGuideReq.getItemId());
        } else if (guideService.checkItemIdValid(RGAssistGuideReq.getItemId())) {
            itemId = String.valueOf(RGAssistGuideReq.getItemId());
        }
        guideService.uploadItemIdShowList(String.valueOf(RGAssistGuideReq.getUserId()), itemId);

        GuideReq guideReq = new GuideReq("", "", RGAssistGuideReq.getUserId(), itemId);
        GuideRCMDListRslt guideResultList = guideService.getActiveRes(guideReq);


        List<String> rcmdReasons = new ArrayList<>();
        List<Long> rcmdItemIds = new ArrayList<>();
        for (GuideRslt guideRslt : guideResultList.getGuideRsltList()) {
            String rcmdReason = genRcmdReason(guideRslt.getItemId());
            if (!StringUtils.isEmpty(rcmdReason)) {
                rcmdItemIds.add(guideRslt.getItemId());
                rcmdReasons.add(rcmdReason);
            }
        }

        // 配置的推荐商品置顶

        rcmdItemIds.addAll(0, botApolloConfig.getRGAssistItemIdDefault());
        rcmdReasons.addAll(0, botApolloConfig.getRGAssistItemReasonDefault());

        rcmdReasons = rcmdReasons.stream().limit(MAX_GUIDE_ITEMS).collect(Collectors.toList());
        rcmdItemIds = rcmdItemIds.stream().limit(MAX_GUIDE_ITEMS).collect(Collectors.toList());

        return new RGAssistGuideResp(rcmdReasons, rcmdItemIds);
    }

    public RGAssistGuideTrackResp guideTrackPipeline(RGAssistGuideTrackReq RGAssistGuideTrackReq) {

        if (envService.isTest()) {
            int randomIdx = random.nextInt(mockTimes.size());
            List<ItemViewInfo> res = new ArrayList<>();
            for (int i = 0; i < randomIdx; i++) {
                res.add(new ItemViewInfo(mockItemIds.get(i), "小选推荐_" + i, GuideTrackAction.CLICK.getValue(), (long) (Math.random() * 100)));
            }
            log.info("result: {}", res);
            res.sort(new Comparator<ItemViewInfo>() {
                @Override
                public int compare(ItemViewInfo o1, ItemViewInfo o2) {
                    return o1.compareTo(o2);
                }
            });
            log.info("result: {}", res);
            return new RGAssistGuideTrackResp(res);
        }

        List<EventAct> userEvents = new ArrayList<>();
        List<ItemViewInfo> guideTrackRslt = new ArrayList<>();
        // 向feature store请求获取用户历史点击数据
        try {
            FeatureRequest request = FeatureRequest.builder()
                    .idType(FEATURE_STORE_DOMAIN)
                    .featureNames(FEATURE_NAMES)
                    .ids(Sets.newHashSet(String.valueOf(RGAssistGuideTrackReq.getUserId())))
                    .build();
            List<FeatureResult> featureResults = featureStoreService.getFeature(request);

            log.info("RGAssistService.guideTrackPipeline.featureResults: {}", featureResults);
//            List<FeatureResult> featureResults = new ArrayList<>();
            if (!CollectionUtils.isEmpty(featureResults)) {
                FeatureResult featureResult = featureResults.get(0);
//                log.info("RGAssistService.guideTrackPipeline.featureResults_0: {}", featureResult);
                String userClickHstStr = featureResult.getFeatures().getOrDefault(FEATURE_STORE_KEY_USER_ACT, null);
                if (StringUtils.isEmpty(userClickHstStr)) {
                    return new RGAssistGuideTrackResp(guideTrackRslt);
                }
                List<String> userClickList = IoUtils.parseJson(userClickHstStr, new TypeReference<List<String>>() {
                });
                userEvents = userClickList.stream().map(str -> {
                    String[] firstSplit = str.split(":");
                    String[] secondSplit = firstSplit[1].split("#");
                    // "1603854524:3990914#view_detail"
                    return new EventAct(secondSplit[1],//eventName
                            secondSplit[0],//itemId
                            Long.parseLong(firstSplit[0]));//timestamp
                }).filter(x -> x.getItemId() != null).collect(Collectors.toList());
            }

            Map<String, Map<String, Integer>> countRslt = new HashMap<>();
            if (!CollectionUtils.isEmpty(userEvents)) {
                for (EventAct eventAct : userEvents) {
                    if (!countRslt.containsKey(eventAct.getItemId())) {
                        countRslt.put(eventAct.getItemId(), new HashMap<>());
                        countRslt.get(eventAct.getItemId()).put(USER_EVENT_VIEW_CH_NAME, 0);
                        countRslt.get(eventAct.getItemId()).put(USER_EVENT_OTHER_CH_NAME, 0);
                    }

                    String eventCHName = reNameUserEvent(eventAct.getEventName());
                    updateEventCount(countRslt.get(eventAct.getItemId()), eventCHName);
                }
            }


            for (Map.Entry<String, Map<String, Integer>> entry : countRslt.entrySet()) {
                if (entry.getValue().containsKey(USER_EVENT_VIEW_CH_NAME)) {
                    String rcmdReason = genRcmdReason(Long.parseLong(entry.getKey()));

                    ItemViewInfo itemViewInfo = new ItemViewInfo(Long.parseLong(entry.getKey()),
                            rcmdReason, USER_EVENT_VIEW_CH_NAME, entry.getValue().get(USER_EVENT_VIEW_CH_NAME));

                    guideTrackRslt.add(itemViewInfo);
                }
            }

            guideTrackRslt.sort(ItemViewInfo::compareTo);

            guideTrackRslt = guideTrackRslt.stream().limit(MAX_GUIDE_TRACK_ITEMS).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("RGAssistService.guideTrackPipeline.featureResults error:", e);
            return new RGAssistGuideTrackResp(guideTrackRslt);
        }
        return new RGAssistGuideTrackResp(guideTrackRslt);
    }

    private String reNameUserEvent(String name) {
        if ("view_detail".equals(name)) {
            return USER_EVENT_VIEW_CH_NAME;
        }
        return USER_EVENT_OTHER_CH_NAME;
    }

    private void updateEventCount(Map<String, Integer> countRslt, String eventName) {
        Integer count = countRslt.get(eventName);
        countRslt.put(eventName, count + 1);
    }

    public String genRcmdReason(RGAssistRcmdReasonReq rgAssistRcmdReasonReq) {
        long itemId = rgAssistRcmdReasonReq.getItemId();
        // 有配置读配置
        Map<Long, Map<Long, String>> rcmdReasonMap = itemSearchApolloConfig.getChannelRcmdReasonMap();
        if (MapUtils.isNotEmpty(rcmdReasonMap)) {
            if (rcmdReasonMap.containsKey(itemId)) {
                String rcmdReson = rcmdReasonMap.get(itemId).getOrDefault(rgAssistRcmdReasonReq.getChannelId(), null);
                // 如果为空，尝试主站兜底
                if (StringUtils.isEmpty(rcmdReson)) {
                    rcmdReson = rcmdReasonMap.get(itemId).getOrDefault(1L, null);
                }
                // 如果能取到，则返回
                if (StringUtils.isNotEmpty(rcmdReson)) {
                    return rcmdReson;
                }
            }
        }

        if (envService.isTest()) {
            return "小选为您推荐好物~";
        }

        String rcmdStr;
        // 先找人工提供的推荐话术是否存在
        rcmdStr = itemService.getRCMDReasonRG(itemId);
        if (rcmdStr != null && rcmdStr.length() > 4) {
            return rcmdStr;
        }

        rcmdStr = itemService.getDescCombineItemName(itemId);
        if (StringUtils.isEmpty(rcmdStr)) {
            rcmdStr = itemService.getDescItemName(itemId);

            if (StringUtils.isEmpty(rcmdStr)) {
                rcmdStr = itemService.getRcmdReason(itemId);
                if (StringUtils.isEmpty(rcmdStr)) {
                    return RCMD_EMPTY_REASON;
                }
            }
        }

        return rcmdStr + RCMD_REASON;
    }

    /* 人工侧商品推荐理由
     * */
    public String genRcmdReason(Long itemId) {
        return genRcmdReason(new RGAssistRcmdReasonReq(itemId, 1));
    }
}
