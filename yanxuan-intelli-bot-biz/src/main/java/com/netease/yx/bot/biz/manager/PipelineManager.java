/**
 * @(#)PipelineManager.java, 2020/7/16.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.manager;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yanxuan.log.api.statlog.StatLogger;
import com.netease.yanxuan.log.api.statlog.StatLoggerFactory;
import com.netease.yx.bot.biz.common.*;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.biz.service.*;
import com.netease.yx.bot.biz.service.qa.FaqMatchPipelineService;
import com.netease.yx.bot.biz.service.qa.ItemAttributeService;
import com.netease.yx.bot.biz.service.qa.recall.RecallServiceWithEs8;
import com.netease.yx.bot.common.util.CustomLangUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.constant.*;
import com.netease.yx.bot.core.model.entity.*;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaResp;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaRslt;
import com.netease.yx.bot.core.model.entity.chat.ChatType;
import com.netease.yx.bot.core.model.entity.faq.FaqIdRslt;
import com.netease.yx.bot.core.model.entity.guide.GuideRCMDListRslt;
import com.netease.yx.bot.core.model.entity.guide.GuideReq;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import com.netease.yx.bot.core.model.entity.item.Item;
import com.netease.yx.bot.core.model.entity.knowledge.Knowledge;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import com.netease.yx.bot.core.model.log.BotRespWithLog;
import com.netease.yx.bot.core.model.log.MainPipelineLog;
import com.netease.yx.bot.core.model.log.MultiChannelPipelineLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Component
@Slf4j
public class PipelineManager {
    private static final String LOG_TOPIC = "BOT_PIPELINE";
    private static final String LOG_MODULE = "PipelineService";
    private static final int MAX_PIPELINE_NUM = 2;
    // 机器人转人工客服的FAQ， 目前有效
    private static final Long RG_FAQID = 20525576L;

    @Autowired
    private EnvService envService;
    @Autowired
    private TextPreprocessService textPreprocessService;
    @Autowired
    private RecallServiceWithEs8 recallServiceWithEs8;
    @Autowired
    private IntentServiceV2 intentServiceV2;
    @Autowired
    private IntentProductService intentProductService;
    @Autowired
    private ItemService itemService;
    @Autowired
    private UserService userService;
    @Autowired
    private BotApolloConfig botApolloConfig;
    @Autowired
    private KnowledgeService knowledgeService;
    @Autowired
    private GuideService guideService;
    @Autowired
    private ChatService chatService;
    @Autowired
    private ProphetManager prophetManager;
    @Autowired
    private SmartRGVisibleService smartRGVisibleManager;
    @Autowired
    private RedisService redisService;
    @Autowired
    private NlpServerService nlpServerService;

    @Autowired
    private ItemAttributeService itemAttributeService;

    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;

    @Autowired
    private FaqMatchPipelineService faqMatchPipelineService;

    public static boolean isReqParamValid(BotReq rb) {
        boolean ret = false;

        String sessionId = rb.getSessionId();

        if (sessionId == null) {
            return ret;
        }

        int type = rb.getType();

        if (type == BotReq.TYPE_TEXT) {
            String content = rb.getData();
            if (content != null && content.trim().length() > 0) {
                ret = true;
            }
        }

        if (type >= 0 && type <= 4) {
            ret = true;
        }

        return ret;
    }

    public BotResp pipeline(BotReq botReq) {
        // 目前订单卡片不处理
        if (StringUtils.isEmpty(botReq.getData()) && botReq.getItemId() == 0) {
            return BotResp.buildOnlyStateRslt(ReplyState.SUCCESS);
        }
        StatLogger statLogger = StatLoggerFactory.getLogger(LOG_MODULE);
        log.info("start pipeline");
        BotRespWithLog botRespWithLog = pipelineWithLog(botReq);
        // 如果只是单纯卡片，没有实际内容返回，也过滤
        if (StringUtils.startsWith(botReq.getData(), "id:") && (ObjectUtils.isEmpty(botRespWithLog.getBotResp().getFaqRslt()) || CollectionUtils.isEmpty(botRespWithLog.getBotResp().getFaqRslt().getFaqIds()))) {
            return BotResp.buildOnlyStateRslt(ReplyState.SUCCESS);
        }

        // 智能人工按钮 && 关键词
        // 都是后处理，附加控制是否出智能人工按钮和 修改faq人工结果
        // 会修改resp的内容

        smartRGVisible(botRespWithLog, botReq.getFaqIds(), botReq.getKfGroupId());

        // 通过faq是否命中人工，记录用户人工次数, 为智能转人工策略提供输入，不会修改resp的内容
        BotResp botResp = botRespWithLog.getBotResp();
        if (botResp != null) {
            log.debug("PipelineService.pipeline botResp: {}", botResp);
            recordSessionRG(botRespWithLog.getBotResp(), Long.parseLong(botReq.getSessionId()));
        }

        statLogger.log(LOG_TOPIC, MainPipelineLog.buildMap(botRespWithLog.getMainPipelineLog()));
        MainPipelineLog mainPipelineLog = botRespWithLog.getMainPipelineLog();

        // 记录pipelinelog到redis中
        if (mainPipelineLog != null) {
            BotContext botContext = mainPipelineLog.getContext();
            if (botContext != null) {
                List<MainPipelineLog> pipelineLogs = botContext.getPipelineLogs();
                // 如果历史为空，则新生成一个列表
                if (pipelineLogs == null) {
                    pipelineLogs = new ArrayList<>();
                }
                // 添加当前轮次的textbasicdata
                mainPipelineLog.genHstTextBasicData();
                // 为避免循环依赖
                mainPipelineLog.setContext(null);
                pipelineLogs.add(0, mainPipelineLog);
                botContext.setPipelineLogs(pipelineLogs.stream().limit(MAX_PIPELINE_NUM).collect(Collectors.toList()));
                String botContextStr = IoUtils.toJsonString(botContext);
                String contextKey = BotContext.buildContextKey(Long.parseLong(botReq.getSessionId()));
                log.info("reset context");
                redisService.set(contextKey, botContextStr, 30);
            }
        }

        return botRespWithLog.getBotResp();
    }

    public BotContext getBotContext(long sessionId, BotReq botReq) {

        BotContext botContext = getContextFromCache(sessionId);
        if (botReq.getUserId() != 0) {
            botContext.setUserId(botReq.getUserId());
        }
        // 更新卡片
        if (botReq.getItemId() != 0) {
            botContext.setItemId(botReq.getItemId());
        }
        // 卡片类型， data为空
        if (StringUtils.isEmpty(botReq.getData())) {
            botReq.setData("id:" + String.valueOf(botReq.getItemId() != 0 ? botReq.getItemId() : botContext.getOrderId()));
        }
        botContext.setSessionInteraction(1);
        botContext.setChannel(1);
        // 处理文本
        TextBasicData textBasicData = textPreprocessService.preprocess(botReq.getData(), false);
        botContext.setCurInput(textBasicData);
        return botContext;
    }

    public BotRespWithLog pipelineWithLog(BotReq botReq) {
        long sessionId = Long.parseLong(botReq.getSessionId());

        // 预处理
        BotContext botContext = getBotContext(sessionId, botReq);

        String cleanSen = botContext.getInputs().get(0).getCleanedText();

        log.info("traceLog {} {} botContext {}", sessionId, cleanSen, botContext);

        MainPipelineLog mainPipelineLog = new MainPipelineLog();
        mainPipelineLog.setInput(botReq);
        mainPipelineLog.setContext(botContext);

        // 目前观察很少触发，可以先删除
        // 判断是否订单推荐问
        if (botReq.isOrderQues()) {
            log.info("botContext orderRCMD: {}", botContext);
            List<Long> orderRcmdKnowledgeIds = prophetManager.orderRcmdQues(botContext, botReq.getOrderId(), botReq.getPackageId());
            log.info("traceLog {} {} {} orderRcmdFAQs {}", sessionId, botReq.getOrderId(), botReq.getPackageId(), orderRcmdKnowledgeIds);
            BotResp botResp = BotResp.buildOrderRcmdKnowledgeRslt(orderRcmdKnowledgeIds);
            mainPipelineLog.setContext(botContext);
            return new BotRespWithLog(botResp, mainPipelineLog);
        }

        // 参数强校验
        if (!isReqParamValid(botReq)) {
            BotResp botResp = BotResp.buildOnlyStateRslt(ReplyState.PARAM_ERROR);
            return new BotRespWithLog(botResp, mainPipelineLog);
        }
        // 输入text没有任何有效信息, 或者也不是商品卡片
        if (!CustomLangUtils.isValidStr(cleanSen) && botReq.getItemId() == 0 && botReq.getOrderId() == 0) {
            BotResp botResp = BotResp.buildTextUseLessRslt();
            return new BotRespWithLog(botResp, mainPipelineLog);
        }
        // 一级意图和人工意图等，分为 业务，商品，闲聊，导购，人工
        IntentRslt intentRslt = intentServiceV2.getIntentV2(cleanSen);
        // 核心意图, 业务导向，如要退货等
        List<CoreIntent> coreIntents = intentProductService.getCoreIntent(cleanSen, sessionId);
        intentRslt.setCoreIntents(coreIntents);
        log.info("traceLog {} {} coreIntents {}", sessionId, cleanSen, intentRslt);

        // 记录当前session的意图, 专门给人工按钮使用
        recordSessionIntent(coreIntents, sessionId);

        // 闲聊
        // 午安，早安，晚安，逆向情绪，结束语，打招呼，感谢
        ChatType chatType = chatService.getChitChatRes(true, cleanSen);
        if (chatType.isChat()) {
            log.info("bot chat {} {}", cleanSen, chatType);
            BotResp botResp = BotResp.buildTextChitClassifyRslt(chatType.getChatResp(), chatType.getChatName());
            return new BotRespWithLog(botResp, mainPipelineLog);
        }

        // FAQ 匹配
        KnowledgeMatchResp knowledgeMatchResp = faqMatchPipelineService.processWithSimpleModel(botContext);
        log.info("traceLog {} {} knowledgeMatchResp {}", sessionId, cleanSen, knowledgeMatchResp);

        FaqIdRslt faqIdResult = transformFromKnowledgeMatchResp(knowledgeMatchResp);
        log.info("traceLog {} {} faqIdResult {}", sessionId, cleanSen, faqIdResult);

        // 综合FAQ 和 意图的结果
        FaqIdRslt combinedFaqIdResult = combine(intentRslt, faqIdResult, botContext);

        log.info("traceLog {} {} combinedFaqIdResult {}", sessionId, cleanSen, combinedFaqIdResult);

        if (StringUtils.isNotEmpty(botReq.getMessageId()) || StringUtils.isNotEmpty(botReq.getSessionId())) {
            // 优先取消息粒度
            MultiChannelPipelineLog multiChannelPipelineLog = redisService.getObj(StringUtils.join(RedisKey.MSG_2_PIPELINE_LOG, botReq.getMessageId()), new TypeReference<MultiChannelPipelineLog>() {
            });
            // 没有再取会话粒度的
            if (multiChannelPipelineLog == null) {
                multiChannelPipelineLog = redisService.getObj(StringUtils.join(RedisKey.SESSION_2_PIPELINE_LOG, botReq.getSessionId()), new TypeReference<MultiChannelPipelineLog>() {
                });
            }
            // 替换已有的数据
            if (multiChannelPipelineLog != null) {
                MainPipelineLog mainPipelineLog1 = transfer(multiChannelPipelineLog);
                combinedFaqIdResult = mainPipelineLog1.getFaqCombine();
                intentRslt = mainPipelineLog1.getIntent();
                KbqaResp kbqaResp = mainPipelineLog1.getKbqa();
                if (kbqaResp != null) {
                    mainPipelineLog.setKbqa(kbqaResp);
                }
                log.info("replace mainPipelineLog {}", IoUtils.toJsonString(mainPipelineLog));
            }
        }

        mainPipelineLog.setFaqCombine(combinedFaqIdResult);
        mainPipelineLog.setMatch(knowledgeMatchResp);
        mainPipelineLog.setIntent(intentRslt);

        BotResp botResp = null;
        // FAQ结果
        if (combinedFaqIdResult.isNotNull()) {
            if (combinedFaqIdResult.getFaqIds().size() == 0) {
                botResp = BotResp.buildTextUseLessRslt();
            } else {
                botResp = BotResp.buildFaqIdRslt(combinedFaqIdResult, intentRslt);
            }
        }

        log.info("check botResp {}", IoUtils.toJsonString(botResp));

        // 普通商品问答
        if (botContext.getItemId() != 0 && Level1IntentType.checkCommonIntent(intentRslt.getFirstIntent())) {
            // 商品问答
            // 取缓存
            log.info("bot similarityForAttr");
            if (mainPipelineLog.getKbqa() != null) {
                KbqaResp kbqaResp = mainPipelineLog.getKbqa();
                if (StringUtils.isNotEmpty(kbqaResp.getShowText())) {
                    log.info("use cache kbqa");
                    botResp = BotResp.buildTextRslt(kbqaResp.getShowText(), StatType.TEXT_PIC_KBQA, intentRslt, false);
                }
            }
            // 避免KBQA 把 FAQ 覆盖了
            else if (CollectionUtils.isNotEmpty(combinedFaqIdResult.getFaqIds())){

            } else {
                List<ChannelQaRslt> channelQaRslt = itemAttributeService.similarityForAttr(cleanSen, botContext.getItemId(), 0.95);
                if (CollectionUtils.isNotEmpty(channelQaRslt)) {
                    String showQuestion = channelQaRslt.get(0).getShowQuestion();
                    String showAnswer = channelQaRslt.get(0).getShowAnswer();
                    if (StringUtils.isNotEmpty(showAnswer)) {
                        log.info("knowledgeManagerV3 hit {} {} {} {} {}", sessionId, cleanSen, botContext.getItemId(), showQuestion, showAnswer);
                        showAnswer = showQuestion + ":" + showAnswer;
                        KbqaResp kbqaResp = new KbqaResp(showAnswer, KbqaType.GOODS_PROPERTY, channelQaRslt.get(0).getScore());
                        mainPipelineLog.setKbqa(kbqaResp);
                        botResp = BotResp.buildTextRslt(showAnswer, StatType.TEXT_PIC_KBQA, intentRslt, false);
                    }
                }
            }
        }

        if (Level1IntentType.FAQ.equals(intentRslt.getFirstIntent()) && botResp == null) {
            botResp = BotResp.buildTextWorkRelatedRslt(true);
        }
        /**
         * 导购 - 用户触发、被动推荐
         * 触发条件：
         * 导购 & 机器人来源 & 导购开 & (售前 | 全) & (abt | 白名单)
         * -----------------------------------------------------
         * 导购 - 主动推荐
         * 触发条件：
         * 1) 售前、机器人来源
         * 2) 对话K轮后
         * 3) 精准回复后
         * 4) 非人工
         */
        // 被动推荐
        boolean isGuide = Level1IntentType.GUIDE.equals(intentRslt.getFirstIntent())
                && botApolloConfig.isGuideEnable()
                && (botContext.isPreSale() || botApolloConfig.isGuideAftersaleEnable())
                && (botApolloConfig.checkAbt(String.valueOf(botContext.getSessionId()), botApolloConfig.getAbtGuideSidSuffix())
                || botApolloConfig.getWhiteUserIdList().contains(String.valueOf(botContext.getUserId())));
        boolean guidePassive = false;

        // 可以被降级
        if (isGuide) {
            log.info("jump into guide.");
            GuideReq guideReq = new GuideReq(String.valueOf(botContext.getSessionId()), cleanSen,
                    String.valueOf(botContext.getUserId()));
            GuideRCMDListRslt guideRsltList = guideService.getPassiveRes(guideReq);
            log.info("traceLog {} {} guideResp {}", botContext.getSessionId(), botReq.getData(), guideRsltList);
            if (!GuideService.NULL_RCMD_ITEMID.equals(guideRsltList.getGuideRsltList().get(0).getItemId())) {
                if (ObjectUtils.isEmpty(botResp)) {
                    botResp = BotResp.buildOnlyStateRslt(ReplyState.SUCCESS);
                }
                botResp.setGuideRslt(guideRsltList.getGuideRsltList());
                botResp.setGuideRcmdReason(guideRsltList.getRcmdReason());
                botResp.resetFAQRslt();
                guidePassive = true;
            }
        }

        log.info("botResp {}", botResp);

        // 兜底策略, 这里是无意义的结果
        if (ObjectUtils.isEmpty(botResp)) {
            botResp = BotResp.buildTextWorkRelatedRslt(false);
            return new BotRespWithLog(botResp, mainPipelineLog);
        }

        // pro会员0元购提醒
        // 可以被降级
        if ((botApolloConfig.isGuideProZeroActivityEnable()
                || botApolloConfig.getWhiteUserIdList().contains(String.valueOf(botContext.getUserId())))
                && !guidePassive) {
            log.info("getProZeroActivity jump into.");
            GuideRCMDListRslt guideRslt = guideService.getProZeroActivity(botContext);
            if (guideRslt != null && !GuideService.NULL_RCMD_ITEMID.equals(guideRslt.getGuideRsltList().get(0).getItemId())) {
                botResp.genProZeroActiveRslt(guideRslt);
                return new BotRespWithLog(botResp, mainPipelineLog);
            }
        }

        // 主动推荐
        // 1.开关 2.机器人 3.ABT|白名单 4. session内推荐指定次数（默认1次推荐）5.低K轮后开始推荐(默认3) || 精准匹配推荐&& 非SOP答案
        boolean isActiveGuide = botApolloConfig.isGuideActiveEnable()
                && (botApolloConfig.checkAbt(String.valueOf(botContext.getSessionId()), botApolloConfig.getAbtGuideActiveSidSuffix())
                || botApolloConfig.getWhiteUserIdList().contains(String.valueOf(botContext.getUserId())))
                && (botApolloConfig.isGuideActiveRuleRcmdCountEnable(botContext.getActiveRcmdCount()))
                && (botApolloConfig.isGuideActiveTurnCount(botContext.getTurnCount())
                || (botApolloConfig.isGuideActiveRulePrecisionEnable() && combinedFaqIdResult.getType() == FaqIdRslt.TYPE_FAQ_PRECISE &&
                combinedFaqIdResult.getFaqIds().size() > 0

                // TODO 需要修改为使用ES，或者返回的answerType直接判断
//                && !knowledgeService.isSOPAnswer(combinedFaqIdResult.getFaqIds().get(0))
                ));
        // 人工不推
        if (!CollectionUtils.isEmpty(combinedFaqIdResult.getFaqIds())) {
            isActiveGuide = isActiveGuide
                    && (combinedFaqIdResult.getType() == FaqIdRslt.TYPE_FAQ_PRECISE
                    && !combinedFaqIdResult.getFaqIds().get(0).equals(RG_FAQID));
        }

        // 被动推荐后不推
        isActiveGuide = isActiveGuide && (!guidePassive);

        // 可以被降级
        if (isActiveGuide || (envService.isTest() && StringUtils.equals("纯商品推荐", cleanSen))) {
            log.info("jump into active guide.");

            // 用户的商品卡片不推
            String itemId = null;
            if (!CollectionUtils.isEmpty(botContext.getCardItemIds())) {
                itemId = String.valueOf(botContext.getCardItemIds().get(0));
            } else if (guideService.checkItemIdValid(String.valueOf(botContext.getItemId()))) {
                itemId = String.valueOf(botContext.getItemId());
            }
            guideService.uploadItemIdShowList(String.valueOf(botContext.getUserId()), itemId);

            GuideReq guideReq = new GuideReq(String.valueOf(botContext.getSessionId()), cleanSen,
                    String.valueOf(botContext.getUserId()), itemId);
            GuideRCMDListRslt guideRsltList = guideService.getActiveRes(guideReq);
            log.info("traceLog {} {} guideActiveResp {}", botContext.getSessionId(), botReq.getData(), guideRsltList);
            if (!GuideService.NULL_RCMD_ITEMID.equals(guideRsltList.getGuideRsltList().get(0).getItemId())) {
                botResp.setGuideRslt(guideRsltList.getGuideRsltList());
                botResp.setGuideRcmdReason(guideRsltList.getRcmdReason());
                botContext.setActiveRcmdCount(botContext.getActiveRcmdCount() + 1);
                mainPipelineLog.setContext(botContext);
            }
        }

        return new BotRespWithLog(botResp, mainPipelineLog);
    }

    private MainPipelineLog transfer(MultiChannelPipelineLog multiChannelPipelineLog) {
        ChannelQaResp channelQaResp = multiChannelPipelineLog.getOutput();
        log.info("multiChannelPipelineLog {}", IoUtils.toJsonString(channelQaResp));

        MainPipelineLog mainPipelineLog = new MainPipelineLog();
        ChannelQaResp output = multiChannelPipelineLog.getOutput();

        IntentTypeV2 intentTypeV2 = output.getIntentTypeV2();
        List<CoreIntent> coreIntents = output.getCoreIntents();
        List<ChannelQaRslt> channelQaRslts = output.getFaqList();

        IntentRslt intentRslt = new IntentRslt(Level1IntentType.getFromName(intentTypeV2.name()), null, coreIntents);
        mainPipelineLog.setIntent(intentRslt);

        FaqIdRslt faqIdRslt = new FaqIdRslt();
        KnowledgeMatchResp knowledgeMatchResp = new KnowledgeMatchResp();

        mainPipelineLog.setFaqCombine(faqIdRslt);
        double faqScore = 0;
        if (CollectionUtils.isNotEmpty(channelQaRslts)) {
            List<ChannelQaRslt> faqChannelQaRslt = channelQaRslts.stream().filter(x -> x.getKnowledgeSource() == 1).collect(Collectors.toList());
            List<ChannelQaRslt> kbqaChannelQaRslt = channelQaRslts.stream().filter(x -> x.getKnowledgeSource() == 2).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(faqChannelQaRslt)) {
                faqScore = faqChannelQaRslt.get(0).getScore();
                List<Long> knowledgeIds = faqChannelQaRslt.stream().map(ChannelQaRslt::getKnowledgeId).collect(Collectors.toList());
                List<String> quesList = faqChannelQaRslt.stream().map(ChannelQaRslt::getQuestion).collect(Collectors.toList());
                List<Double> scores = faqChannelQaRslt.stream().map(ChannelQaRslt::getScore).collect(Collectors.toList());

                faqIdRslt.setType(knowledgeIds.size() == 1 && scores.get(0) > channelQaApolloConfig.getExactThreshold() ? FaqIdRslt.TYPE_FAQ_PRECISE : FaqIdRslt.TYPE_FAQ_FUZZY);
                faqIdRslt.setFaqIds(knowledgeIds);
                faqIdRslt.setFaqQues(quesList);
                faqIdRslt.setScores(scores);
                mainPipelineLog.setFaqCombine(faqIdRslt);
            }

            // 如果第一名是FAQ，则不再展示KBQA
            if (CollectionUtils.isNotEmpty(kbqaChannelQaRslt) && kbqaChannelQaRslt.get(0).getScore() > faqScore) {
                // 机器人的商品属性过滤链接类型的结果
                if (StringUtils.contains(kbqaChannelQaRslt.get(0).getShowAnswer(), "http")) {
                    log.info("ignore property {}", kbqaChannelQaRslt.get(0).getShowAnswer());
                } else {
                    KbqaResp kbqaResp = new KbqaResp(kbqaChannelQaRslt.get(0).getShowQuestion() + ":" + kbqaChannelQaRslt.get(0).getShowAnswer(), KbqaType.GOODS_PROPERTY, kbqaChannelQaRslt.get(0).getScore());
                    mainPipelineLog.setKbqa(kbqaResp);
                }
            }
        }
        return mainPipelineLog;
    }

    private FaqIdRslt combine(IntentRslt intentRslt, FaqIdRslt faqIdResultFromMatch, BotContext botContext) {

        try {

            // 如果faq中的结果是完全匹配，则仅返回完全匹配的faqid
            if ((faqIdResultFromMatch != null) && (FaqIdRslt.TYPE_FAQ_PRECISE == faqIdResultFromMatch.getType()) && (faqIdResultFromMatch.getScores().size() > 0)
                    && (faqIdResultFromMatch.getScores().get(0) == 1.0)) {
                return faqIdResultFromMatch;
            }

            // 取出意图里的结果
            List<SimpleKnowledge> strongKnowledgesFromIntent = new ArrayList<>();
            List<SimpleKnowledge> medianKnowledgesFromIntent = new ArrayList<>();

            List<CoreIntent> coreIntents = intentRslt.getCoreIntents();
            if (coreIntents != null && coreIntents.size() > 0) {

                for (CoreIntent coreIntent : coreIntents) {
                    String intentEn = coreIntent.getIntent();
                    Map<String, Long> intentKnowledgeMap = botApolloConfig.getIntentCoreKnowledgeMap();
                    if (!intentKnowledgeMap.containsKey(intentEn)) {
                        log.warn("combine not in intentKnowledgeMap {}", intentEn);
                        continue;
                    }
                    long intentKnowledgeId = intentKnowledgeMap.get(intentEn);
                    Knowledge knowledge = knowledgeService.getKnowledgeById(intentKnowledgeId);
                    // 该知识有效
                    if (knowledge != null) {
                        if (coreIntent.isStrongConfidence()) {
                            strongKnowledgesFromIntent.add(new SimpleKnowledge(intentKnowledgeId, knowledge.getStdQuestion(), 0.95));
                        } else {
                            medianKnowledgesFromIntent.add(new SimpleKnowledge(intentKnowledgeId, knowledge.getStdQuestion(), 0.75));
                        }
                        coreIntent.setKnowledgeId(intentKnowledgeId);
                    }
                }
            }

            log.info("strongKnowledgesFromIntent {}", strongKnowledgesFromIntent);
            log.info("medianKnowledgesFromIntent {}", medianKnowledgesFromIntent);

            List<SimpleKnowledge> strongKnowledgesFromMatch = new ArrayList<>();
            List<SimpleKnowledge> medianKnowledgesFromMatch = new ArrayList<>();

            if (faqIdResultFromMatch != null && faqIdResultFromMatch.getFaqIds() != null) {
                for (int i = 0; i < faqIdResultFromMatch.getFaqIds().size(); i++) {
                    long knowledgeId = faqIdResultFromMatch.getFaqIds().get(i);
                    double score = faqIdResultFromMatch.getScores().get(i);
                    String content = faqIdResultFromMatch.getFaqQues().get(i);

                    double exactThreshold = botApolloConfig.checkAbt(String.valueOf(botContext.getSessionId()), botApolloConfig.getAbtFaqSessionIdSuffix())
                            && botApolloConfig.getWhiteUserIdList().contains(String.valueOf(botContext.getUserId()))
                            ? botApolloConfig.getAbtMatchExactThreshold() : botApolloConfig.getMatchExactThreshold();

                    if (score >= exactThreshold) {
                        strongKnowledgesFromMatch.add(new SimpleKnowledge(knowledgeId, content, score));
                    } else {
                        medianKnowledgesFromMatch.add(new SimpleKnowledge(knowledgeId, content, score));
                    }
                }
            }

            FaqIdRslt combineFaqIdResult = new FaqIdRslt();
            combineFaqIdResult.setState(ReplyState.SUCCESS);

            // 如果返回的是唯一的strong意图，直接返回
            if (strongKnowledgesFromIntent.size() == 1 && strongKnowledgesFromMatch.size() == 0) {
                SimpleKnowledge simpleKnowledge = strongKnowledgesFromIntent.get(0);
                combineFaqIdResult.setFaqIds(new ArrayList<Long>() {{
                    add(simpleKnowledge.getKnowledgeId());
                }});
                combineFaqIdResult.setScores(new ArrayList<Double>() {{
                    add(simpleKnowledge.getScore());
                }});
                combineFaqIdResult.setFaqQues(new ArrayList<String>() {{
                    add(simpleKnowledge.getContent());
                }});
                combineFaqIdResult.setType(FaqIdRslt.TYPE_FAQ_PRECISE);
            } else {
                List<SimpleKnowledge> total = new ArrayList<>();
                total.addAll(strongKnowledgesFromMatch);
                total.addAll(medianKnowledgesFromMatch);

                Set<Long> idSet = new HashSet<>();
                idSet.addAll(strongKnowledgesFromMatch.stream().map(SimpleKnowledge::getKnowledgeId).collect(Collectors.toList()));
                idSet.addAll(medianKnowledgesFromMatch.stream().map(SimpleKnowledge::getKnowledgeId).collect(Collectors.toList()));

                List<SimpleKnowledge> strongTotal = new ArrayList<>(strongKnowledgesFromMatch);

                for (SimpleKnowledge simpleKnowledge : strongKnowledgesFromIntent) {
                    if (!idSet.contains(simpleKnowledge.getKnowledgeId())) {
                        total.add(simpleKnowledge);
                        idSet.add(simpleKnowledge.getKnowledgeId());
                        strongTotal.add(simpleKnowledge);
                    }
                }

                for (SimpleKnowledge simpleKnowledge : medianKnowledgesFromIntent) {
                    if (!idSet.contains(simpleKnowledge.getKnowledgeId())) {
                        total.add(simpleKnowledge);
                        idSet.add(simpleKnowledge.getKnowledgeId());
                    }
                }

                Collections.sort(total);
                Collections.sort(strongTotal);

                if (total.size() == 0) {
                    combineFaqIdResult.setType(FaqIdRslt.TYPE_NO_ANSWER);
                } else {

                    // 唯一的一个来自匹配的strong
                    if (strongTotal.size() == 1) {
                        combineFaqIdResult.setFaqIds(strongTotal.stream().map(SimpleKnowledge::getKnowledgeId).collect(Collectors.toList()));
                        combineFaqIdResult.setScores(strongTotal.stream().map(SimpleKnowledge::getScore).collect(Collectors.toList()));
                        combineFaqIdResult.setFaqQues(strongTotal.stream().map(SimpleKnowledge::getContent).collect(Collectors.toList()));
                        combineFaqIdResult.setType(FaqIdRslt.TYPE_FAQ_PRECISE);
                    } else {
                        combineFaqIdResult.setFaqIds(total.stream().map(SimpleKnowledge::getKnowledgeId).collect(Collectors.toList()));
                        combineFaqIdResult.setScores(total.stream().map(SimpleKnowledge::getScore).collect(Collectors.toList()));
                        combineFaqIdResult.setFaqQues(total.stream().map(SimpleKnowledge::getContent).collect(Collectors.toList()));
                        combineFaqIdResult.setType(FaqIdRslt.TYPE_FAQ_FUZZY);
                    }
                }

            }

            return combineFaqIdResult;
        } catch (Exception e) {
            log.error("combine", e);
        }
        return faqIdResultFromMatch;
    }

    /**
     * 获取商品卡片产生的itemId上下文
     *
     * @param sessionId
     * @return
     */
    private long getContextItemId(String sessionId) {
        String itemIdKey = BotContext.buildItemIdContextKey(sessionId);
        long itemId = 0;
        try {
            String value = redisService.get(itemIdKey);
            itemId = BotContext.parseItemId(value);
            log.info("getContextItemId {} {}", itemIdKey, value);
        } catch (Exception e) {
            log.error("getContextItemId", e);
        }
        return itemId;
    }

    private FaqIdRslt transformFromKnowledgeMatchResp(KnowledgeMatchResp knowledgeMatchResp) {
        FaqIdRslt faqIdResult = new FaqIdRslt();
        switch (knowledgeMatchResp.getMatchType()) {
            case KnowledgeMatchResp.TYPE_NO_ANSWER:
                break;
            case KnowledgeMatchResp.TYPE_FAQ_FUZZY:
            case KnowledgeMatchResp.TYPE_FAQ_PRECISE:
                faqIdResult.setType(knowledgeMatchResp.getMatchType());
                faqIdResult.setFaqQues(knowledgeMatchResp.getMatchRslts().stream().map(KnowledgeMatchRslt::getQuestion).collect(Collectors.toList()));
                faqIdResult.setFaqIds(knowledgeMatchResp.getMatchRslts().stream().map(KnowledgeMatchRslt::getKnowledgeId).collect(Collectors.toList()));
                faqIdResult.setScores(knowledgeMatchResp.getMatchRslts().stream().map(KnowledgeMatchRslt::getScore).collect(Collectors.toList()));
                faqIdResult.setState(ReplyState.SUCCESS);
            default:
                break;
        }
        return faqIdResult;
    }

    public BotContext getContextFromCache(long sessionId) {
        String contextKey = BotContext.buildContextKey(sessionId);
        BotContext botContext = null;
        try {
            String value = redisService.get(contextKey);
            log.info("getContext {} {}", sessionId, value);
            if (value != null) {
                botContext = IoUtils.parseJson(value, new TypeReference<BotContext>() {
                });

                // 这里后面要下线
                // 使用最实时的来自商品卡片的itemIds
                String itemIdKey = BotContext.buildItemIdContextKey(sessionId);
                String itemIdValue = redisService.get(itemIdKey);
                if (itemIdValue != null) {
                    List<Long> carditemIds = BotContext.parseItemIds(itemIdValue);
                    botContext.setCardItemIds(carditemIds);
                    botContext.setCardItems(itemService.getItemsByIds(carditemIds));
                }
            }
        } catch (Exception e) {
            log.error("getShortcutContext", e);
        }
        // 如果取不到，则尝试取单个商品卡片的id，生成一个最基础的BotContext
        if (botContext == null) {
            log.info("botContext is null");
            long itemId = getContextItemId(String.valueOf(sessionId));
            Item item = itemService.getItemById(itemId);
            botContext = BotContext.builder().sessionId(sessionId).itemId(itemId).item(item).build();
        }

        // 设置对话轮数
        String turnCountKey = BotContext.buildTurnCountContextKey(sessionId);
        String turnCount = redisService.get(turnCountKey);
        int currentTurnCount = Integer.parseInt(BotContext.buildTurnCountContextVal(turnCount));
        botContext.setTurnCount(currentTurnCount);
        log.debug("botContext turn count: {}", botContext.getTurnCount());

        return botContext;
    }

    private void recordSessionIntent(List<CoreIntent> coreIntents, long sessionId) {
        // 记录当前会话中出现的意图
        String sessIntentKey = BotContext.buildRequestIntentKey(sessionId);
        String sessIntents = redisService.get(sessIntentKey);
        String intentFaqIds = coreIntents.stream().map(CoreIntent::getIntent).filter(x -> !"other".equals(x)).collect(Collectors.joining(","));
        if (intentFaqIds.length() < 1) {
            return;
        }
        if (sessIntents == null) {
            redisService.set(sessIntentKey, intentFaqIds, 30);
        } else {
            redisService.set(sessIntentKey, sessIntents + "," + intentFaqIds, 30);
        }
    }

    private void recordSessionRG(BotResp botResp, long sessionId) {
        if (botResp.getFaqRslt() != null
                && (botResp.getFaqRslt().getType() == FaqIdRslt.TYPE_FAQ_PRECISE)
                && (CollectionUtils.isNotEmpty(botResp.getFaqRslt().getFaqIds()))
                && (botResp.getFaqRslt().getFaqIds().get(0).equals(RG_FAQID))) {

            String sessRGKey = BotContext.buildRequestRGKey(sessionId);
            String sessRG = redisService.get(sessRGKey);
            if (sessRG == null) {
                redisService.set(sessRGKey, "1", 30);
            } else {
                long rgCount = Long.parseLong(sessRG) + 1;
                redisService.set(sessRGKey, Long.toString(rgCount), 30);
            }
        }
    }

    private void smartRGVisible(BotRespWithLog botRespWithLog, List<Long> clickFaqIds, Long kfGroupId) {
        String smartVisibleRGKey = BotContext.buildRequestVisibleRGKey(botRespWithLog.getMainPipelineLog().getContext().getSessionId());
        String smartVisibleRGResult = redisService.get(smartVisibleRGKey);

        if (smartVisibleRGResult == null) {

            // 测试环境mock数据
            if (envService.isTest() && ("测试人工按钮".equals(botRespWithLog.getMainPipelineLog().getContext().getInputs().get(0).getCleanedText()) ||
                    "我要换货".equals(botRespWithLog.getMainPipelineLog().getContext().getInputs().get(0).getCleanedText()))) {
                botRespWithLog.getBotResp().setRGVisible(true);
                redisService.set(smartVisibleRGKey, "1", 30);
                return;
            }

            if (botApolloConfig.isSmartRGVisibleEnable()) {
                boolean rgVisible = smartRGVisibleManager.getVisible(botRespWithLog.getMainPipelineLog().getOutput(), botRespWithLog.getMainPipelineLog().getContext(), clickFaqIds, kfGroupId);
                if (rgVisible) {
                    redisService.set(smartVisibleRGKey, "1", 30);
                }
                botRespWithLog.getBotResp().setRGVisible(rgVisible);
            } else {
                botRespWithLog.getBotResp().setRGVisible(false);
            }
        } else {
            botRespWithLog.getBotResp().setRGVisible(false);
        }
    }

    public boolean checkTransferRg(long sessionId) {
        BotContext botContext = getContextFromCache(sessionId);
        if (ObjectUtils.isEmpty(botContext) || CollectionUtils.isEmpty(botContext.getPipelineLogs())) {
            return false;
        }
        for (MainPipelineLog mainPipelineLog : botContext.getPipelineLogs()) {
            if (ObjectUtils.isNotEmpty(mainPipelineLog.getOutput())) {
                boolean checkFlag = containPreciseFaq(mainPipelineLog.getOutput().getFaqRslt());
                if (checkFlag) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean containPreciseFaq(FaqIdRslt faqIdRslt) {
        if (ObjectUtils.isNotEmpty(faqIdRslt)) {
            // 有精确的业务问题
            if (faqIdRslt.getType() == 1) {
                return true;
            }
            // 结果里前3位有人工或者高分
            if (CollectionUtils.isNotEmpty(faqIdRslt.getFaqIds())) {
                for (int i = 0; i < faqIdRslt.getFaqIds().size(); i++) {
                    if (i > 2) {
                        break;
                    }
                    if (RG_FAQID.equals(faqIdRslt.getFaqIds().get(i)) || faqIdRslt.getScores().get(i) > botApolloConfig.getFaq2ChatThreshold()) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
}