package com.netease.yx.bot.biz.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.biz.common.NlpServerService;
import com.netease.yx.bot.biz.common.SmartWorkService;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.chat.ChatClassifyReq;
import com.netease.yx.bot.core.model.entity.chat.ChatClassifyTFResp;
import com.netease.yx.bot.core.model.entity.chat.ChatType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Service
@Slf4j
public class ChatService {

    public static final String CHAT_COMPLAINT = "逆向情绪";
    private static final String OTHER = "其它";
    private static final Random random = new Random();

    private static final String MODEL_NAME = "chat-update-focal-loss-sm-op";
    private static final String ROLE_SYSTEM = "system";
    private static final String ROLE_USER = "user";
    private static final String ROLE_ASSISTANT = "assistant";
    private static final String EXTRA_REQ_PROMPT = "prompt";
    private static final String EXTRA_REQ_INSTANCE_ROLE = "role";

    private static final ChatType CHAT_OTHER_TYPE = new ChatType(false, "请稍等，小选帮您查一下哦~", "其它");
    private static final String FINISH_REPLY_TEXT = "如您暂无其他问题，小选先离开去为其他用户服务咯~祝您生活愉快~";

    @Value("${chat.classify.rule}")
    private String chatClassifyRulePath;

    @Value("${chat.resp.path}")
    private String chatRespPath;

    @Value("${chat.rg.resp.path}")
    private String chatRGRespPath;

    private Map<String, List<String>> ChatResp;
    private Map<String, List<String>> ChatRGResp;
    private Map<String, String> chatRuleStrMap;
    private Map<String, Pattern> chatRuleMap = new HashMap<>();

    @Autowired
    private IntentProductService intentProductService;
    @Autowired
    private NlpServerService nlpServerService;

    @Autowired
    private SmartWorkService smartWorkService;

    @PostConstruct
    private void init() {
        try {
            ChatResp = IoUtils.loadJsonFromFile(chatRespPath, new TypeReference<Map<String, List<String>>>() {
            });
            ChatRGResp = IoUtils.loadJsonFromFile(chatRGRespPath, new TypeReference<Map<String, List<String>>>() {
            });

            chatRuleStrMap = IoUtils.loadJsonFromFile(chatClassifyRulePath, new TypeReference<Map<String, String>>() {
            });

            for (Map.Entry<String, String> entry : chatRuleStrMap.entrySet()) {
                Pattern pattern = Pattern.compile(entry.getValue());
                chatRuleMap.put(entry.getKey(), pattern);
            }
            log.info("ChitChatClassifyManager.init {}", chatRuleMap);
        } catch (Exception e) {
            log.error("load chatresp error", e);
        }
    }

    public ChatType getChitChatRes(boolean isRobot, String sen) {
        if (StringUtils.equals("rg", sen)) {
            return CHAT_OTHER_TYPE;
        }

        try {
            ChatClassifyTFResp chatClassifyTFResp = smartWorkService.httpStandardSingleReq(MODEL_NAME, intentProductService.tokenizer(intentProductService.clean(sen)), new TypeReference<ChatClassifyTFResp>() {
            });

            String chatModelRes = chatClassifyTFResp.getChat();
            String chatRuleRes = rule(sen);
            if (!OTHER.equals(chatRuleRes)) {
                chatModelRes = chatRuleRes;
            }
            List<String> respList;
            if (isRobot) {
                respList = ChatResp.get(chatModelRes);
            } else {
                respList = ChatRGResp.get(chatModelRes);
            }

            if (CollectionUtils.isEmpty(respList)) {
                return CHAT_OTHER_TYPE;
            }
            String resp = respList.get(random.nextInt(respList.size()));
            return new ChatType(true, resp, chatModelRes);
        } catch (Exception e) {
            log.error("error in chatClassifyManager", e);
        }

        return CHAT_OTHER_TYPE;
    }

    private String rule(String sen) {

        for (Map.Entry<String, Pattern> entry : chatRuleMap.entrySet()) {
            Matcher matcher = entry.getValue().matcher(sen);
            boolean isMatch = matcher.matches();
            if (isMatch) {
                return entry.getKey();
            }
        }

        return OTHER;
    }

    public ChatType getChitChatSmartHostRes(ChatClassifyReq classifyReq) {
        ChatType chatType = getChitChatRes(false, classifyReq.getSen());
        if (chatType.isChat() & (ChatType.FINAL_CATEGORY.equals(chatType.getChatName()) || ChatType.THANK_CATEGORY.equals(chatType.getChatName()))) {
            chatType.setChatResp(FINISH_REPLY_TEXT);
        }

        return chatType;
    }
}
