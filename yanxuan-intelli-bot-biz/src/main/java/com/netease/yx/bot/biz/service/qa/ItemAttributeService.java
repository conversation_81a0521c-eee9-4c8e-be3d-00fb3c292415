package com.netease.yx.bot.biz.service.qa;

import com.netease.yx.bot.biz.common.NlpServerService;
import com.netease.yx.bot.core.model.entity.attr.AttrResp;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaRslt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ItemAttributeService {
    @Autowired
    private NlpServerService nlpServerService;

    public List<ChannelQaRslt> similarityForAttr(String input, long itemId, double threshold) {
        log.info("similarityForAttr");
        if (StringUtils.equals(input, "URL")) {
            return new ArrayList<>();
        }

        List<AttrResp> attrResps = nlpServerService.getAttr(input, itemId);

        List<ChannelQaRslt> channelQaRslts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attrResps)) {
            for (AttrResp attrResp : attrResps) {
                channelQaRslts.add(new ChannelQaRslt(itemId, attrResp.getAttrId(), attrResp.getAttrName(), attrResp.getValueIds().get(0), attrResp.getAttrValues().get(0), attrResp.getSource(), attrResp.getRankScoreFromSimilar()));
            }
        }

        return channelQaRslts;
    }
}
