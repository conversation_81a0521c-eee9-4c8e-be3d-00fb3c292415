/**
 * @(#)CheckManualService.java, 2020/5/14.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.manager;

import com.netease.yx.bot.biz.service.IntentServiceV2;
import com.netease.yx.bot.core.model.constant.Level1IntentType;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> @ corp.netease.com)
 * 检查某句话是否是想转人工
 */
@Service
@Slf4j
public class CheckManualManager {
    private static final Set<String> CONTAIN_KEY_WORDS = new HashSet<String>() {{
        add("人工");
        add("真人");
    }};
    private static final Set<String> EQUAL_KEY_WORDS = new HashSet<String>() {{
        add("rg");
    }};
    @Autowired
    private IntentServiceV2 intentServiceV2;

    public Boolean checkManual(@RequestParam(value = "query") String query) {
        boolean checkRslt = false;

        if (EQUAL_KEY_WORDS.contains(query.toLowerCase())) {
            checkRslt = true;
        }

        for (String keyWord : CONTAIN_KEY_WORDS) {
            if (query.contains(keyWord)) {
                checkRslt = true;
                break;
            }
        }
        if(!checkRslt){
            IntentRslt intentRslt = intentServiceV2.getIntentV2(query);

            if (Level1IntentType.SPECIAL.equals(intentRslt.getFirstIntent())) {
                checkRslt = true;
            }
        }

        return checkRslt;
    }
}