package com.netease.yx.bot.biz.manager;

import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.service.TextPreprocessService;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.biz.service.qa.FaqMatchPipelineService;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.KnowledgeMatchResp;
import com.netease.yx.bot.core.model.entity.check.CheckKnowledge;
import com.netease.yx.bot.core.model.entity.check.CheckResp;
import com.netease.yx.bot.core.model.entity.check.CheckSimilarKnowledgeReq;
import com.netease.yx.bot.core.model.entity.check.NeedCheckKnowledge;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 运营在后台维护知识的时候，保存前都会进行知识重复检测
 * 复用了QA的接口
 */
@Service
@Slf4j
public class CheckKnowledgeManager {

    @Autowired
    private BotApolloConfig botApolloConfig;

    @Autowired
    private TextPreprocessService preprocessManager;

    @Autowired
    private EnvService envService;

    @Autowired
    private TestMockManager testMockManager;

    @Autowired
    private FaqMatchPipelineService faqMatchPipelineService;

    public CheckResp process(CheckSimilarKnowledgeReq checkSimilarKnowledgeReq) {
        if (envService.isTest()) {
            return testMockManager.mockCheckSimilarKnowledgeResp(checkSimilarKnowledgeReq);
        }
        Map<Long, List<CheckKnowledge>> checkMap = new HashMap<>();

        List<NeedCheckKnowledge> needCheckKnowledges = new ArrayList<>(checkSimilarKnowledgeReq.getNeedCheckKnowledgeMap().values());

        List<KnowledgeMatchResp> KnowledgeMatchResps = needCheckKnowledges.parallelStream().map(this::check).collect(Collectors.toList());

        for (int i = 0; i < needCheckKnowledges.size(); i++) {
            NeedCheckKnowledge needCheckKnowledge = needCheckKnowledges.get(i);
            KnowledgeMatchResp knowledgeMatchResp = KnowledgeMatchResps.get(i);

            checkMap.put(needCheckKnowledge.getId(), transform(knowledgeMatchResp));
        }

        return new CheckResp(checkMap);
    }

    private List<CheckKnowledge> transform(KnowledgeMatchResp knowledgeMatchResp) {
        if (knowledgeMatchResp.getMatchType() == 0) {
            return new ArrayList<>();
        }
        return knowledgeMatchResp.getMatchRslts().stream().map(x -> new CheckKnowledge(x.getKnowledgeId(), x.getQuestion(), x.getScore())).collect(Collectors.toList());
    }

    private KnowledgeMatchResp check(NeedCheckKnowledge needCheckKnowledge) {
        BotContext botContext = new BotContext();
        botContext.setChannel(-1);
        botContext.setSessionInteraction(BotContext.SESSION_INTERACTION_CHECK);
        botContext.setCurInput(preprocessManager.preprocess(needCheckKnowledge.getStdQuestion()));

        return faqMatchPipelineService.processWithSimpleModel(botContext);
    }
}
