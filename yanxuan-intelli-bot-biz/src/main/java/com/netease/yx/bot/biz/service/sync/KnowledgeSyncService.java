package com.netease.yx.bot.biz.service.sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.common.NlpServerService;
import com.netease.yx.bot.biz.service.TextPreprocessService;
import com.netease.yx.bot.biz.common.es8.Es8Service;
import com.netease.yx.bot.biz.common.es8.SimpleBulkRequest;
import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.kfkm.*;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库数据同步
 */
@Service
@Slf4j
public class KnowledgeSyncService {
    private static final String GET_QA_TRAIN_TASK_URL = "/api/knowledge/get";
    private static final String GET_UNIFIED_SIMILAR_Q = "/api/knowledge/queryUnifiedSimilarQuestion";

    private static final String KNOWLEDGE_SOURCE = "knowledgeSource";

    private static final int KNOWLEDGE_SOURCE_FAQ = 1;

    private static final String KEY_KNOWLEDGE_ID = "knowledgeId";

    @Value("${remote.kfkm.host}")
    private String remoteKfkmHost;

    @Autowired
    private TextPreprocessService textPreprocessService;

    @Autowired
    private NlpServerService nlpServerService;

    @Autowired
    private Es8Service es8Service;

    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;

    @Autowired
    private EnvService envService;


    public List<UnifiedSimilarQuestion> getUnifiedSimilarQuestionById(long itemId, long attrId) {
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair(KNOWLEDGE_SOURCE, String.valueOf(KNOWLEDGE_SOURCE_FAQ)));
        params.add(new BasicNameValuePair(KEY_KNOWLEDGE_ID, String.valueOf(attrId)));

        try {
            String url = remoteKfkmHost + GET_UNIFIED_SIMILAR_Q;
            log.info("request {} {}", url, params);
            String resp = HttpUtils.executePost(url, params);
//            log.info("resp {}", resp);
            SucResp<List<UnifiedSimilarQuestion>> sucResp = IoUtils.parseJson(resp, new TypeReference<SucResp<List<UnifiedSimilarQuestion>>>() {
            });
            if (ObjectUtils.isNotEmpty(sucResp.getData())) {
                return sucResp.getData();
            }
        } catch (Exception e) {
            log.error("getById", e);
        }
        return null;
    }

    public AlgoKnowledge getById(Long knowledgeId) {
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair(KEY_KNOWLEDGE_ID, String.valueOf(knowledgeId)));

        try {
            String url = remoteKfkmHost + GET_QA_TRAIN_TASK_URL;
            log.info("request {} {}", url, params);
            String resp = HttpUtils.executeGet(url, params);
//            log.info("resp {}", resp);
            SucResp<AlgoKnowledgeWithId> sucResp = IoUtils.parseJson(resp, new TypeReference<SucResp<AlgoKnowledgeWithId>>() {
            });
            if (ObjectUtils.isNotEmpty(sucResp.getData())) {
                AlgoKnowledgeWithId algoKnowledgeWithId = sucResp.getData();

                return process(algoKnowledgeWithId);
            }
        } catch (Exception e) {
            log.error("getById", e);
        }
        return null;
    }

    public int sync(long knowledgeId) {
//        if (envManager.isRelease()){
//            return 1;
//        }
        log.info("sync knowledge {}", knowledgeId);
        AlgoKnowledge algoKnowledge = getById(knowledgeId);
        if (ObjectUtils.isNotEmpty(algoKnowledge)) {
            List<JSONObject> jsonObjects = new ArrayList<>();
            jsonObjects.add(JSON.parseObject(IoUtils.toJsonString(algoKnowledge)));
            List<String> ids = new ArrayList<>();
            ids.add(String.valueOf(algoKnowledge.getKnowledgeId()));
            log.info("sync {}", channelQaApolloConfig.getKnowledgeEsIndex());
            SimpleBulkRequest simpleBulkRequest = new SimpleBulkRequest(channelQaApolloConfig.getKnowledgeEsIndex(), jsonObjects, ids);
            try {
                int count = es8Service.bulk(simpleBulkRequest);
                return count;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return 0;
    }

    private AlgoKnowledge process(AlgoKnowledgeWithId algoKnowledgeWithId) {
        AlgoKnowledge algoKnowledge = algoKnowledgeWithId.getKnowledge();

        if (algoKnowledge.getEffectiveTime() == 0 && algoKnowledge.getExpiryTime() == 0) {
            algoKnowledge.setEffectiveType(0);
        } else {
            algoKnowledge.setEffectiveType(1);
        }

        TextBasicData textBasicData = textPreprocessService.preprocess(algoKnowledge.getStdQuestion());

        algoKnowledge.setStqQuestionVector(nlpServerService.getEmbedding(algoKnowledge.getStdQuestion()));
        algoKnowledge.setCleanStdQuestion(textBasicData.getCleanedText());
        algoKnowledge.setCleanStdQuestionTerms(StringUtils.join(textBasicData.getWords(), StringUtils.SPACE));

        if (CollectionUtils.isNotEmpty(algoKnowledgeWithId.getSimilarQuestions())) {
            for (AlgoSimilarQuestion algoSimilarQuestion : algoKnowledgeWithId.getSimilarQuestions()) {
                TextBasicData textBasicDataForSimilarQuestion = textPreprocessService.preprocess(algoSimilarQuestion.getContent());

                algoSimilarQuestion.setCleanContent(textBasicDataForSimilarQuestion.getCleanedText());
                algoSimilarQuestion.setCleanContentTerms(StringUtils.join(textBasicDataForSimilarQuestion.getWords(), StringUtils.SPACE));
            }
        }

        algoKnowledge.setSimilarQuestions(algoKnowledgeWithId.getSimilarQuestions());

        if (CollectionUtils.isNotEmpty(algoKnowledgeWithId.getKeywords())) {
            for (AlgoKeyword algoKeyword : algoKnowledgeWithId.getKeywords()) {
                TextBasicData textBasicDataForKeyword = textPreprocessService.preprocess(algoKeyword.getKeyword());

                algoKeyword.setCleanKeyword(textBasicDataForKeyword.getCleanedText());
            }
        }
        algoKnowledge.setKeywordGroups(algoKnowledgeWithId.getKeywords());

        if (CollectionUtils.isNotEmpty(algoKnowledgeWithId.getAnswers())) {
            for (AlgoAnswer algoAnswer : algoKnowledgeWithId.getAnswers()) {
                if (StringUtils.isEmpty(algoAnswer.getItemInfoLabel())) {
                    algoAnswer.setItemInfoLabel(AlgoKnowledge.VALUE_EMPTY);
                }
                // 类目id比较特殊，还是处理成一个string
                if (CollectionUtils.isEmpty(algoAnswer.getItemCateLabel())) {
                    algoAnswer.setItemCateLabelStr(AlgoKnowledge.VALUE_EMPTY);
                } else {
                    algoAnswer.setItemCateLabelStr(StringUtils.join(algoAnswer.getItemCateLabel().stream().map(String::valueOf).collect(Collectors.toList()), StringUtils.SPACE));
                }
                if (algoAnswer.getAnswerUse() == 0){
                    algoAnswer.setAnswerUse(algoKnowledge.getKnowledgeUse());
                }
            }
        }

        algoKnowledge.setAnswers(algoKnowledgeWithId.getAnswers());

        List<UnifiedSimilarQuestion> unifiedSimilarQuestions = getUnifiedSimilarQuestionById(0, algoKnowledgeWithId.getKnowledgeId());
        if (CollectionUtils.isNotEmpty(unifiedSimilarQuestions)) {
            List<AlgoSimilarQuestion> unSimilarQuestions = new ArrayList<>();
            // 只取逆向
            for (UnifiedSimilarQuestion unifiedSimilarQuestion : unifiedSimilarQuestions) {
                if (unifiedSimilarQuestion.getSimilarFlag() == UnifiedSimilarQuestion.UN_SIMILAR_FLAG) {
                    TextBasicData textBasicDataForSimilarQuestion = textPreprocessService.preprocess(unifiedSimilarQuestion.getContent());
                    unSimilarQuestions.add(new AlgoSimilarQuestion(unifiedSimilarQuestion.getId(), unifiedSimilarQuestion.getContent(),
                            textBasicDataForSimilarQuestion.getCleanedText(), StringUtils.join(textBasicDataForSimilarQuestion.getWords(), StringUtils.SPACE), 1));
                }
            }
            if (CollectionUtils.isNotEmpty(unifiedSimilarQuestions)) {
                algoKnowledge.setUnSimilarQuestions(unSimilarQuestions);
            }
        }

        return algoKnowledge;
    }
}
