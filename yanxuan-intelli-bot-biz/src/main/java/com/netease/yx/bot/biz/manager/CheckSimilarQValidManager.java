package com.netease.yx.bot.biz.manager;

import com.netease.yx.bot.biz.applloconfig.CheckConfig;
import com.netease.yx.bot.common.util.CustomLangUtils;
import com.netease.yx.bot.core.model.entity.checkSimilar.CheckSimilarQuestionReq;
import com.netease.yx.bot.core.model.entity.checkSimilar.CheckSimilarSingleResp;
import com.netease.yx.bot.core.model.entity.checkSimilar.CheckSimilarType;
import com.netease.yx.bot.core.model.entity.checkSimilar.DeleteResults;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoAnswer;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoKnowledge;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoSimilarQuestion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 离线批量检测每条知识的相似问是否符合规范和预期
 * 判断是否可以删除，比如长度太长或太短，或者都是无意义的文本
 *
 * 目前一般不会被调用
 */
@Service
@Slf4j
public class CheckSimilarQValidManager {
    @Autowired
    private CheckConfig checkConfig;

    public List<CheckSimilarSingleResp> process(CheckSimilarQuestionReq checkSimilarQuestionReq) {
        if (CollectionUtils.isEmpty(checkSimilarQuestionReq.getCheckTypes())) {
            checkSimilarQuestionReq.setCheckTypes(checkSimilarQuestionReq.getCheckTypes());
        }
        if (CollectionUtils.isEmpty(checkSimilarQuestionReq.getKnowledges())) {
            return new ArrayList<>();
        }
        return checkSimilarQuestionReq.getKnowledges().parallelStream().map(x -> doProcess(x, checkSimilarQuestionReq.getCheckTypes())).collect(Collectors.toList());
    }

    private CheckSimilarSingleResp doProcess(AlgoKnowledge knowledge, List<Integer> checkTypeCodes) {
        String stdQuestion = knowledge.getStdQuestion();
        List<AlgoAnswer> answers = knowledge.getAnswers();
        List<AlgoSimilarQuestion> similarQuestions = knowledge.getSimilarQuestions();
        List<DeleteResults> deleteResults = new ArrayList<>();

        for (AlgoSimilarQuestion algoSimilarQuestion : similarQuestions) {
            for (Integer checkTypeCode : checkTypeCodes) {
                CheckSimilarType checkSimilarType = CheckSimilarType.getByCode(checkTypeCode);
                boolean flag = false;
                switch (checkSimilarType) {
                    case UNVALID_LENGTH:
                        if (checkLength(algoSimilarQuestion.getContent())) {
                            deleteResults.add(new DeleteResults(algoSimilarQuestion.getId(), algoSimilarQuestion.getContent(), checkTypeCode, CheckSimilarType.UNVALID_LENGTH.name()));
                            flag = true;
                            break;
                        }
                        break;
                    case UNVALID_TEXT:
                        if (checkText(algoSimilarQuestion.getContent())) {
                            deleteResults.add(new DeleteResults(algoSimilarQuestion.getId(), algoSimilarQuestion.getContent(), checkTypeCode, CheckSimilarType.UNVALID_TEXT.name()));
                            flag = true;
                            break;
                        }
                    default:
                }
                if (flag) {
                    break;
                }
            }
        }

        return new CheckSimilarSingleResp(knowledge.getKnowledgeId(), deleteResults);
    }

    private boolean checkLength(String similarContent) {
        return StringUtils.length(similarContent) <= checkConfig.getSimilarCheckMinLength() || StringUtils.length(similarContent) >= checkConfig.getSimilarCheckMaxLength();
    }

    private boolean checkText(String similarContent) {
        return !CustomLangUtils.isChiStr(similarContent);
    }
}
