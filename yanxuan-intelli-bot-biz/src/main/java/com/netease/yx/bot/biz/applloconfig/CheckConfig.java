package com.netease.yx.bot.biz.applloconfig;

import com.ctrip.framework.apollo.spring.annotation.EnableAutoUpdateApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ValueMapping;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 主动预测配置
 */
@Component
@EnableAutoUpdateApolloConfig("check")
@Slf4j
@Getter
public class CheckConfig {
    @ValueMapping("${default.similar.check.types:[1,2,3,4]}")
    private List<Integer> defaultSimilarCheckTypes;

    @ValueMapping("${similar.check.min.length:3}")
    private int similarCheckMinLength;

    @ValueMapping("${similar.check.max.length:50}")
    private int similarCheckMaxLength;
}
