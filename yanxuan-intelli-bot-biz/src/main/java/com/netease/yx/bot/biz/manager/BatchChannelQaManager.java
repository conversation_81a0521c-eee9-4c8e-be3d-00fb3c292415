package com.netease.yx.bot.biz.manager;

import com.netease.yx.bot.biz.common.mps.MpsReceiveMessageBean;
import com.netease.yx.bot.biz.service.mps.SessionAnalysisMsg;
import com.netease.yx.bot.biz.service.mps.SessionCloseService;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.channel.BatchChannelQaReq;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaReq;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaResp;
import com.netease.yx.bot.core.model.train.MsgIdMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * APP-主站的会话
 * 结束的时候，批量发消息过来用于生成会话挖掘任务
 */
@Service
@Slf4j
public class BatchChannelQaManager {

    @Autowired
    private ChannelQaManager channelQaManager;
    @Autowired
    private SessionCloseService sessionCloseService;

    public List<ChannelQaResp> process(BatchChannelQaReq batchChannelQaReq) {
        List<ChannelQaResp> resps = new ArrayList<>();
        if (CollectionUtils.isEmpty(batchChannelQaReq.getQaReqList())) {
            return resps;
        }
        for (ChannelQaReq channelQaReq : batchChannelQaReq.getQaReqList()) {
            channelQaReq.setBatch(true);
            resps.add(channelQaManager.pipeline(channelQaReq));
        }

        // 等待一秒，避免redis缓存还没有生成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        // 然后模拟会话结束消息，复用原有的会话结束流程
        SessionAnalysisMsg mpsMsg = new SessionAnalysisMsg();

        int size = batchChannelQaReq.getQaReqList().size();
        // 取最后一个, 七鱼-人工，可能会有 机器人会话，人工混淆的情况
        Long kefuSessionId = Long.parseLong(batchChannelQaReq.getQaReqList().get(size - 1).getSessionId());
        // 消息映射
        List<MsgIdMap> msgIdMaps = new ArrayList<>();
        for (ChannelQaReq channelQaReq : batchChannelQaReq.getQaReqList()) {
            msgIdMaps.add(new MsgIdMap(channelQaReq.getMessageId(), Long.parseLong(channelQaReq.getMessageId())));
        }

        mpsMsg.setSessionId(kefuSessionId);
        mpsMsg.setSessionInteraction(batchChannelQaReq.getQaReqList().get(size - 1).getSessionInteraction());
        mpsMsg.setMessageIds(msgIdMaps);

        // MpsReceiveMessageBean mpsMessage
        MpsReceiveMessageBean mpsReceiveMessageBean = new MpsReceiveMessageBean();
        mpsReceiveMessageBean.setPayload(IoUtils.toJsonString(mpsMsg));
        log.info("process {}", IoUtils.toJsonString(mpsReceiveMessageBean));
        sessionCloseService.process(mpsReceiveMessageBean);

        return resps;
    }
}
