/**
 * @(#)QcIdempotentDao.java, 2018/3/22.
 * <p/>
 * Copyright 2018 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.common.mps;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class IdempotentRedisDAO implements IdempotentDAO {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void insert(String record) {
        Boolean aTrue = redisTemplate.opsForValue().setIfAbsent(record, "true", 30, TimeUnit.DAYS);
        log.info("[op:msgInsert] insert record={}", record);
        if (!aTrue) {
            log.error("[op:msgInsert] insert error={}", record);
            throw new RuntimeException(DUPLICATED_RECORD_MESSAGE);
        }
    }

    @Override
    public void deleteByRecord(String record) {
        redisTemplate.delete(record);
        log.info("[op:msgDelete] delete record={}", record);
    }
}
