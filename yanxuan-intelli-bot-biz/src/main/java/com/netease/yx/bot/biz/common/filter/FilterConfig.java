package com.netease.yx.bot.biz.common.filter;

import com.netease.mail.yanxuan.eudemon.EudemonWebPageFilter;
import org.springframework.beans.BeansException;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;


/**
 * 限流中间件使用
 */
@Configuration
public class FilterConfig implements ApplicationContextAware {

    private static final String EUDEMON_WEB_PAGE_FILETER_NAME = "eudemonWebPageFilter";

    private ApplicationContext applicationContext;

    @Bean
    public FilterRegistrationBean registFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        Filter webPageFilter = (EudemonWebPageFilter) applicationContext.getBean(EUDEMON_WEB_PAGE_FILETER_NAME);
        registration.setFilter(webPageFilter);
        registration.addUrlPatterns("/*");
        registration.setName(EUDEMON_WEB_PAGE_FILETER_NAME);
        registration.setOrder(1);
        return registration;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
