package com.netease.yx.bot.biz.service.qa.emb;

import com.alibaba.fastjson.JSON;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.faq.MatchServiceDistanceInput;
import com.netease.yx.bot.core.model.entity.faq.MatchServiceDistanceResponse;
import com.netease.yx.bot.core.model.entity.faq.MatchTokenizerInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BertMatchManager {

    private static final String HEADER_KEY_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_VALUE_CONTENT_TYPE = "application/json";
    private static final String HEADER_KEY_HOST_TYPE = "host";
    private static final int CANDIDATE_NUM = 50;
    private static final int DIM = 768;

    private static final String REQUEST_OBJ_KEY = "instances";
    private static final List<Float> DEFAULT_CANDIDATE_EMB = Collections.nCopies(DIM, 0.0f);

    @Autowired
    private TextProcessor4BertMatch preprocessor;
    @Value("${rank.bert.match.url}")
    private String bertMatchUrl;

    private Map<String, String> buildHeaders(String headerHost) {
        Map<String, String> headers = new HashMap<>(2);
        headers.put(HEADER_KEY_CONTENT_TYPE, HEADER_VALUE_CONTENT_TYPE);
        headers.put(HEADER_KEY_HOST_TYPE, headerHost);
        return headers;
    }

    public List<Float> getDistance(String sen, List<List<Float>> rawCandidateEmb, Integer maxLength, String url, String headerHost, String hst) {
        List<Float> response = new ArrayList<>();
        if (StringUtils.isEmpty(sen) || CollectionUtils.isEmpty(rawCandidateEmb)) {
            return response;
        }
        List<List<Float>> candidateEmb = new ArrayList<>(rawCandidateEmb);
        if (candidateEmb.size() <= CANDIDATE_NUM) {
            candidateEmb.addAll(Collections.nCopies(CANDIDATE_NUM - rawCandidateEmb.size(), DEFAULT_CANDIDATE_EMB));
        }
        try {
            MatchTokenizerInput tokenizerInput;
            if (!StringUtils.isEmpty(hst)) {
                tokenizerInput = preprocessor.getInput(sen, hst, maxLength);
            } else {
                tokenizerInput = preprocessor.getInput(sen, maxLength);
            }

            Map<String, String> headers = buildHeaders(headerHost);
            Map<String, List<MatchServiceDistanceInput>> reqObj = new HashMap<>(1);

            MatchServiceDistanceInput matchServiceDistanceInput = new MatchServiceDistanceInput(tokenizerInput.getInput_ids(),
                    tokenizerInput.getLength(), candidateEmb);

            List<MatchServiceDistanceInput> reqDistanceInput = new ArrayList<>(Collections.nCopies(1, matchServiceDistanceInput));
            reqObj.put(REQUEST_OBJ_KEY, reqDistanceInput);
            String result = HttpUtils.executePost(url, JSON.toJSONString(reqObj), null, headers);
            MatchServiceDistanceResponse matchServiceEmbResponse = IoUtils.parseJson(result, MatchServiceDistanceResponse.class);
            if (matchServiceEmbResponse != null && matchServiceEmbResponse.getPredictions() != null) {
                response = matchServiceEmbResponse.getPredictions();
            }
        } catch (Exception e) {
            log.error("error in BertMatchManager", e);
        }

        return response.stream().limit(rawCandidateEmb.size()).collect(Collectors.toList());
    }
}
