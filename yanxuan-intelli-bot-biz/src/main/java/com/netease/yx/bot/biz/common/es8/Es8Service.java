package com.netease.yx.bot.biz.common.es8;

import com.alibaba.fastjson.JSONObject;
import com.netease.yx.bot.common.util.IoUtils;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.ElasticsearchClient;
import essearch.shaded.co.elasticsearch.clients.elasticsearch._types.KnnQuery;
import essearch.shaded.co.elasticsearch.clients.elasticsearch._types.query_dsl.MatchAllQuery;
import essearch.shaded.co.elasticsearch.clients.elasticsearch._types.query_dsl.Query;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.*;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.bulk.BulkResponseItem;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.knn_search.KnnSearchQuery;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.search.Hit;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.search.InnerHits;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.indices.DeleteIndexRequest;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.indices.DeleteIndexResponse;
import essearch.shaded.co.elasticsearch.clients.json.jackson.JacksonJsonpMapper;
import essearch.shaded.co.elasticsearch.clients.transport.rest_client.RestClientTransport;
import essearch.shaded.org.elasticsearch.client.RestClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * 注意，要使用essearch.shaded.co.elasticsearch包来替代
 */
@Service
@Slf4j
public class Es8Service {
    @Value("${es.nodes}")
    private String esNodes;

    @Value("${es.port}")
    private Integer esPort;

    private ElasticsearchClient elasticsearchClient;

    @PostConstruct
    private void init() {
        HttpHost[] httpHosts = Arrays.stream(StringUtils.split(esNodes, ",")).map((host) -> new HttpHost(host, esPort)).toArray((x$0) -> new HttpHost[x$0]);
        RestClient restClient = RestClient.builder(httpHosts).build();
        RestClientTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());
        elasticsearchClient = new ElasticsearchClient(transport);
    }

    public ElasticsearchClient getElasticsearchClient() {
        return this.elasticsearchClient;
    }

    // 检索所有，测试
    public List<JSONObject> matchAll(String index) throws IOException {
        SearchResponse<JSONObject> search = elasticsearchClient.search(s -> s
                        .index(index)
                        .query(q -> q
                                .matchAll(new MatchAllQuery.Builder().build())),
                JSONObject.class);
        List<JSONObject> results = new ArrayList<>();
        for (Hit<JSONObject> hit : search.hits().hits()) {
            results.add(hit.source());
        }
        return results;
    }

    // 基础的向量召回，包含向量和简单的筛选
    public List<HitObject> knnSearch(SimpleKnnRequest simpleKnnRequest) throws IOException {
        log.info("simpleKnnRequest {}", IoUtils.toJsonString(simpleKnnRequest));
        KnnSearchRequest.Builder knnSearchRequest = new KnnSearchRequest.Builder();
        knnSearchRequest.index(simpleKnnRequest.getIndex());
        if (CollectionUtils.isNotEmpty(simpleKnnRequest.getFields())) {
            knnSearchRequest.fields(simpleKnnRequest.getFields());
        }

        if (CollectionUtils.isNotEmpty(simpleKnnRequest.getFilterTerms())) {
            List<Query> filters = new ArrayList<>();

            for (FilterTerm filterTerm : simpleKnnRequest.getFilterTerms()) {
                if (filterTerm.isNested()) {
                    // 添加嵌套查询
                    switch (filterTerm.getFunc()) {
                        case MATCH:
                            filters.add(Query.of(q -> q.bool(b -> b.must(m -> m.nested(n -> n.path(filterTerm.getNestedPath()).innerHits(x -> new InnerHits.Builder())
                                    .query(qq -> qq.match(t -> t.field(filterTerm.getKey()).query(filterTerm.getValue())))
                            )))));
                            break;
                        case TERM:
                            filters.add(Query.of(q -> q.bool(b -> b.must(m -> m.nested(n -> n.path(filterTerm.getNestedPath()).innerHits(x -> new InnerHits.Builder())
                                    .query(qq -> qq.term(t -> t.field(filterTerm.getKey()).value(filterTerm.getValue())))
                            )))));
                            break;
                        default:
                            break;
                    }
                } else {
                    // 添加普通查询
                    filters.add(Query.of(q -> q.term(t -> t.field(filterTerm.getKey()).value(filterTerm.getValue()))));
                }
            }
            knnSearchRequest.filter(filters);
        }

        KnnSearchQuery.Builder knnSearchQuery = new KnnSearchQuery.Builder();
        knnSearchQuery.field(simpleKnnRequest.getKnnField());
        knnSearchQuery.k(simpleKnnRequest.getTopK());
        knnSearchQuery.numCandidates(simpleKnnRequest.getCandidates());
        knnSearchQuery.queryVector(simpleKnnRequest.getVector());

        knnSearchRequest.knn(knnSearchQuery.build());
        long s = System.currentTimeMillis();
        KnnSearchRequest knnSearchRequest1 = knnSearchRequest.build();
        log.info("knnSearchRequest1 {}", IoUtils.toJsonString(knnSearchRequest1));

        KnnSearchResponse<JSONObject> res = elasticsearchClient.knnSearch(knnSearchRequest1, JSONObject.class);
        log.info("knn cost {}", System.currentTimeMillis() - s);

        List<HitObject> results = new ArrayList<>();
        for (Hit<JSONObject> hit : res.hits().hits()) {
            results.add(
                    new HitObject(hit.score(), hit.source(), hit.fields())
            );
        }
        return results;
    }

    // 混合的查询
    public List<HitObject> search(SimpleKnnRequest simpleKnnRequest) throws IOException {
        log.info("simpleKnnRequest {}", IoUtils.toJsonString(simpleKnnRequest));
        SearchRequest.Builder requestBuilder = new SearchRequest.Builder();
        requestBuilder.index(simpleKnnRequest.getIndex());

        KnnQuery.Builder knnQuery = new KnnQuery.Builder();
        knnQuery.k(simpleKnnRequest.getTopK());
        knnQuery.field(simpleKnnRequest.getKnnField());
        knnQuery.queryVector(simpleKnnRequest.getVector());
        knnQuery.numCandidates(simpleKnnRequest.getCandidates());

        if (CollectionUtils.isNotEmpty(simpleKnnRequest.getFilterTerms())) {
            List<Query> filters = new ArrayList<>();

            for (FilterTerm filterTerm : simpleKnnRequest.getFilterTerms()) {
                if (filterTerm.isNested()) {
                    // 添加嵌套查询
                    switch (filterTerm.getFunc()) {
                        case MATCH:
                            filters.add(Query.of(q -> q.bool(b -> b.must(m -> m.nested(n -> n.path(filterTerm.getNestedPath()).innerHits(x -> new InnerHits.Builder())
                                    .query(qq -> qq.match(t -> t.field(filterTerm.getKey()).query(filterTerm.getValue())))
                            )))));
                            break;
                        case TERM:
                            filters.add(Query.of(q -> q.bool(b -> b.must(m -> m.nested(n -> n.path(filterTerm.getNestedPath()).innerHits(x -> new InnerHits.Builder())
                                    .query(qq -> qq.term(t -> t.field(filterTerm.getKey()).value(filterTerm.getValue())))
                            )))));
                            break;
                        default:
                            break;
                    }
                } else {
                    // 添加普通查询
                    filters.add(Query.of(q -> q.term(t -> t.field(filterTerm.getKey()).value(filterTerm.getValue()))));
                }
            }
            knnQuery.filter(filters);
        }

        requestBuilder.knn(knnQuery.build());
        SearchRequest searchRequest = requestBuilder.build();
        log.info("searchRequest {}", IoUtils.toJsonString(searchRequest));

        SearchResponse<JSONObject> response = elasticsearchClient.search(searchRequest, JSONObject.class);

        List<HitObject> results = new ArrayList<>();
        for (Hit<JSONObject> hit : response.hits().hits()) {
            results.add(
                    new HitObject(hit.score(), hit.source(), hit.fields())
            );
        }
        return results;
    }

    // 删除索引
    public boolean delete(String index) throws IOException {
        DeleteIndexRequest.Builder builder = new DeleteIndexRequest.Builder();
        builder.index(index, new String[0]);
        DeleteIndexResponse response = elasticsearchClient.indices().delete(builder.build());
        return response.acknowledged();
    }

    public int bulk(SimpleBulkRequest simpleBulkRequest) throws IOException {
        BulkRequest.Builder br = new BulkRequest.Builder();
        List<JSONObject> objects = simpleBulkRequest.getObjects();
        List<String> ids = simpleBulkRequest.getIds();
        String index = simpleBulkRequest.getIndex();
        for (int i = 0; i < objects.size(); i++) {
            final int finalI = i;
            br.operations(op -> op.index(idx -> idx.index(index).id(ids.get(finalI)).document(objects.get(finalI))));
        }
        BulkResponse result = elasticsearchClient.bulk(br.build());

        int count = objects.size();
        if (result.errors()) {
            for (BulkResponseItem item : result.items()) {
                if (item.error() != null) {
                    log.error(item.error().reason());
                    count -= 1;
                }
            }
        }
        return count;
    }
}
