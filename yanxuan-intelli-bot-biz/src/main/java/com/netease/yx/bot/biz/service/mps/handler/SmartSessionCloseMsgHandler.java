/**
 * @(#)SessionAnalysisMsgHandler.java, 4/18/23.
 * <p/>
 * Copyright 2023 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service.mps.handler;

import com.netease.yx.bot.biz.common.mps.MpsBaseHandler;
import com.netease.yx.bot.biz.common.mps.MpsHandler;
import com.netease.yx.bot.biz.common.mps.MpsReceiveMessageBean;
import com.netease.yx.bot.biz.service.mps.SessionCloseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会话结束消息处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@MpsHandler(product = "yanxuan-kefu-smart-workbench", topic = "kefu_smart_workbench_session_analysis")
public class SmartSessionCloseMsgHandler extends MpsBaseHandler {

    @Autowired
    private SessionCloseService sessionCloseService;

    @Override
    public void process(MpsReceiveMessageBean mpsMessage) {
        sessionCloseService.process(mpsMessage);
    }
}