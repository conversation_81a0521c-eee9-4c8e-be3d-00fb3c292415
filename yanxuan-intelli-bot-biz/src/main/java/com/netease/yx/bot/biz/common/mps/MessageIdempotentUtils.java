/**
 * @(#)IdempotentUtils.java, 2018/3/23.
 * <p/>
 * Copyright 2018 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.common.mps;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

import static com.netease.yx.bot.biz.common.mps.IdempotentDAO.DUPLICATED_ENTRY;
import static com.netease.yx.bot.biz.common.mps.IdempotentDAO.DUPLICATED_RECORD_MESSAGE;


/**
 * <AUTHOR>
 */
@Component
public class MessageIdempotentUtils {

    private static MessageIdempotentUtils sThis;

    @Autowired
    private IdempotentDAO idempotentDAO;


    /**
     * 幂等校验
     *
     * @param record 幂等记录
     * @return 是否成功
     */
    public static boolean check(String record) {
        try {
            sThis.idempotentDAO.insert(record);
        } catch (Exception e) {
            if (e.getMessage().contains(DUPLICATED_RECORD_MESSAGE) || e.getMessage().contains(DUPLICATED_ENTRY)) {
                // 命中了唯一键的限制
                return false;
            }
            throw new RuntimeException("idempotent insert error", e);
        }
        return true;
    }

    /**
     * 幂等回滚
     *
     * @param record 幂等记录
     */
    public static void rollBack(String record) {
        sThis.idempotentDAO.deleteByRecord(record);
    }

    private static void setsThis(MessageIdempotentUtils sThis) {
        MessageIdempotentUtils.sThis = sThis;
    }

    @PostConstruct
    public void init() {
        setsThis(this);
        //sThis = this;
    }
}
