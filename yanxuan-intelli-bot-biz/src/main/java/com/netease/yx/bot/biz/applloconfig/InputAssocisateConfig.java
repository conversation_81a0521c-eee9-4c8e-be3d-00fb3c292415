package com.netease.yx.bot.biz.applloconfig;

import com.ctrip.framework.apollo.spring.annotation.EnableAutoUpdateApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ValueMapping;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 自动补全配置
 */
@Component
@EnableAutoUpdateApolloConfig("inputAssociate")
@Slf4j
@Getter
public class InputAssocisateConfig {

    @ValueMapping("${inputAssociate.ignore.faqid.list:[]}")
    private List<Long> inputAssociateIgnoreFaqIdList;
}
