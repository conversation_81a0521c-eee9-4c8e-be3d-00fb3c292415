/**
 * @(#)ProphetRuleManager.java, 2020/12/1.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service.qa;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.constant.PlatformType;
import com.netease.yx.bot.core.model.constant.SourceType;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.UserOrder;
import com.netease.yx.bot.core.model.entity.prophet.ProphetRule;
import com.netease.yx.bot.core.model.entity.prophet.RuleSchemaNode;
import com.netease.yx.bot.core.model.entity.prophet.UserTag;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR> @ corp.netease.com)
 * 主动预测规则系统
 * <p>
 * 从配置后台获取主动预测的配置
 * 相关接口文档
 * http://yx.mail.netease.com/wiki#/doc/2840949
 * http://yx.mail.netease.com/wiki#/doc/2977310
 */
@Service
@EnableScheduling
@Slf4j
@Getter
public class ProphetRuleService {
    private static final String RULE_KEY_PRE_SALE = "presale";

    private static final String RULE_KEY_RETURN_STATUS = "returnStatus";
    private static final String RULE_KEY_EXCHANGE_STATUS = "exchangeStatus";
    private static final String RULE_KEY_REJECT_STATUS = "rejectStatus";
    private static final String RULE_KEY_REPAIR_STATUS = "repairStatus";
    private static final String RULE_KEY_PRICE_PROTECTION_STATUS = "priceProtectionStatus";

    private static final String RULE_KEY_SPMC_STATUS = "spmcStatus";
    private static final String RULE_KEY_USER_CREDIT_LEVEL = "userCreditLevel";
    private static final String RULE_KEY_TICKET_STATUS = "ticketStatus";
    private static final String RULE_KEY_TRACKING_STATUS = "trackingStatus";
    private static final String RULE_KEY_ORDER_STATUS = "orderStatus";

    private static final String RULE_KEY_SPEED_RETURN = "speedReturn";
    private static final String RULE_KEY_SPEED_EXCHANGE = "speedExchange";

    private static final String SOURCE_VALUE_ALL = "ALL";

    private static final Map<Integer, String> APPLY_CODE_DESC_MAP = new HashMap<Integer, String>() {{
        put(UserOrder.APPLY_TYPE_CODE_CHANGE, RULE_KEY_EXCHANGE_STATUS);
        put(UserOrder.APPLY_TYPE_CODE_FIX, RULE_KEY_REPAIR_STATUS);
        put(UserOrder.APPLY_TYPE_CODE_PRICE, RULE_KEY_PRICE_PROTECTION_STATUS);
        put(UserOrder.APPLY_TYPE_CODE_REJECT, RULE_KEY_REJECT_STATUS);
        put(UserOrder.APPLY_TYPE_CODE_RETURN, RULE_KEY_RETURN_STATUS);
    }};

    private static final Map<String, Integer> USER_CREDIT_LEVEL_MAP = new HashMap<String, Integer>() {{
        put(UserOrder.USER_CREDIT_LEVEL_R1, 0);
        put(UserOrder.USER_CREDIT_LEVEL_R2, 1);
        put(UserOrder.USER_CREDIT_LEVEL_R3, 2);
        put(UserOrder.USER_CREDIT_LEVEL_R4, 3);
        put(UserOrder.USER_CREDIT_LEVEL_R5, 4);
    }};
    /**
     * 规则定义的schema
     */
    private List<RuleSchemaNode> schema;
    /**
     * 配置的规则
     */
    private List<ProphetRule> rules;
    /**
     * 查询规则的接口
     */
    @Value("${prophet.rule.schema.query.url}")
    private String prophetRuleSchemaQueryUrl;
    /**
     * 查询配置的接口
     */
    @Value("${prophet.rule.config.query.url}")
    private String prophetRuleConfigQueryUrl;

    @PostConstruct
    private void init() {
        update();
    }

    @Scheduled(cron = "0 */${schedule.prophet.rule.interval.minutes} * * * *")
    private void update() {
        log.info("start ProphetRule");
        List<RuleSchemaNode> newSchema = new ArrayList<>();
        List<ProphetRule> newRules = new ArrayList<>();

        try {
            String schemaResponse = HttpUtils.executeGet(prophetRuleSchemaQueryUrl);
//            log.info("ProphetRuleManager, response: {} {}", prophetRuleSchemaQueryUrl, schemaResponse);

            SucResp<List<RuleSchemaNode>> schemaResult = IoUtils.parseJson(schemaResponse, new TypeReference<SucResp<List<RuleSchemaNode>>>() {
            });
            if (schemaResult.getData() != null) {
                newSchema = schemaResult.getData();
            }

            String configResponse = HttpUtils.executeGet(prophetRuleConfigQueryUrl);
//            log.info("ProphetRuleManager, response: {} {}", prophetRuleConfigQueryUrl, configResponse);

            SucResp<List<ProphetRule>> configSchemaResult = IoUtils.parseJson(configResponse, new TypeReference<SucResp<List<ProphetRule>>>() {
            });
            if (configSchemaResult.getData() != null) {
                newRules = configSchemaResult.getData();
            }
            // 优先级从高到低
            Collections.sort(newRules);
        } catch (IOException e) {
            log.info("ProphetRuleManager exception", e);
        }
        schema = newSchema;
        rules = newRules;

        log.info("ProphetRuleManager, schema: {} ", IoUtils.toJsonString(schema));
        log.info("ProphetRuleManager, rules: {} ", IoUtils.toJsonString(rules));
    }

    /**
     * 检查是否满足条件
     *
     * @param userOrder
     * @param botContext
     * @return
     */
    public ProphetRule check(UserOrder userOrder, BotContext botContext) {
        if (botContext == null) {
            return null;
        }
        // 将用户的对象转换
        Map<String, Integer> userStat = transfer(userOrder, botContext);
        log.info("ProphetRuleManager userStat {} ", userStat);

        for (ProphetRule prophetRule : rules) {
            log.info("ProphetRuleManager {} prophetRule", prophetRule);

            // 判断是否满足用户身份条件
            boolean userTypeFLag = checkUserType(botContext.getUserV(), botContext.isNewUser(), prophetRule.getUserTagsList());
            if (!userTypeFLag) {
                continue;
            }
            log.info("userTypeFLag pass");
            // 先判断是否满足入口和设备条件
            boolean platformSourceFlag = checkPlatformType(botContext.getPlatformType(), botContext.getSourceType(), prophetRule.getPlatformSourceMap());
            if (!platformSourceFlag) {
                continue;
            }
            log.info("platformSourceFlag pass");

            Map<String, List<Integer>> ruleContent = prophetRule.getContent();
            // 如果没有规则内容，则判断为是直接通过
            if (MapUtils.isEmpty(ruleContent)) {
                return prophetRule;
            }

            boolean contentMatchFlag = true;
            for (Map.Entry<String, List<Integer>> entry : ruleContent.entrySet()) {
                //
                log.info("ruleContent match {} {}", entry, userStat.getOrDefault(entry.getKey(), null));

                if (!userStat.containsKey(entry.getKey()) || !entry.getValue().contains(userStat.get(entry.getKey()))) {
                    contentMatchFlag = false;
                    break;
                }
            }

            if (contentMatchFlag) {
                log.info("ProphetRuleManager {} {} {} {}", botContext, userOrder, prophetRule, userStat);
                return prophetRule;
            }
        }
        return null;
    }

    private boolean checkUserType(int mbrLevel, boolean isNew, List<UserTag> userTags) {
        // 如果为null，说明不做限制
        if (CollectionUtils.isEmpty(userTags)) {
            return true;
        }
        // 循环判断，有一个命中即可
        for (UserTag userTag : userTags) {
            // 特殊的一档，是否是新用户
            if (userTag.getValue() == UserTag.NEW_USER.getValue() && isNew) {
                return true;
            }
            // 两个变量都是1对应v1，2对应v2
            if (userTag.getValue() == mbrLevel && mbrLevel != 0) {
                return true;
            }
        }
        return false;
    }

    private boolean checkPlatformType(PlatformType platformType, SourceType sourceType, Map<PlatformType, List<SourceType>> platformTypeListMap) {
        if (MapUtils.isEmpty(platformTypeListMap)) {
            return false;
        }
        // 条件里包含当前的平台，且平台里的source也包含当前source 或者平台source包含ALL
        return platformTypeListMap.containsKey(platformType) && (platformTypeListMap.get(platformType).contains(sourceType)
                || platformTypeListMap.get(platformType).contains(SourceType.ALL));
    }

    private boolean checkPreSale(boolean isPreSale, Map<String, List<Integer>> ruleContent) {
        // 根据约定定义，售前为0
        int value = isPreSale ? 0 : 1;
        // 没有这个条件
        // 或者有这个条件，且满足
        return !ruleContent.containsKey(RULE_KEY_PRE_SALE) || (ruleContent.containsKey(RULE_KEY_PRE_SALE) && ruleContent.get(RULE_KEY_PRE_SALE).contains(value));
    }


    /**
     * 将用户状态上下文转换成规则可以匹配到的对象
     *
     * @param userOrder
     * @param botContext
     * @return
     */
    private Map<String, Integer> transfer(UserOrder userOrder, BotContext botContext) {
        Map<String, Integer> map = new HashMap<>(10);
        // 根据约定定义，售前为0
        int value = botContext.isPreSale() ? 0 : 1;
        map.put(RULE_KEY_PRE_SALE, value);
        if (ObjectUtils.isEmpty(userOrder)) {
            return map;
        }
        map.put(RULE_KEY_ORDER_STATUS, userOrder.getOrderStatus());

        int applyCode = (int) userOrder.getApplyTypeCode();
        if (APPLY_CODE_DESC_MAP.containsKey(applyCode)) {
            String applyKey = APPLY_CODE_DESC_MAP.get(applyCode);
            int applyValue = (int) userOrder.getApplyStatus();
            map.put(applyKey, applyValue);
        }

        map.put(RULE_KEY_SPMC_STATUS, userOrder.getSpmcStatus());
        map.put(RULE_KEY_USER_CREDIT_LEVEL, USER_CREDIT_LEVEL_MAP.getOrDefault(userOrder.getUserCreditLevel(), -1));
        map.put(RULE_KEY_TICKET_STATUS, userOrder.getTicketStatus());
        map.put(RULE_KEY_TRACKING_STATUS, (int) userOrder.getTrackingStatus());

        return map;
    }
}