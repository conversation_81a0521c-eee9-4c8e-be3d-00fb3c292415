/**
 * @(#)ShortcutRcmdManager.java, 2020/10/24.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.manager;

import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.service.UserService;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.biz.service.KnowledgeService;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.UserOrder;
import com.netease.yx.bot.core.model.entity.knowledge.Knowledge;
import com.netease.yx.bot.core.model.entity.shourtcut.ShortcutRcmdReq;
import com.netease.yx.bot.core.model.entity.shourtcut.ShortcutRcmdResp;
import com.netease.yx.bot.core.model.log.MainPipelineLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * APP机器人-快捷短语推荐
 * 不重要
 * <AUTHOR> @ corp.netease.com)
 */
@Service
@Slf4j
public class ShortcutRcmdManager {
    private static final long IGNORED_ITEM_ID = 3998431;
    private static final int MAX_NUM = 10;
    private static final int MIN_NUM = 3;
    @Autowired
    private BotApolloConfig botApolloConfig;
    @Autowired
    private KnowledgeService knowledgeService;
    @Autowired
    private UserService userService;
    @Autowired
    private PipelineManager pipelineManager;
    @Autowired
    private EnvService envService;

    public ShortcutRcmdResp process(ShortcutRcmdReq shortcutRcmdReq) {

        if (envService.isTest()) {
            String shortstrIds = "265,266,1,2,3,4,5";
            List<Long> shortIds = Arrays.stream(shortstrIds.split(",")).map(Long::parseLong).collect(Collectors.toList());

            return new ShortcutRcmdResp(shortIds);
        }

        // 这里专门给省钱卡做了处理
        // 已经买过了省钱卡
        boolean shengqiankaFlag = false;
        // 是VIP
        boolean vipFlag = false;
        // 是新用户
        boolean newUserFlag = false;

        BotContext botContext = pipelineManager.getContextFromCache(shortcutRcmdReq.getSessionId());
        if (botContext != null) {
            long userId = botContext.getUserId();
            // 取用户的订单，这里背后已经做了缓存
            List<UserOrder> userOrders = userService.queryOrderByUserId(userId, botContext.getOrderId());

            if (!CollectionUtils.isEmpty(userOrders)) {
                for (UserOrder userOrder : userOrders) {
                    if (userOrder.getItemId() == IGNORED_ITEM_ID) {
                        shengqiankaFlag = true;
                    }
                    if (userOrder.getSpmcStatus() == 3) {
                        vipFlag = true;
                    }
                }
            }
            newUserFlag = userService.isNewUser(userId);
            log.info("ShortcutRcmdManager {} {} {}", shengqiankaFlag, vipFlag, newUserFlag);
        }

        // 全局置顶
        List<Long> topConfig = botApolloConfig.getShortcutTopList();
        List<Long> result = new ArrayList<>(topConfig);

        // 新用户配置
        if (newUserFlag) {
            List<Long> newFlagConfig = botApolloConfig.getNewFlagTopShortcutList();
            result.addAll(newFlagConfig);
        }

        // VIP 配置
        if (vipFlag) {
            List<Long> vipConfig = botApolloConfig.getVipTopShortcutList();
            result.addAll(vipConfig);
        }

        // 根据历史推荐
        result.addAll(rcmd(botContext, shortcutRcmdReq));

        // 兜底配置
        List<Long> defaultConfig = botApolloConfig.getDefaultShortcutList();
        result.addAll(defaultConfig);

        // 后处理，如果已经买过省钱卡，则不推特定
        if (shengqiankaFlag) {
//            result.remove(20143185L);
        }
        // 后处理，如果不是新用户，则不推特定
        if (!newUserFlag) {
//            result.remove(20143181L);
        }

        // min到max之间
        int rcmdNum = Math.max(MAX_NUM, Math.min(MIN_NUM, shortcutRcmdReq.getRcmdNum()));
        return new ShortcutRcmdResp(result.stream().filter(x -> knowledgeService.getShortcutIdSet().contains(x)).distinct().limit(rcmdNum).collect(Collectors.toList()));
    }

    private List<Long> getHistoryIds(BotContext botContext, ShortcutRcmdReq shortcutRcmdReq) {
        List<Long> historyIds = new ArrayList<>();

        // 如果有历史记录
        if (botContext != null && !CollectionUtils.isEmpty(botContext.getPipelineLogs())) {
            // 这里就取第一个
            MainPipelineLog mainPipelineLog = botContext.getPipelineLogs().get(0);
            log.info("getHistoryIds {}", mainPipelineLog.getOutput());
            if (mainPipelineLog.getOutput() != null && mainPipelineLog.getOutput().getFaqRslt() != null && !CollectionUtils.isEmpty(mainPipelineLog.getOutput().getFaqRslt().getFaqIds())) {
                historyIds.addAll(mainPipelineLog.getOutput().getFaqRslt().getFaqIds());
            }
        }

        if (CollectionUtils.isEmpty(historyIds) && !CollectionUtils.isEmpty(shortcutRcmdReq.getHistoryKnowledgeIds())) {
            List<List<Long>> historyKnowledgeIds = shortcutRcmdReq.getHistoryKnowledgeIds().stream().filter(x -> x != null && x.size() > 0).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(historyKnowledgeIds)) {
                historyIds.addAll(historyKnowledgeIds.get(historyKnowledgeIds.size() - 1));
            }
        }

        return historyIds;
    }

    private List<Long> rcmd(BotContext botContext, ShortcutRcmdReq shortcutRcmdReq) {
        List<Long> result = new ArrayList<>();

        try {
            Map<Long, List<Long>> cate2ShortcutMap = knowledgeService.getCate2shortcutMap();

            List<Long> historyIds = getHistoryIds(botContext, shortcutRcmdReq);

            if (CollectionUtils.isEmpty(historyIds) || MapUtils.isEmpty(cate2ShortcutMap)) {
                return result;
            }

            // 这里就简单用历史的第一个返回的类目来做取数据
            for (Long historyKnowledgeId : historyIds) {
                Knowledge knowledge = knowledgeService.getKnowledgeById(historyKnowledgeId);
                if (knowledge != null) {
                    long cate2 = knowledge.getCate2();
                    if (cate2ShortcutMap.containsKey(cate2)) {
                        return cate2ShortcutMap.get(cate2);
                    }
                }
            }
        } catch (Exception e) {
            log.error("shourt rcmd", e);
        }

        return result;
    }
}