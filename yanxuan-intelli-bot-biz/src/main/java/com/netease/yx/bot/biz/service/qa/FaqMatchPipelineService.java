package com.netease.yx.bot.biz.service.qa;

import com.github.houbb.heaven.util.util.CollectionUtil;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.applloconfig.FaqApolloConfig;
import com.netease.yx.bot.biz.service.qa.rank.RankService;
import com.netease.yx.bot.biz.service.qa.recall.RecallServiceWithEs6;
import com.netease.yx.bot.biz.service.qa.recall.RecallServiceWithEs8;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.KnowledgeMatchResp;
import com.netease.yx.bot.core.model.entity.KnowledgeMatchRslt;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FaqMatchPipelineService {

    private static final double STRICT_THRESHOLD = 0.91;
    private static final double KBQA_MIN_THRESHOLD = 0.7;

    @Autowired
    private RecallServiceWithEs8 recallServiceWithEs8;

    @Autowired
    private RecallServiceWithEs6 recallServiceWithEs6;

    @Autowired
    private RankService rankService;

    @Autowired
    private FaqApolloConfig faqApolloConfig;

    @Autowired
    private EnvService envService;

    public KnowledgeMatchResp processWithComplicateModel(BotContext botContext) {
        List<KnowledgeMatchRslt> matchRslts = recallServiceWithEs8.matchWithContextForMultiChannelV2(botContext);
        TextBasicData textBasicData = botContext.getInputs().get(0);

        List<KnowledgeMatchRslt> serviceQaRslt = rankService.serviceQa(matchRslts, textBasicData);
        log.info("after serviceQa {}", serviceQaRslt);
        List<KnowledgeMatchRslt> commonFilter = rankService.commonFaq(serviceQaRslt, textBasicData);
        log.info("after commonFaq {}", commonFilter);
        matchRslts = commonFilter;

        return new KnowledgeMatchResp(1, matchRslts);
    }

    public KnowledgeMatchResp processWithSimpleModel(BotContext botContext) {
        if (envService.isTest()){
            return processWithComplicateModel(botContext);
        }
        List<KnowledgeMatchRslt> matchRslts = recallServiceWithEs8.matchWithContextForMultiChannelV2(botContext);
        TextBasicData textBasicData = botContext.getInputs().get(0);

        List<KnowledgeMatchRslt> results = rankService.leileiFaq(matchRslts, textBasicData);
        CollectionUtil.sort(results);
        return judge(results, textBasicData, 0);
    }


    public KnowledgeMatchResp judge(List<KnowledgeMatchRslt> rslts, TextBasicData queryObj, long sessionId) {
        double threshold = STRICT_THRESHOLD;
        rslts = rslts.stream().limit(faqApolloConfig.getMaxReturnNum(sessionId)).collect(Collectors.toList());
        // 没有结果
        if (rslts.size() == 0) {
            log.info("matchRsltAll zero {}", queryObj);
            return new KnowledgeMatchResp(KnowledgeMatchResp.TYPE_NO_ANSWER, rslts);
        } else {
            List<KnowledgeMatchRslt> finalKnowledgeMatchRsltList = new ArrayList<>();
            for (KnowledgeMatchRslt knowledgeMatchRslt : rslts) {
                if (knowledgeMatchRslt.getScore() > threshold) {
                    finalKnowledgeMatchRsltList.add(knowledgeMatchRslt);
                }
            }
            // 如果第一名超过阈值，且唯一
            if (finalKnowledgeMatchRsltList.size() == 1) {
                log.info("matchRsltAll TYPE_FAQ_PRECISE {}", queryObj);
                return new KnowledgeMatchResp(KnowledgeMatchResp.TYPE_FAQ_PRECISE, finalKnowledgeMatchRsltList);
            } else {
                // 剩下的是模糊匹配
                log.info("matchRsltAll TYPE_FAQ_FUZZY {}", queryObj);
                return new KnowledgeMatchResp(KnowledgeMatchResp.TYPE_FAQ_FUZZY, rslts);
            }
        }
    }
}
