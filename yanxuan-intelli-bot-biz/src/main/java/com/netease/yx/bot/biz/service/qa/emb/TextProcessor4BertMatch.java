package com.netease.yx.bot.biz.service.qa.emb;

import com.github.houbb.segment.support.segment.result.impl.SegmentResultHandlers;
import com.github.houbb.segment.util.SegmentHelper;
import com.hankcs.hanlp.HanLP;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.preprocess.berttokenizer.FullTokenizer;
import com.netease.yx.bot.core.model.entity.faq.MatchTokenizerInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Component
@Slf4j
public class TextProcessor4BertMatch {
    private static final Pattern PATTERN_PUNC = Pattern.compile("[\\p{P}+~$`^=|<>～｀＄＾＋＝｜＜＞￥× 。\\.？\\?，,\\t\\n]");
    private static final Pattern PATTERN_DIGIT = Pattern.compile("\\d+");
    private static final String DIGIT_TOKEN = "digit";
    private Set<String> stopWords;
    private FullTokenizer tokenizer;

    @Value("${rank.bert.stop_words}")
    private String bertMatchStopWordsPath;
    @Value("${rank.bert.vocab}")
    private String bertVocabPath;

    private static boolean isEmojiCharacter(char codePoint) {
        return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA)
                || (codePoint == 0xD)
                || ((codePoint >= 0x20) && (codePoint <= 0xD7FF))
                || ((codePoint >= 0xE000) && (codePoint <= 0xFFFD));
    }

    private static String toDBC(String input) {
        char[] c = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    @PostConstruct
    private void init() {
        try {
            log.info("TextProcessor4BertMatch {} {}", bertMatchStopWordsPath, bertVocabPath);
            stopWords = IoUtils.getLineSetFromFile(bertMatchStopWordsPath);
            log.info("stopwords :{}", stopWords.size());
            tokenizer = new FullTokenizer(bertVocabPath);
        } catch (IOException e) {
            stopWords = new HashSet<>();
            log.error("load TextProcessor4BertMatch error", e);
        }
    }

    public List<String> segment(String sen) {
        return SegmentHelper.segment(sen, SegmentResultHandlers.word());
    }

    public String removeStopWords(String sen) {
        List<String> segmentResult = segment(sen);
        return segmentResult.stream().filter(x -> !stopWords.contains(x)).collect(Collectors.joining());
    }

    public String removeEmoji(String sen) {
        StringBuilder newSen = new StringBuilder(sen.length());
        for (int i = 0; i < sen.length(); i++) {
            char senPoint = sen.charAt(i);
            if (isEmojiCharacter(senPoint)) {
                newSen.append(senPoint);
            }
        }
        return newSen.toString();
    }

    public String clean(String sen) {
        if (StringUtils.isEmpty(sen)) {
            return sen;
        }
        // 去除emoji
        sen = removeEmoji(sen);
        // 繁体转简体
        sen = HanLP.convertToSimplifiedChinese(sen);
        // 去除标点
        sen = PATTERN_PUNC.matcher(sen).replaceAll("");
        // 全角转半角
        sen = toDBC(sen);
        // 大写转小写
        sen = sen.toLowerCase();
        // 数字
        sen = PATTERN_DIGIT.matcher(sen).replaceAll(DIGIT_TOKEN);

        return sen;
    }

    public MatchTokenizerInput getInput(String sen, int maxLength) {
        return tokenizer.getInputs(clean(sen), maxLength);
    }

    public MatchTokenizerInput getInput(String sen, String hst, int maxLength) {
        return tokenizer.getInputs(clean(sen), clean(hst), maxLength);
    }

    public List<MatchTokenizerInput> getInput(List<String> senList, int maxLength) {
        return senList.parallelStream().map(x -> getInput(x, maxLength)).collect(Collectors.toList());
    }
}
