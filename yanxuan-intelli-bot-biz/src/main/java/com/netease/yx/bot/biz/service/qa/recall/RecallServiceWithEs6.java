package com.netease.yx.bot.biz.service.qa.recall;


import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.netease.search.client.common.Result;
import com.netease.search.client.vo.AppInfo;
import com.netease.search.search.ESSearchRequest;
import com.netease.search.search.ESSearchResult;
import com.netease.search.search.util.SearchResponseUtil;
import com.netease.yx.bot.biz.common.DictService;
import com.netease.yx.bot.biz.service.ItemService;
import com.netease.yx.bot.biz.applloconfig.FaqApolloConfig;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.AlgoInfo;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.KnowledgeMatchRslt;
import com.netease.yx.bot.core.model.entity.faq.RecallMatchProcessData;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoAnswer;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoKnowledge;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoSimilarQuestion;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预先保留
 */
@Component
@Slf4j
public class RecallServiceWithEs6 {

    private static final String VALUE_EMPTY = "empty";
    private static final int MAX_LEVEL = 4;
    private static final float ITEM_ID_BOOST = 1.5f;

    private static final String URL = "faq/recall.do";

    @Autowired
    private DictService dictManager;
    @Value("${recall.text.url}")
    private String textUrl;
    @Value("${recall.text.index.appName}")
    private String appName;

    @Autowired
    private FaqApolloConfig faqApolloConfig;

    @Autowired
    private ItemService itemManager;

    public static SearchResponse search(String searchUrl, String appName, String indexName, SearchSourceBuilder searchSourceBuilder) {
        try {
            ESSearchRequest esSearchRequest = new ESSearchRequest();
            AppInfo appInfo = new AppInfo(appName);
            esSearchRequest.setAppInfo(appInfo);
            //传入indice对象
            esSearchRequest.setIndexName(indexName);

            esSearchRequest.setSearchRequest(searchSourceBuilder.toString());
            log.info("searchResquest serial:{}", IoUtils.toJsonString(esSearchRequest));

            //构建HTTP参数对象
            NameValuePair nameValuePair = new BasicNameValuePair("data", IoUtils.toJsonString(esSearchRequest).replace(",\\\"seq_no_primary_term\\\":false", ""));
            List<NameValuePair> parameters = Lists.newArrayList(nameValuePair);
            String response = HttpUtils.executePost(searchUrl, parameters);
            log.info(response);
            //解析HTTP结果
            Result<ESSearchResult> httpResult = IoUtils.parseJson(response, new TypeReference<Result<ESSearchResult>>() {
            });
            if (!httpResult.isSuccess()) {
                log.error("get index error，param result msg {}", httpResult.getErrorMsg());
                throw new RuntimeException(httpResult.getErrorMsg());
            }
            //获取搜索结果
            ESSearchResult esSearchResult = httpResult.getModel();
            //转换为通用对象
            return SearchResponseUtil.getSearchResponse(esSearchResult);
        } catch (Exception e) {
            log.error("search error");
            throw new RuntimeException(e);
        }
    }

    public List<RecallMatchProcessData> recallWithContextMultiChannel(BotContext botContext) {

        TextBasicData textBasicData = botContext.getInputs().get(0);
        List<String> filteredWords = textBasicData.getWords().stream().filter(x -> !dictManager.isStopWord(x)).collect(Collectors.toList());

        // 标准问
        // 文本分词后的匹配字段
        Map<String, String> stdQuestionMatchParams = new HashMap<>(2);
        // 自带的分词器
        stdQuestionMatchParams.put(AlgoKnowledge.CLEAN_STD_QUESTION, textBasicData.getCleanedText());
        // 去除停用词
        stdQuestionMatchParams.put(AlgoKnowledge.CLEAN_STD_QUESTION_TERMS, StringUtils.join(filteredWords, StringUtils.SPACE));
        // 文本内容命中
        BoolQueryBuilder contentMatchShouldBool = QueryBuilders.boolQuery();
        for (Map.Entry<String, String> param : stdQuestionMatchParams.entrySet()) {
            contentMatchShouldBool.should(QueryBuilders.matchQuery(param.getKey(), param.getValue()));
        }

        // 相似问
        Map<String, String> similarQuestionMatchParams = new HashMap<>(2);
        // 自带的分词器
        similarQuestionMatchParams.put(AlgoKnowledge.SIMILAR_QUESTIONS_CLEAN_CONTENT, textBasicData.getCleanedText());
        // 去除停用词
        similarQuestionMatchParams.put(AlgoKnowledge.SIMILAR_QUESTIONS_CLEAN_CONTENT_TERMS, StringUtils.join(filteredWords, StringUtils.SPACE));

        BoolQueryBuilder similarQuestionBoolQueryBuilder = QueryBuilders.boolQuery();

        for (Map.Entry<String, String> param : similarQuestionMatchParams.entrySet()) {
            similarQuestionBoolQueryBuilder.should(QueryBuilders.matchQuery(param.getKey(), param.getValue()));
        }
        NestedQueryBuilder similarQuestionNestedQueryBuilder = QueryBuilders.nestedQuery(AlgoKnowledge.SIMILAR_QUESTIONS, similarQuestionBoolQueryBuilder, ScoreMode.None);
        // 不相关的相似问不返回
        similarQuestionNestedQueryBuilder.innerHit(new InnerHitBuilder().addSort(SortBuilders.scoreSort().order(SortOrder.DESC)));
        contentMatchShouldBool.should(similarQuestionNestedQueryBuilder);

        // 答案条件限定
        BoolQueryBuilder answerQueryBuilder = QueryBuilders.boolQuery();

        // 这里跟上面recallWithContextV2的多商品卡片取最后一个不同，可能是由于外渠单个商品id会映射到主站多个商品导致的，复用了这个字段
        List<Long> itemIds = botContext.getCardItemIds();
        if (CollectionUtils.isEmpty(itemIds) && botContext.getItemId() != 0) {
            // 为空，则添加默认的itemId;
            itemIds = new ArrayList<>();
            itemIds.add(botContext.getItemId());
        }
        if (CollectionUtils.isNotEmpty(itemIds)) {
            BoolQueryBuilder answerScopeQuery = QueryBuilders.boolQuery();

            for (int index = 0; index < itemIds.size(); index++) {
                long itemId = itemIds.get(index);

                // 如果 有itemId，则按 itemId，4级id，3级id，2级id，1级id，通用来给予一定的权重匹配
                if (itemId != 0) {
                    answerScopeQuery.should(QueryBuilders.matchQuery(AlgoKnowledge.ANSWER_ITEM_INFO_LABEL, String.valueOf(itemId)).boost(ITEM_ID_BOOST));
                    // 最后一个商品会额外附加类目筛选
                    if (index == itemIds.size() - 1) {
                        List<String> phyIds = itemManager.phyCateIds(itemId);

                        if (CollectionUtils.size(phyIds) == MAX_LEVEL) {
                            for (int i = 0; i < MAX_LEVEL; i++) {
                                List<String> subPhyIds = phyIds.subList(0, MAX_LEVEL - i);
                                String termMatchIdStr = String.join(StringUtils.SPACE, subPhyIds);
                                answerScopeQuery.should(QueryBuilders.termQuery("answers.itemCateLabel.keyword", termMatchIdStr).boost(ITEM_ID_BOOST - (i + 1) * 0.1f));
                            }
                        }
                    }
                }
            }
            if (itemIds.size() > 1) {
                log.info("multiItem search {} {}", itemIds, botContext.getItemId());
            }
            BoolQueryBuilder commonAnswerScopeQuery = QueryBuilders.boolQuery();
            commonAnswerScopeQuery.must(QueryBuilders.matchQuery(AlgoKnowledge.ANSWER_ITEM_INFO_LABEL_KEYWORD, VALUE_EMPTY));
            commonAnswerScopeQuery.must(QueryBuilders.matchQuery("answers.itemCateLabel.keyword", VALUE_EMPTY));
            // 带商品id筛选 + 带商品id 为empty的筛选
            // 对应商品 + 非商品知识
            answerScopeQuery.should(commonAnswerScopeQuery);

            answerQueryBuilder.must(answerScopeQuery);
        } else {
            // 不带卡片，目前的策略是所有商品的都不匹配
            BoolQueryBuilder commonAnswerScopeQuery = QueryBuilders.boolQuery();
            commonAnswerScopeQuery.must(QueryBuilders.matchQuery(AlgoKnowledge.ANSWER_ITEM_INFO_LABEL_KEYWORD, VALUE_EMPTY));
            commonAnswerScopeQuery.must(QueryBuilders.matchQuery("answers.itemCateLabel.keyword", VALUE_EMPTY));

            answerQueryBuilder.must(commonAnswerScopeQuery);
        }

        long channel = botContext.getChannel();
        BoolQueryBuilder channelQuery = QueryBuilders.boolQuery();
        if (channel != -1) {
            channelQuery.should(QueryBuilders.matchQuery(AlgoKnowledge.ANSWER_CHANNEL, String.valueOf(channel)));
        }
        String platform = botContext.getPlatform();
        if (StringUtils.isNotEmpty(platform)) {
            channelQuery.should(QueryBuilders.matchQuery(AlgoKnowledge.ANSWER_PLATFORM, platform));
        }
        answerQueryBuilder.must(channelQuery);

        // 生效时间的处理
        BoolQueryBuilder expiryTimeQuery = QueryBuilders.boolQuery();
        expiryTimeQuery.should(QueryBuilders.termQuery(AlgoKnowledge.ANSWER_EXPIRY_TIME, 0));
        if (botContext.getConsultTime() > 0) {
            BoolQueryBuilder timeQuery = QueryBuilders.boolQuery();
            timeQuery.must(QueryBuilders.rangeQuery(AlgoKnowledge.ANSWER_EXPIRY_TIME).gte(botContext.getConsultTime()));
            timeQuery.must(QueryBuilders.rangeQuery(AlgoKnowledge.ANSWER_EFFECTIVE_TIME).lte(botContext.getConsultTime()));

            expiryTimeQuery.should(timeQuery);
        } else {
            expiryTimeQuery.should(QueryBuilders.rangeQuery(AlgoKnowledge.ANSWER_EXPIRY_TIME).gte(System.currentTimeMillis()));
        }
        answerQueryBuilder.must(expiryTimeQuery);

        if (channel == 1 && botContext.getSessionInteraction() == 1) {
            // 主站机器人不能直接出 智能解决方案类型的答案
            answerQueryBuilder.mustNot(QueryBuilders.termQuery(AlgoKnowledge.ANSWER_TYPE, AlgoAnswer.ANSWER_TYPE_INTELLI_SOLUTION));
        }

        NestedQueryBuilder answerNestedQueryBuilder = QueryBuilders.nestedQuery(AlgoKnowledge.ANSWERS, answerQueryBuilder, ScoreMode.None);
        // 不相关的答案不返回。
        answerNestedQueryBuilder.innerHit(new InnerHitBuilder());

        // 问题状态限制
        TermQueryBuilder statusQuery = QueryBuilders.termQuery(AlgoKnowledge.STATUS, AlgoKnowledge.STATUS_VALID);

        // 问题用途限制
        // 如果是人工，则对内对外都出
        // 如果是机器人，则只出对外
        QueryBuilder knowledgeUseQuery = botContext.getSessionInteraction() == 1 ? QueryBuilders.termQuery(AlgoKnowledge.KNOWLEDGE_USE, AlgoKnowledge.USE_OUT) :
                QueryBuilders.boolQuery().should(QueryBuilders.termQuery(AlgoKnowledge.KNOWLEDGE_USE, AlgoKnowledge.USE_OUT)).should(QueryBuilders.termQuery(AlgoKnowledge.KNOWLEDGE_USE, AlgoKnowledge.USE_INTERNAL));

        // 综合所有条件
        BoolQueryBuilder finalQuery = QueryBuilders.boolQuery();

        // 必备条件
        finalQuery.must(knowledgeUseQuery);
        // 必备条件
        finalQuery.must(answerNestedQueryBuilder);
        //
        finalQuery.must(statusQuery);
        // 问题类型
//        finalQuery.must(knowledgeTypeQuery);

        // 内容匹配
        finalQuery.must(contentMatchShouldBool);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.matchAllQuery());
        searchSourceBuilder.size(20);

//        String[] includes = new String[]{
//                AlgoKnowledge.KNOWLEDGE_ID,
//                AlgoKnowledge.STD_QUESTION
//        };
//        searchSourceBuilder.fetchSource(includes, null);

        log.info("query {}", searchSourceBuilder);

        List<AlgoKnowledge> list = new ArrayList<>();
        List<RecallMatchProcessData> recallMatchProcessDataList = new ArrayList<>();
        try {

            log.info("query {}", textUrl);
            SearchResponse response = search(textUrl, appName, faqApolloConfig.getAlgoKnowledgeIndex(), searchSourceBuilder);

            for (SearchHit hit : response.getHits().getHits()) {
                log.info(hit.toString());
                AlgoKnowledge algoKnowledge = IoUtils.parseJson(IoUtils.toJsonString(hit.getSourceAsMap()), AlgoKnowledge.class);
                algoKnowledge.setAnswers(new ArrayList<>());
                if (hit.getInnerHits().get(AlgoKnowledge.ANSWERS).getHits().length > 0) {
                    for (SearchHit searchHit : hit.getInnerHits().get(AlgoKnowledge.ANSWERS).getHits()) {
                        algoKnowledge.getAnswers().add(IoUtils.parseJson(IoUtils.toJsonString(searchHit.getSourceAsMap()), AlgoAnswer.class));
                    }
                }
                algoKnowledge.setSimilarQuestions(new ArrayList<>());
                if (hit.getInnerHits().get(AlgoKnowledge.SIMILAR_QUESTIONS).getHits().length > 0) {
                    for (SearchHit searchHit : hit.getInnerHits().get(AlgoKnowledge.SIMILAR_QUESTIONS).getHits()) {
                        algoKnowledge.getSimilarQuestions().add(IoUtils.parseJson(IoUtils.toJsonString(searchHit.getSourceAsMap()), AlgoSimilarQuestion.class));
                    }
                }
                list.add(algoKnowledge);

                String id = String.valueOf(algoKnowledge.getKnowledgeId());
                String question = algoKnowledge.getStdQuestion();
                String answerId = null;
                String itemInfo = null;
                String cateInfo = null;
                // 怕出现一条knowledge下有， itemid， cateid， 通用等多种情况出现的时候
                // 取第一个
                if (CollectionUtils.size(algoKnowledge.getAnswers()) >= 1) {
                    AlgoAnswer algoAnswer = algoKnowledge.getAnswers().get(0);
                    answerId = String.valueOf(algoAnswer.getAnswerId());
                    itemInfo = algoAnswer.getItemInfoLabel();
                    cateInfo = algoAnswer.getItemCateLabelStr();
                }
                if (CollectionUtils.size(algoKnowledge.getAnswers()) > 1) {
                    log.warn("answer size > 1");
                }
                String stdQuestionCorpusId = StringUtils.join(AlgoInfo.CORPUS_ID_PREFFIX_STANDARD, id);
                if (StringUtils.equals(itemInfo, VALUE_EMPTY) || StringUtils.isEmpty(itemInfo)) {
                    itemInfo = null;
                } else {
                    if (CollectionUtils.isNotEmpty(itemIds)) {
                        itemInfo = String.valueOf(itemIds.get(0));
                    }
                }

                if (StringUtils.equals(cateInfo, VALUE_EMPTY) || StringUtils.isEmpty(cateInfo)) {
                    cateInfo = null;
                } else {
                    List<String> cateIds = Arrays.stream(cateInfo.split(StringUtils.SPACE)).collect(Collectors.toList());
                    cateInfo = cateIds.get(cateIds.size() - 1);
                }

                recallMatchProcessDataList.add(new RecallMatchProcessData(id, question, stdQuestionCorpusId, itemInfo, cateInfo, answerId, hit.getScore()));

                // 相似问的第一个
                if (CollectionUtils.isNotEmpty(algoKnowledge.getSimilarQuestions())) {
                    AlgoSimilarQuestion algoSimilarQuestion = algoKnowledge.getSimilarQuestions().get(0);
                    String similarQuestionCorpusId = StringUtils.join(AlgoInfo.CORPUS_ID_PREFFIX_SIMILAR, id, "-", algoSimilarQuestion.getId());
//                            log.info("recallWithContextMultiChannel {}", similarQuestionCorpusId);
                    recallMatchProcessDataList.add(new RecallMatchProcessData(id, algoSimilarQuestion.getContent(), similarQuestionCorpusId, itemInfo, cateInfo, answerId, hit.getScore()));
                }
            }
            log.info("recallWithContextMultiChannel {}", IoUtils.toJsonString(list));
        } catch (IOException e) {
            log.error("recallWithContextMultiChannel", e);
        }

        return recallMatchProcessDataList;
    }
}
