package com.netease.yx.bot.biz.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.BotResp;
import com.netease.yx.bot.core.model.entity.rgVisible.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@EnableScheduling
public class SmartRGVisibleService {
    private static final String HEADER_KEY_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_VALUE_CONTENT_TYPE = "application/json";
    private static final String HEADER_KEY_HOST_TYPE = "host";
    private static final String HOST = "aibot-smart-rg-visible.yx-serving.svc";
    private static final String URL = "http://smart-infer.hz.infra.mail:31938/v1/models/aibot-smart-rg-visible:predict";

    private static final String unFinishTicketStatus = "1,2,100";
    private static final Integer DISABLE_TICKET_STATUS = 0;
    private static final float EMO_SCORE = 0.5f;
    private final List<String> UNFINISH_TICKET_STATUS = new ArrayList<>(Arrays.asList(unFinishTicketStatus.split(",")));
    private float smartRGVisibleQueueMainCount = 0f;
    private int smartRGVisibleQueueMainStatus = 0;
    private SmartRGVisibleRule smartRGVisiblePresaleRule;
    private SmartRGVisibleRule smartRGVisibleAftersaleRule;
    private List<Long> smartRGVisibleKFGroupIds = new ArrayList<>();
    private Map<Long, Float> smartRGVisibleQueueMaxValidCount = new HashMap<>();

    private List<OnlineSessionVO> kfGroupStatus;

    @Value("${rg.smart.visible.kfgroup.status.url}")
    private String rgSmartVisibleKFGroupStatusURL;
    @Value("${rg.smart.visible.aftersale.rule.path}")
    private String rgSmartVisibleAftersaleRulePath;
    @Value("${rg.smart.visible.presale.rule.path}")
    private String rgSmartVisiblePresaleRulePath;

    @Autowired
    private BotApolloConfig botApolloConfig;
    @Autowired
    private UserService userService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @PostConstruct
    private void init() {
        updateKFGroupStatus();
        loadSmartRGVisibleRule();
    }

    private void loadRuleFromLocal(boolean isPresale) {
        try {
            if (isPresale) {
                smartRGVisiblePresaleRule = IoUtils.loadJsonFromFile(rgSmartVisiblePresaleRulePath, new TypeReference<SmartRGVisibleRule>() {
                });
            } else {
                smartRGVisibleAftersaleRule = IoUtils.loadJsonFromFile(rgSmartVisibleAftersaleRulePath, new TypeReference<SmartRGVisibleRule>() {
                });

            }
        } catch (Exception e) {
            log.error("SmartRGVisibleManager.loadRuleFromLocal error.", e);
        }
    }

    private void loadSmartRGVisibleRule() {
        smartRGVisibleAftersaleRule = botApolloConfig.getSmartRGVisibleRule(false);
        if (smartRGVisibleAftersaleRule == null) {
            loadRuleFromLocal(false);
        }

        smartRGVisiblePresaleRule = botApolloConfig.getSmartRGVisibleRule(true);
        if (smartRGVisiblePresaleRule == null) {
            loadRuleFromLocal(true);
        }
    }


    public boolean getVisible(BotResp botResp, BotContext botContext, List<Long> faqIds, Long groupId) {
        if (groupId == null) {
            log.info("SmartRGVisibleManager.getVisible: groupId is null");
            return false;
        }
        try {
            // 加载规则
            loadSmartRGVisibleRule();

            // 生成必要的用户信息
            SmartRGVisibleUserInfo smartRGVisibleUserInfo = genUserInfo(botContext, faqIds);
//            log.info("SmartRGVisibleManager.SmartRGVisibleUserInfo: {}", smartRGVisibleUserInfo);

            // 模型得分
            float modelRGVisibleScore = model(smartRGVisibleUserInfo);
//            log.info("SmartRGVisibleManager.modelRGVisibleScore: {}", modelRGVisibleScore);

            // 规则得分
            float ruleRGVisibleScore;
            if (botContext.isPreSale()) {
                ruleRGVisibleScore = rule(smartRGVisibleUserInfo, smartRGVisiblePresaleRule);
            } else {
                ruleRGVisibleScore = rule(smartRGVisibleUserInfo, smartRGVisibleAftersaleRule);
            }
//            log.info("SmartRGVisibleManager.ruleRGVisibleScore: {}", ruleRGVisibleScore);

            // 当前情绪得分
            float emoRGVisibleScore = emoRule(botResp);
//            log.info("SmartRGVisibleManager.emoRGVisibleScore: {}", emoRGVisibleScore);

            // 总分
            float rgVisibleScore = modelRGVisibleScore + ruleRGVisibleScore + emoRGVisibleScore;
//            log.info("SmartRGVisibleManager.rgVisibleScore: {}", rgVisibleScore);

            Float smartRGVisibleThreshold = 2f;
            if (botApolloConfig.getSmartRGVisibleThreshold().size() == 2) {
                if (botContext.isPreSale()) {
                    smartRGVisibleThreshold = botApolloConfig.getSmartRGVisibleThreshold().get(0);
                } else {
                    smartRGVisibleThreshold = botApolloConfig.getSmartRGVisibleThreshold().get(1);
                }
            }
//            log.info("SmartRGVisibleManager.getSmartRGVisibleThreshold, {}", smartRGVisibleThreshold);

            if (rgVisibleScore >= smartRGVisibleThreshold) {
//                log.info("SmartRGVisibleManager.groupId {}", groupId);
                List<OnlineSessionVO> validSessionVOList = kfGroupStatus.stream().filter(x -> x.getGroupId() == groupId).collect(Collectors.toList());

//                log.info("SmartRGVisibleManager.validSessionVO {}", validSessionVOList);
                if (validSessionVOList.size() > 0 | groupId == 0) {

                    int diff;
                    float minQueueCount = botApolloConfig.getSmartRGVisibleSessionCount();
                    // 若groupId=0，未分配客服，走通用客服组
                    if (groupId == 0) {
                        diff = smartRGVisibleQueueMainStatus;
                        minQueueCount = Math.min(minQueueCount, smartRGVisibleQueueMainCount);
                    } else {
                        OnlineSessionVO validSessionVO = validSessionVOList.get(0);
                        // 如果坐席为-1/0，则表示当前客服组没有客服， 走通用客服组
                        if (validSessionVO.getOnlineStatus() == -1 || validSessionVO.getOnlineStatus() == 0) {
                            diff = smartRGVisibleQueueMainStatus;
                            minQueueCount = Math.min(minQueueCount, smartRGVisibleQueueMainCount);
                        } else {
                            diff = validSessionVO.getQueueSize();
                            if (smartRGVisibleQueueMaxValidCount.containsKey(groupId)) {
                                minQueueCount = Math.min(minQueueCount, smartRGVisibleQueueMaxValidCount.get(groupId));
                            }
                        }
                    }
//                    log.info("SmartRGVisibleManager.minQueueCount {} & diff {}", minQueueCount, diff);

                    return diff <= minQueueCount;
                }
            }
        } catch (Exception e) {
            log.error("SmartRGVisibleManager.getVisible error", e);
        }

        return false;

    }

    @Scheduled(cron = "1 * * * * *")
    private void updateKFGroupStatus() {
        try {
            String kfGroupStatusString = HttpUtils.executeGet(rgSmartVisibleKFGroupStatusURL);
            OnlineSessionVOResp kfGroupStatusResp = IoUtils.parseJson(kfGroupStatusString, OnlineSessionVOResp.class);
            if (kfGroupStatusResp != null && OnlineSessionVOResp.SUCCESS_CODE == kfGroupStatusResp.getCode()) {
                kfGroupStatus = kfGroupStatusResp.getData();
                smartRGVisibleQueueMainCount = 0f;
                smartRGVisibleQueueMainStatus = 0;
                smartRGVisibleKFGroupIds = botApolloConfig.getSmartRGVisibleKFGroupIds();

                for (OnlineSessionVO onlineSessionVO : kfGroupStatus) {
                    smartRGVisibleQueueMaxValidCount.put(onlineSessionVO.getGroupId(), onlineSessionVO.getOnlineStatus() * botApolloConfig.getSmartRGVisibleSessionCoefficient());

                    // 主站-访客分流 单独计算
                    if (smartRGVisibleKFGroupIds.contains(onlineSessionVO.getGroupId())) {
                        smartRGVisibleQueueMainStatus += onlineSessionVO.getQueueSize();
                        smartRGVisibleQueueMainCount += onlineSessionVO.getOnlineStatus() * botApolloConfig.getSmartRGVisibleSessionCoefficient();
                    }
                }

//                log.debug("SmartRGVisibleManager.updateKFGroupStatus: success");
//                log.debug("SmartRGVisibleManager.updateKFGroupStatus smartRGVisibleQueueCount: {}", smartRGVisibleQueueMaxValidCount);
//                log.debug("SmartRGVisibleManager.updateKFGroupStatus groupID 0 : {}", smartRGVisibleQueueMainStatus);
//                log.debug("SmartRGVisibleManager.updateKFGroupStatus groupID 0 : {}", smartRGVisibleQueueMainCount);
//                log.debug("SmartRGVisibleManager.updateKFGroupStatus kfGroupStatus: {}", kfGroupStatus);
            } else {
                kfGroupStatus = new ArrayList<>();
//                log.debug("error in SmartRGVisibleManager: kfgroup status get fail. {}", kfGroupStatusString);
            }

        } catch (Exception e) {
            kfGroupStatus = new ArrayList<>();
            log.error("error in SmartRGVisibleManager: kfgroup status get fail. ", e);
        }
    }

    private SmartRGVisibleResp requestSmartRGVisibleService(SmartRGVisibleReq smartRGVisibleReq, Map<String, String> header) {
        SmartRGVisibleResp smartRGVisibleResp = null;

        try {
            String response = HttpUtils.executePost(URL, IoUtils.toJsonString(smartRGVisibleReq), null, header);
            smartRGVisibleResp = IoUtils.parseJson(response, SmartRGVisibleResp.class);
        } catch (Exception e) {
            log.error("error in requestSmartRGVisibleService: smartwork request error", e);
        }
        return smartRGVisibleResp;
    }


    private Map<String, String> buildSmartworkHeaders() {
        Map<String, String> headers = new HashMap<>(2);
        headers.put(HEADER_KEY_CONTENT_TYPE, HEADER_VALUE_CONTENT_TYPE);
        headers.put(HEADER_KEY_HOST_TYPE, HOST);
        return headers;
    }


    private SmartRGVisibleUserInfo genUserInfo(BotContext botContext, List<Long> faqIds) {

        // 从redis 获取用户历史的访问记录
        int robotCount24H = 0;
        int robotCount48H = 0;
        String userAccessKey = BotContext.buildUserRobotAccessKey(botContext.getUserId());
        String userAccessHst = stringRedisTemplate.opsForValue().get(userAccessKey);
//        log.info("SmartRGVisibleManager.genUserInfo.userAccessHst: {}", userAccessHst);
        if (userAccessHst != null && !userAccessHst.equals("")) {
            List<String> userAccessTimeSeries = Arrays.stream(userAccessHst.split(",")).collect(Collectors.toList());
            long currentST = System.currentTimeMillis() / 1000;
            for (String ts : userAccessTimeSeries) {
                if (ts.length() >= 10) {
                    if (currentST - Long.parseLong(ts) > 24 * 60 * 60) {
                        robotCount24H += 1;
                    }
                    if (currentST - Long.parseLong(ts) > 48 * 60 * 60) {
                        robotCount48H += 1;
                    }
                }
            }
            if (robotCount24H != 0) {
                robotCount24H = robotCount24H - 1;
            }
            if (robotCount48H != 0) {
                robotCount48H = robotCount48H - 1;
            }
        }

        // 获取用户在当前会话的人工次数
        String sessRGKey = BotContext.buildRequestRGKey(botContext.getSessionId());
        String sessRG = stringRedisTemplate.opsForValue().get(sessRGKey);
        int rgReqCount = 0;
        if (sessRG != null) {
            rgReqCount = Integer.parseInt(sessRG);
        }

        // 获取当前会话的所有意图
        String sessIntentKey = BotContext.buildRequestIntentKey(botContext.getSessionId());
        String sessIntent = stringRedisTemplate.opsForValue().get(sessIntentKey);

        List<String> sessReqIntents = new ArrayList<>();
        if (sessIntent != null) {
            sessReqIntents.addAll(Arrays.asList(sessIntent.split(",")));
        }

        Map<String, Long> repeatIntents = new HashMap<>();
        sessReqIntents.forEach(x -> repeatIntents.compute(x, (k, v) -> v == null ? 1L : v + 1L));

        // 对话轮次
        int interactRounds = 0;
        if (faqIds == null) {
            interactRounds = botContext.getHstTexts().size();
        } else {
            if (botContext.getHstTexts() == null) {
                interactRounds = faqIds.size();
            } else {
                interactRounds = botContext.getHstTexts().size() + faqIds.size();
            }
        }

        return SmartRGVisibleUserInfo.builder()
                .directInput(botContext.getHstTexts())
                .clickFaqs(faqIds)
                .rgCount24H(botContext.getUserRGCount24H())
                .rgCount48H(botContext.getUserRGCount48H())
                .robotCount24H(robotCount24H)
                .robotCount48H(robotCount48H)
                .interactCount(interactRounds)
                .rgReqCount(rgReqCount)
                .sessReqIntents(repeatIntents)
                .userCredit(botContext.getUserR())
                .userProLevel(botContext.getUserV())
                .ticketStatus(botContext.getTicketStatus())
                .isPro(userService.checkUserPro(botContext.getUserId()))
                .build();
    }


    private float rule(SmartRGVisibleUserInfo smartRGVisibleUserInfo, SmartRGVisibleRule smartRGVisibleRule) {
        // 用户信誉等级
        int userCredit = smartRGVisibleUserInfo.getUserCredit();
        if (smartRGVisibleUserInfo.isPro()) {
            userCredit = 0;
        }
        float userCreditScore = 0;
        if (userCredit >= 0 && userCredit <= 5) {
            userCreditScore = smartRGVisibleRule.getCredit().get(userCredit);
        } else {
            return userCreditScore;
        }

        //重复咨询意图
        float repeatIntentScore = 0;
        if (smartRGVisibleUserInfo.getSessReqIntents().size() > 0) {
            Long repeatCount = smartRGVisibleUserInfo.getSessReqIntents().entrySet().stream().max(Map.Entry.comparingByValue()).get().getValue();
            if (repeatCount >= smartRGVisibleRule.getRepeatIntent().get(userCredit)) {
                repeatIntentScore = smartRGVisibleRule.getRepeatIntentScore();
            }
        }

        //指令人工
        float requestRGScore = 0;
        if (smartRGVisibleUserInfo.getRgReqCount() >= smartRGVisibleRule.getRgRequest().get(userCredit)) {
            requestRGScore = smartRGVisibleRule.getRgRequestScore();
        }

        //完结工单
        float ticketScore = 0;
        if (UNFINISH_TICKET_STATUS.contains(String.valueOf(smartRGVisibleUserInfo.getTicketStatus()))
                && (!DISABLE_TICKET_STATUS.equals(smartRGVisibleRule.getTicket().get(userCredit)))) {
            ticketScore = smartRGVisibleRule.getTicketScore();
        }

        // 交互次数
        float interactCountScore = 0;
        if (smartRGVisibleUserInfo.getInteractCount() >= smartRGVisibleRule.getInteractCount().get(userCredit)) {
            interactCountScore += smartRGVisibleRule.getInteractCountScore();
        }

        // 购买力
        float buyPowerScore = 0;
        int userBuyPower = smartRGVisibleUserInfo.getUserProLevel();
        if (smartRGVisibleUserInfo.isPro()) {
            userBuyPower = 0;
        }
        if (userBuyPower >= smartRGVisibleRule.getBuyPower().get(userCredit)) {
            buyPowerScore += smartRGVisibleRule.getBuyPowerScore();
        }

//        log.info("SmartRGVisibleManager.rule: userCreditScore {}, repeatIntentScore {}, requestRGScore {}, " +
//                        "ticketScore {}, interactCountScore {}, buyPowerScore {}",
//                userCreditScore, repeatIntentScore, requestRGScore, ticketScore, interactCountScore, buyPowerScore);

        return userCreditScore + repeatIntentScore + requestRGScore + ticketScore + interactCountScore + buyPowerScore;
    }

    private float emoRule(BotResp botResp) {
        if ((botResp.getChatIntent() != null) && (ChatService.CHAT_COMPLAINT.equals(botResp.getChatIntent()))) {
            return EMO_SCORE;
        }

        if (botResp.getFaqRslt() != null && botResp.getFaqRslt().getFaqIds() != null && (botResp.getFaqRslt().getFaqIds().contains(20254319))) {
            return EMO_SCORE;
        }

        return 0;
    }

    private float model(SmartRGVisibleUserInfo smartRGVisibleUserInfo) {
        SmartRGVisibleReq smartRGVisibleReq = SmartRGVisibleReq.builder()
                .directInput(smartRGVisibleUserInfo.getDirectInput())
                .clickFaqs(smartRGVisibleUserInfo.getClickFaqs())
                .rgCount24H(smartRGVisibleUserInfo.getRgCount24H())
                .rgCount48H(smartRGVisibleUserInfo.getRgCount48H())
                .robotCount24H(smartRGVisibleUserInfo.getRobotCount24H())
                .robotCount48H(smartRGVisibleUserInfo.getRobotCount48H())
                .interactCount(smartRGVisibleUserInfo.getInteractCount())
                .build();

        // 请求模型结果
        Map<String, String> header = buildSmartworkHeaders();
        SmartRGVisibleResp smartRGVisibleResp = requestSmartRGVisibleService(smartRGVisibleReq, header);

        float modelScore = 10f;
        if (SmartRGVisibleResp.SUCCESS.equals(smartRGVisibleResp.getStatus())) {
            modelScore = smartRGVisibleResp.getPredictions();
        } else {
            log.info("SmartRGVisibleManager.model service fail. {}", smartRGVisibleResp.getError());
        }

        return modelScore;
    }

}
