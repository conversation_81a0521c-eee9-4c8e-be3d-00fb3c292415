package com.netease.yx.bot.biz.manager;

import com.netease.yanxuan.log.api.statlog.StatLogger;
import com.netease.yanxuan.log.api.statlog.StatLoggerFactory;
import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.common.NlpServerService;
import com.netease.yx.bot.biz.common.RedisService;
import com.netease.yx.bot.biz.common.SmartWorkService;
import com.netease.yx.bot.biz.service.*;
import com.netease.yx.bot.biz.service.qa.FaqMatchPipelineService;
import com.netease.yx.bot.biz.service.qa.ItemAttributeService;
import com.netease.yx.bot.biz.service.qa.recall.RecallServiceWithEs8;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.constant.IntentTypeV2;
import com.netease.yx.bot.core.model.constant.Level1IntentType;
import com.netease.yx.bot.core.model.constant.RedisKey;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.CoreIntent;
import com.netease.yx.bot.core.model.entity.KnowledgeMatchResp;
import com.netease.yx.bot.core.model.entity.KnowledgeMatchRslt;
import com.netease.yx.bot.core.model.entity.channel.*;
import com.netease.yx.bot.core.model.entity.chat.ChatType;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import com.netease.yx.bot.core.model.entity.search.SearchItemInfo;
import com.netease.yx.bot.core.model.entity.search.SearchReq;
import com.netease.yx.bot.core.model.entity.search.SearchResp;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import com.netease.yx.bot.core.model.log.MultiChannelPipelineLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 全渠道问答
 */
@Slf4j
@Service
@EnableScheduling
public class ChannelQaManager {
    private static final String LOG_TOPIC = "BOT_MULTI_CHANNEL_PIPELINE";
    private static final String LOG_MODULE = "ChannelQaService";
    private static final String JD_COMMOM_PATTERN = "item.jd.com/(\\d*).html";

    @Autowired
    private EnvService envService;
    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;
    @Autowired
    private RecallServiceWithEs8 recallServiceWithEs8;
    @Autowired
    private TextPreprocessService textPreprocessService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IntentProductService intentProductService;

    @Autowired
    private ChannelSearchManager channelSearchManager;

    @Autowired
    private SmartWorkService smartWorkService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private ItemAttributeService itemAttributeService;

    @Autowired
    private NlpServerService nlpServerService;

    @Autowired
    private ChatService chatService;

    @Autowired
    private TestMockManager testMockManager;

    @Autowired
    private IntentServiceV2 intentServiceV2;

    private List<Pattern> itemIdPattens;

    @Autowired
    private FaqMatchPipelineService faqMatchPipelineService;

    @PostConstruct
    private void init() {
        updateItemIdPatterns();
    }

    @Scheduled(cron = "0 */5 * * * *")
    private void updateItemIdPatterns() {
        List<String> regexList = channelQaApolloConfig.getItemIdRegexList();
        log.info("updateItemIdPatterns {}", regexList);
        List<Pattern> patterns = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(regexList)) {
            for (String regex : regexList) {
                patterns.add(Pattern.compile(regex));
            }
        }
        this.itemIdPattens = patterns;
    }

    public int processAddi(ChannelQaAddiReq channelQaAddiReq) {
        String key = BotContext.buildContextKey(channelQaAddiReq.getSessionId());
        String value = redisService.get(key);
        BotContext botContext = new BotContext();

        try {
            log.info("processAddi {}", value);
            if (StringUtils.isNotEmpty(value)) {
                botContext = IoUtils.parseJson(value, BotContext.class);
            }

            // 如果还没有context，则生成一个新的
            if (botContext == null) {
                botContext = new BotContext();
            }

            // 如果itemId不为空，则更新context
            if (channelQaAddiReq.getItemId() != 0) {
                botContext.setItemId(channelQaAddiReq.getItemId());
            }
            if (StringUtils.isNotEmpty(channelQaAddiReq.getPlatformItemId())) {
                botContext.setPlatformItemId(channelQaAddiReq.getPlatformItemId());
                botContext.setPlatformRawItemCardId(channelQaAddiReq.getPlatformRawItemCardId());
                botContext.setItemPhyCategoryIdStr(channelQaAddiReq.getItemPhyCategoryIdStr());
            }
            // 如果订单id不为空，则更新context
            if (StringUtils.isNotEmpty(channelQaAddiReq.getOrderId())) {
                botContext.setMultiChannelOrderId(channelQaAddiReq.getOrderId());
            }
            // 1对多的情况
            if (CollectionUtils.isNotEmpty(channelQaAddiReq.getItemIds())) {
                botContext.setCardItemIds(channelQaAddiReq.getItemIds());
            }

            redisService.set(key, IoUtils.toJsonString(botContext));
        } catch (IOException e) {
            log.error("processAddi", e);
            return 0;
        }
        return 1;
    }

    public ChannelQaResp pipeline(ChannelQaReq channelQaReq) {
        // platformMessageId 到 platformSessionId的存储，后续生成训练任务有用
        redisService.set(StringUtils.join(RedisKey.MSG_2_PLATFORM_SESSION_ID, channelQaReq.getMessageId()),
                channelQaReq.getSessionId());
        // 过滤source
        if (channelQaReq.getMessageSource() != MessageSource.CUSTOMER.getCode() && channelQaReq.getMessageSource() != MessageSource.SYSTEM.getCode()) {
            return new ChannelQaResp();
        }
        // 过滤type
        if (!MessageType.checkNeedProcess(channelQaReq.getMessageType())) {
            return new ChannelQaResp();
        }
        // 机器人最多5个
        if (channelQaReq.getChannelId() == 0 && channelQaReq.getSessionInteraction() == 1) {
            channelQaReq.setLimit(5);
        }

        // 转换输入数据为新格式
        channelQaReq.setMessageContent(transferToMessageContentObj(channelQaReq));
        // 如果没有传入要指定的咨询时间，则以当前时间
        if (channelQaReq.getConsultTime() == 0) {
            channelQaReq.setConsultTime(channelQaReq.getTimestamp());
        }
        log.info("message content {}", channelQaReq.getMessageContent());
        // 过滤不需要的数据
        if (CollectionUtils.isNotEmpty(channelQaApolloConfig.getIgnoreContents()) && channelQaApolloConfig.getIgnoreContents().contains(channelQaReq.getMessageContent().getText())) {
            return new ChannelQaResp();
        }
        // 消息处理缓存
        // batch 不取缓存，全部重新算
        if (!envService.isTest() && !channelQaReq.isBatch()) {
            String multiChannelPipelineLogStr = redisService.get(
                    StringUtils.join(RedisKey.MSG_2_PIPELINE_LOG, channelQaReq.getMessageId()));
            if (StringUtils.isNotEmpty(multiChannelPipelineLogStr)) {
                try {
                    MultiChannelPipelineLog multiChannelPipelineLog = IoUtils.parseJson(multiChannelPipelineLogStr, MultiChannelPipelineLog.class);
                    ChannelQaResp channelQaResp = multiChannelPipelineLog.getOutput();
                    if (ObjectUtils.isNotEmpty(channelQaResp) && channelQaResp.getIntentTypeV2() != null) {
                        log.info("hit qa cache {}", multiChannelPipelineLogStr);
                        return channelQaResp;
                    }
                } catch (IOException e) {
                    log.error("hit qa cache", e);
                }
            }
        }

        StatLogger statLogger = StatLoggerFactory.getLogger(LOG_MODULE);

        // 正式的处理链路都在这里
        MultiChannelPipelineLog multiChannelPipelineLog = pipelineWithLog(channelQaReq);
        // 正式的内容才会用于生成训练任务
        // 用户的输入才会被生成训练任务
        if (!channelQaReq.isTestMode() && channelQaReq.getMessageSource() <= MessageSource.CUSTOMER.getCode()) {
            statLogger.log(LOG_TOPIC, MultiChannelPipelineLog.buildMap(multiChannelPipelineLog));
        }

        // 记录pipelinelog到redis中， 作为后续的上下文
        // 作为实时生成训练任务的依据
        if (multiChannelPipelineLog != null) {
            BotContext botContext = multiChannelPipelineLog.getContext();
            if (botContext != null) {
                List<MultiChannelPipelineLog> pipelineLogs = botContext.getMultiChannelPipelineLogs();
                // 如果历史为空，则新生成一个列表
                if (pipelineLogs == null) {
                    pipelineLogs = new ArrayList<>();
                }
                // 为避免循环依赖
                BotContext cloneBotContext = SerializationUtils.clone(botContext);
                cloneBotContext.setMultiChannelPipelineLogs(null);
                cloneBotContext.setPipelineLogs(null);
                multiChannelPipelineLog.setContext(cloneBotContext);

                pipelineLogs.add(0, multiChannelPipelineLog);
                // 最多保留最近的3个
                botContext.setMultiChannelPipelineLogs(pipelineLogs.stream().limit(3).collect(Collectors.toList()));

                String contextKey = BotContext.buildContextKey(channelQaReq.getSessionId());
                log.info("reset context");
                // 保存当前上文总的状态
                redisService.set(contextKey, IoUtils.toJsonString(botContext), 60 * 24);
                // 消息粒度的日志入库
                redisService.set(StringUtils.join(RedisKey.MSG_2_PIPELINE_LOG, channelQaReq.getMessageId()), IoUtils.toJsonString(multiChannelPipelineLog), 60 * 24);
                // 会话粒度的日志入库，给七鱼-机器人使用
                redisService.set(StringUtils.join(RedisKey.SESSION_2_PIPELINE_LOG, channelQaReq.getSessionId()), IoUtils.toJsonString(multiChannelPipelineLog), 30);
            }
        }

        return multiChannelPipelineLog.getOutput();
    }

    private IntentTypeV2 transfer(ChannelQaAddiReq channelQaAddiReq, IntentRslt intentRslt) {
        if (StringUtils.isNotEmpty(channelQaAddiReq.getPlatformItemId())) {
            return IntentTypeV2.ITEM_CARD;
        }
        if (StringUtils.isNotEmpty(channelQaAddiReq.getOrderId())) {
            return IntentTypeV2.ORDER_CARD;
        }
        switch (intentRslt.getFirstIntent()) {
            case FAQ:
                return IntentTypeV2.FAQ;
            case KBQA:
                return IntentTypeV2.KBQA;
            case CHITCHAT:
                return IntentTypeV2.CHITCHAT;
            case SPECIAL:
                return IntentTypeV2.SPECIAL;
            case GUIDE:
                return IntentTypeV2.GUIDE;
        }
        return IntentTypeV2.UNKNOWN;
    }

    private MessageContent transferToMessageContentObj(ChannelQaReq channelQaReq) {
        if (!channelQaReq.isTestMode()) {
            // 老的逻辑
            if (channelQaReq.getMessageType() == MessageType.OTHER.getCode()) {
                return new MessageContent(channelQaReq.getRawMsg());
            }
            // 老的逻辑
            if (CollectionUtils.isNotEmpty(channelQaApolloConfig.getFormatOldPlatforms()) && channelQaApolloConfig.getFormatOldPlatforms().contains(channelQaReq.getPlatform())) {
                return new MessageContent(channelQaReq.getRawMsg());
            }
        }

        MessageContent messageContent = null;
        try {
            messageContent = IoUtils.parseJson(channelQaReq.getRawMsg(), MessageContent.class);
            // 也用id 填充一下
            if (channelQaReq.getMessageType() == MessageType.GOOD_CARD.getCode()) {
                messageContent.setText(messageContent.getItemList().get(0).getItemId());
            } else if (channelQaReq.getMessageType() == MessageType.ORDER_CARD.getCode()) {
                messageContent.setText(messageContent.getOrderList().get(0).getOrderId());
            }
        } catch (IOException e) {
            messageContent = new MessageContent(channelQaReq.getRawMsg());
            log.info("transfer", e);
        }
        return messageContent;
    }

    public MultiChannelPipelineLog pipelineWithLog(ChannelQaReq channelQaReq) {
        // 尝试解析可能有用的信息，比如商品卡片等，先更新或者生成一个 context
        // 消息粒度
        ChannelQaAddiReq channelQaAddiReq = extractFromMessage(channelQaReq);
        log.info("channelQaAddiReq {}", IoUtils.toJsonString(channelQaAddiReq));

        boolean cardInfoFlag = false;
        // 系统卡片目前统一处理为卡片，避免误匹配
        if (channelQaReq.getMessageSource() == MessageSource.SYSTEM.getCode()) {
            log.info("system card");
            cardInfoFlag = true;
        }

        if (ObjectUtils.isNotEmpty(channelQaAddiReq) && StringUtils.isNotEmpty(channelQaAddiReq.getPlatformRawItemCardId())) {
            // 把itemId等信息更新到context里面
            processAddi(channelQaAddiReq);
            cardInfoFlag = true;
        } else {
            try {
                // 前端过来的消息，可能一个会话里的多条消息并发过来，这时候需要先处理商品卡片，再处理其他消息
                // 处理方式是让其他消息等待一段时间
                log.info("msg wait {}", channelQaApolloConfig.getMsgWaitMilliSecond());
                Thread.sleep(channelQaApolloConfig.getMsgWaitMilliSecond());
            } catch (InterruptedException e) {
                log.error("sleep", e);
            }
        }

        // 整个会话粒度的内容上下文信息
        // 保存全局的一些信息
        BotContext botContext = getBotContext(channelQaReq);
        log.info("botContext {}", IoUtils.toJsonString(botContext));

        // 文本预处理
        TextBasicData basicData = textPreprocessService.preprocess(channelQaReq.getMessageContent().getText());
        botContext.setCurInput(basicData);

        List<ChannelQaRslt> channelQaRslts = new ArrayList<>();

        // 默认是FAQ
        IntentTypeV2 intentTypeV2 = IntentTypeV2.FAQ;
        List<CoreIntent> coreIntents = new ArrayList<>();

        // 组合knowledgeId,answerId,itemId,cateId的id
        Set<String> knowledgeRespIds = new HashSet<>();
        if (envService.isTest()) {
            channelQaRslts.addAll(testMockManager.mockMultiChannelPipelineLog(channelQaReq));
            if (CollectionUtils.isEmpty(channelQaRslts)) {
                KnowledgeMatchResp knowledgeMatchResp = cardInfoFlag ? new KnowledgeMatchResp() : getKnowledgeMatchResp(botContext, channelQaRslts, knowledgeRespIds);
                log.info("KnowledgeMatchResp {} {}", basicData.getCleanedText(), IoUtils.toJsonString(knowledgeMatchResp));
            }
            // 商品属性处理
            if (botContext.getItemId() != 0 && channelQaApolloConfig.getItemAttributeFlag() == 1 && !StringUtils.equals("URL", basicData.getCleanedText()) && channelQaReq.getMessageType() != MessageType.GOOD_CARD.getCode()) {
                // 商品属性
                List<ChannelQaRslt> attrs = itemAttributeService.similarityForAttr(basicData.getCleanedText(), botContext.getItemId(), channelQaApolloConfig.getSimilarityThreshold());
                if (ObjectUtils.isNotEmpty(attrs)) {
                    channelQaRslts.addAll(attrs);
                }
            }
        } else {
            KnowledgeMatchResp knowledgeMatchResp = cardInfoFlag ? new KnowledgeMatchResp() : getKnowledgeMatchResp(botContext, channelQaRslts, knowledgeRespIds);

            // 核心意图分类, 业务分类
            coreIntents = cardInfoFlag ? new ArrayList<>() : intentProductService.getCoreIntentByVersion(basicData.getCleanedText());

            // 粗意图分类
            IntentRslt intentRslt = cardInfoFlag ? new IntentRslt(Level1IntentType.KBQA) : intentServiceV2.getIntentV2(basicData.getCleanedText());
            intentTypeV2 = transfer(channelQaAddiReq, intentRslt);
            log.info("coreIntents {} {}", basicData.getCleanedText(), IoUtils.toJsonString(coreIntents));
            log.info("intentRslt {} {}", basicData.getCleanedText(), IoUtils.toJsonString(intentRslt));
            log.info("KnowledgeMatchResp {} {}", basicData.getCleanedText(), IoUtils.toJsonString(knowledgeMatchResp));

            // 从意图里处理知识
            intent2Knowledge(channelQaReq, basicData, channelQaRslts, knowledgeRespIds, coreIntents);

            // 商品属性处理
            if (botContext.getItemId() != 0 && channelQaApolloConfig.getItemAttributeFlag() == 1 && !cardInfoFlag && channelQaReq.getMessageType() != MessageType.GOOD_CARD.getCode()) {
                // 商品属性
                List<ChannelQaRslt> attrs = itemAttributeService.similarityForAttr(basicData.getCleanedText(), botContext.getItemId(), channelQaApolloConfig.getSimilarityThreshold());
                if (ObjectUtils.isNotEmpty(attrs)) {
                    channelQaRslts.addAll(attrs);
                }
            }

            // 商品卡片直接出特定知识
            if (cardInfoFlag && botContext.getItemId() != 0) {
                long knowledgeId = channelQaReq.getMessageType() == MessageType.GOOD_CARD.getCode() ? channelQaApolloConfig.getGoodsRespKnowledgeId() : channelQaApolloConfig.getOrderGoodsRespKnowledgeId();
                List<Long> answerIds = recallServiceWithEs8.getAnswerIdByKnowledgeId(knowledgeId, channelQaReq.getChannelId(), channelQaReq.getPlatform(), botContext.getItemId());
                if (CollectionUtils.isNotEmpty(answerIds)) {
                    channelQaRslts.add(new ChannelQaRslt(knowledgeId, "", "", answerIds.get(0), botContext.getItemId(), 0, 1.0));
                }
            }
        }

        log.info("before postProcess {}", IoUtils.toJsonString(channelQaRslts));

        List<ChannelQaRslt> channelQaRsltAfterPostProcess = postProcess(channelQaReq, botContext, intentTypeV2, channelQaRslts);
        log.info("after postProcess {}", IoUtils.toJsonString(channelQaRsltAfterPostProcess));

        ChannelQaResp channelQaResp = new ChannelQaResp(channelQaRsltAfterPostProcess, intentTypeV2, channelQaAddiReq);
        channelQaResp.setCoreIntents(coreIntents);

        MultiChannelPipelineLog multiChannelPipelineLog = new MultiChannelPipelineLog();
        multiChannelPipelineLog.setContext(botContext);
        multiChannelPipelineLog.setInput(channelQaReq);
        multiChannelPipelineLog.setChannelQaAddiReq(channelQaAddiReq);
        multiChannelPipelineLog.setOutput(channelQaResp);
        multiChannelPipelineLog.setOther(new HashMap<>());

        return multiChannelPipelineLog;
    }

    // 特定业务逻辑过滤
    private boolean filter(ChannelQaRslt channelQaRslt, ChannelQaReq channelQaReq) {
        // 机器人交流中，答案有http并且数据是来自属性的的，三种必须同时满足
        if (channelQaReq.getSessionInteraction() == 1 && StringUtils.contains(channelQaRslt.getShowAnswer(), "http") && channelQaRslt.getKnowledgeSource() == ChannelQaRslt.KNOWLEDGE_SOURCE_GOODS_ATTRIBUTE) {
            return false;
        }
        return true;
    }

    private void intent2Knowledge(ChannelQaReq channelQaReq, TextBasicData basicData, List<ChannelQaRslt> channelQaRslts, Set<String> knowledgeRespIds, List<CoreIntent> coreIntents) {
        // 解析
        if (CollectionUtils.isNotEmpty(coreIntents)) {
            for (CoreIntent coreIntent : coreIntents) {
                if (coreIntent.getProb() <= channelQaApolloConfig.getIntentThreshold()) {
                    log.info("ignore intent {} {}", coreIntent, basicData.getCleanedText());
                    continue;
                }
                String intentEn = coreIntent.getIntent();
                Map<String, Map<String, Long>> intentKnowledgeMap = channelQaApolloConfig.getIntentCoreKnowledgeMap();
                // TODO 统一口径为后端的platform
                // 如果sessionInteraction为1，就额外加一个 "-1"
                String platform = ChannelType.getByName(channelQaReq.getPlatform()).toString();
                if (channelQaReq.getSessionInteraction() == 1) {
                    platform = platform + "-1";
                }

                long intentKnowledgeId = intentKnowledgeMap.getOrDefault(platform, new HashMap<>()).getOrDefault(intentEn, 0L);
                log.info("extractIntent {} {} {}", platform, intentEn, intentKnowledgeId);

                if (intentKnowledgeId != 0) {
                    log.info("hit intent");
                    // 换成中文
                    if (MapUtils.isNotEmpty(channelQaApolloConfig.getId2QuestionMap()) && channelQaApolloConfig.getId2QuestionMap().containsKey(intentKnowledgeId)) {
                        intentEn = channelQaApolloConfig.getId2QuestionMap().get(intentKnowledgeId);
                    }
                    // 尝试找知识id的通用答案
                    List<Long> answerIds = recallServiceWithEs8.getAnswerIdByKnowledgeId(intentKnowledgeId, channelQaReq.getChannelId(), channelQaReq.getPlatform(), 0);
                    if (CollectionUtils.size(answerIds) == 0) {
                        log.warn("extractIntent answer size = 0: {} {} {} {}", intentKnowledgeId, channelQaReq.getChannelId(), channelQaReq.getPlatform(), intentEn);
                    }
                    if (CollectionUtils.size(answerIds) > 1) {
                        log.warn("extractIntent answer size > 1: {} {} {} {}", intentKnowledgeId, channelQaReq.getChannelId(), channelQaReq.getPlatform(), intentEn);
                    }
                    if (CollectionUtils.size(answerIds) >= 1) {
                        log.info("extractIntent {}", answerIds);
                        ChannelQaRslt channelQaRslt = new ChannelQaRslt(intentKnowledgeId, intentEn, "STANDARD-" + intentKnowledgeId, answerIds.get(0), 0, 0, 0.85);

                        if (!knowledgeRespIds.contains(channelQaRslt.buildId())) {
                            channelQaRslts.add(0, channelQaRslt);
                            knowledgeRespIds.add(channelQaRslt.buildId());
                        }
                    }
                }
            }
        }
    }

    private KnowledgeMatchResp getKnowledgeMatchResp(BotContext botContext, List<ChannelQaRslt> channelQaRslts, Set<String> knowledgeRespIds) {
        // 检索 FAQ
        KnowledgeMatchResp knowledgeMatchResp = faqMatchPipelineService.processWithSimpleModel(botContext);
        if (CollectionUtils.isNotEmpty(knowledgeMatchResp.getMatchRslts())) {
            for (KnowledgeMatchRslt knowledgeMatchRslt : knowledgeMatchResp.getMatchRslts()) {
                ChannelQaRslt channelQaRslt = new ChannelQaRslt(knowledgeMatchRslt.getKnowledgeId(), knowledgeMatchRslt.getQuestion(), knowledgeMatchRslt.getCorpusId(), knowledgeMatchRslt.getAnswerId(), knowledgeMatchRslt.getItemId(), knowledgeMatchRslt.getCateId(), knowledgeMatchRslt.getScore());
                // 商品类的需要更严格
                if (channelQaRslt.getItemId() != 0 && channelQaRslt.getScore() < channelQaApolloConfig.getItemFaqThreshold()) {
                    continue;
                }
                if (channelQaRslt.getScore() < channelQaApolloConfig.getFaqThreshold()) {
                    continue;
                }
                if (!knowledgeRespIds.contains(channelQaRslt.buildId())) {
                    channelQaRslts.add(channelQaRslt);
                    knowledgeRespIds.add(channelQaRslt.buildId());
                }
            }
        }
        return knowledgeMatchResp;
    }

    // 如果有多个一模一样的，需要取最新的
    // 客服编辑的商品属性 > 普通商品属性 > FAQ
    private List<ChannelQaRslt> postProcess(ChannelQaReq channelQaReq, BotContext botContext, IntentTypeV2 intentTypeV2, List<ChannelQaRslt> channelQaRslts) {
        // 如果是机器人的闲聊相关，后处理过滤掉
        if (botContext.getSessionInteraction() == 1) {
            ChatType chatType = chatService.getChitChatRes(true, botContext.getInputs().get(0).getCleanedText());
            if (chatType.isChat()) {
                return new ArrayList<>();
            }

            // 如果是导购，则也过滤掉
            if (IntentTypeV2.GUIDE.equals(intentTypeV2)) {
                return new ArrayList<>();
            }
        }

        // 业务规则过滤
        channelQaRslts = channelQaRslts.stream().map(x -> addTrainContext(x, botContext)).filter(x -> filter(x, channelQaReq)).sorted().limit(channelQaReq.getLimit()).collect(Collectors.toList());

        for (ChannelQaRslt channelQaRslt : channelQaRslts) {
            // 配置需要
            if (channelQaRslt.getScore() > 1 && envService.isTest()) {
                log.info("hit test");
                return channelQaRslts;
            }
        }

        // 如果有item_id
        if (botContext.getItemId() != 0) {
            for (ChannelQaRslt channelQaRslt : channelQaRslts) {
                // 遍历一遍所有的FAQ中的answer_id，如果itemInfoLabel 指明了item_id,则直接返回，没有则进行下一步
                if (channelQaRslt.getKnowledgeSource() == 1 && channelQaRslt.getScore() >= channelQaApolloConfig.getExactThreshold()) {
                    // 访问es
                    boolean isItemAnswerInFaq = recallServiceWithEs8.hasItemAnswerInFaq(channelQaRslt.getAnswerId(), channelQaRslt.getItemId(), botContext);
                    if (isItemAnswerInFaq) {
                        return Collections.singletonList(channelQaRslt);
                    }
                }
            }
            // 如果是没有业务配的FAQ，并且有商品属性，则输出商品属性
            for (ChannelQaRslt channelQaRslt : channelQaRslts) {
                if (channelQaRslt.getKnowledgeType() == 1 && channelQaRslt.getScore() >= channelQaApolloConfig.getExactThreshold()) {
                    return Collections.singletonList(channelQaRslt);
                }
            }
        }
        for (ChannelQaRslt channelQaRslt : channelQaRslts) {
            // 如果业务没配、也没有商品属性，则直接按照排序输出
            if (channelQaRslt.getScore() >= channelQaApolloConfig.getExactThreshold()) {
                return Collections.singletonList(channelQaRslt);
            }
        }
        return channelQaRslts;
    }

    private ChannelQaRslt addTrainContext(ChannelQaRslt channelQaRslt, BotContext botContext) {
        // 是有商品绑定的知识
        if (channelQaRslt.getItemId() != 0 && botContext != null) {
            if (StringUtils.isNotEmpty(botContext.getPlatformRawItemCardId())) {
                channelQaRslt.setPlatformRawItemCardId(botContext.getPlatformRawItemCardId());
            }
            if (StringUtils.isNotEmpty(botContext.getItemPhyCategoryIdStr())) {
                channelQaRslt.setItemPhyCategoryIdStr(botContext.getItemPhyCategoryIdStr());
            }
            if (StringUtils.isNotEmpty(botContext.getPlatformItemId())) {
                channelQaRslt.setPlatformItemId(botContext.getPlatformItemId());
            }
        }
        return channelQaRslt;
    }

    private BotContext getBotContext(ChannelQaReq channelQaReq) {
        BotContext botContext = new BotContext();
        try {
            // sessionId构成的key
            String key = BotContext.buildContextKey(channelQaReq.getSessionId());
            log.info("key {} ", key);
            String value = redisService.get(key);
            // 如果之前有完整的context，则取出来
            if (StringUtils.isNotEmpty(value)) {
                log.info("load context {}", value);
                botContext = IoUtils.parseJson(value, BotContext.class);
            }
            if (botContext == null) {
                botContext = new BotContext();
            }
            botContext.setChannel(channelQaReq.getChannelId());
            botContext.setSessionInteraction(channelQaReq.getSessionInteraction());

            if (StringUtils.isEmpty(botContext.getPlatform())) {
                botContext.setPlatform(channelQaReq.getPlatform());
            }
            if (StringUtils.isEmpty(botContext.getMultiChannelSessionId())) {
                botContext.setMultiChannelSessionId(channelQaReq.getSessionId());
            }

            botContext.setConsultTime(channelQaReq.getConsultTime());
            botContext.setTestMode(channelQaReq.isTestMode());
        } catch (Exception e) {
            log.error("processAddi", e);
            botContext = new BotContext();
        }
        return botContext;
    }

    // 处理一些特殊的消息
    private ChannelQaAddiReq extractFromMessage(ChannelQaReq channelQaReq) {
        if (envService.isTest()) {
            return testMockManager.mockChannelQaAddiReq(channelQaReq);
        }

        ChannelQaAddiReq channelQaAddiReq = new ChannelQaAddiReq();

        MessageContent messageContent = channelQaReq.getMessageContent();
        if (ObjectUtils.isNotEmpty(messageContent)) {
            if (CollectionUtils.isNotEmpty(messageContent.getItemList())) {
                // 要么是 itemId，要么是skuId
                String platformCardId = messageContent.getItemList().get(0).getItemId();
                if (StringUtils.isEmpty(platformCardId)) {
                    platformCardId = messageContent.getItemList().get(0).getSkuId();
                }
                if (StringUtils.isNotEmpty(platformCardId)) {
                    SearchResp searchResp = channelSearchManager.process(new SearchReq(channelQaReq.getChannelId(), platformCardId, 5), false);
                    if (CollectionUtils.isNotEmpty(searchResp.getItemInfos())) {
                        long itemId = searchResp.getItemInfos().get(0).getItemId();
                        String itemPhyCategoryIdStr = itemService.getJoinPhyCateId(itemId);
                        channelQaAddiReq = new ChannelQaAddiReq(channelQaReq.getChannelId(), channelQaReq.getSessionId(), channelQaReq.getServiceId(), itemId, null,
                                searchResp.getItemInfos().stream().map(SearchItemInfo::getItemId).collect(Collectors.toList()), platformCardId, searchResp.getItemInfos().get(0).getOutItemId(), itemPhyCategoryIdStr);
                    }
                }
            }
        }

        if (StringUtils.isEmpty(channelQaAddiReq.getPlatformRawItemCardId())) {
            try {
                String msg = channelQaReq.getMessageContent().getText();
                // 遍历检测一遍是否有可能使用的额外信息
                if (CollectionUtils.isNotEmpty(this.itemIdPattens) && StringUtils.isNotEmpty(msg)) {
                    for (Pattern pattern : this.itemIdPattens) {
                        Matcher matcher = pattern.matcher(msg);
                        if (matcher.find()) {
                            String platformCardId = matcher.group(1);
                            SearchResp searchResp = channelSearchManager.process(new SearchReq(channelQaReq.getChannelId(), platformCardId, 5), false);
                            if (CollectionUtils.isNotEmpty(searchResp.getItemInfos())) {
                                long itemId = searchResp.getItemInfos().get(0).getItemId();
                                String itemPhyCategoryIdStr = itemService.getJoinPhyCateId(itemId);
                                channelQaAddiReq = new ChannelQaAddiReq(channelQaReq.getChannelId(), channelQaReq.getSessionId(), channelQaReq.getServiceId(), itemId, null,
                                        searchResp.getItemInfos().stream().map(SearchItemInfo::getItemId).collect(Collectors.toList()), platformCardId, searchResp.getItemInfos().get(0).getOutItemId(), itemPhyCategoryIdStr);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("extract", e);
            }
        }

        return channelQaAddiReq;
    }
}
