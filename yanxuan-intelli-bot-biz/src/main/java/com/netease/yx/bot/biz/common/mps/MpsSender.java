package com.netease.yx.bot.biz.common.mps;

import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.MD5Utils;
import com.netease.yx.bot.common.util.web.BaseResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class MpsSender {
    private static final String VALUE_PRODUCT = "yanxuan-intelli-bot";
    private static final String KEY_PRODUCT = "product";
    private static final String KEY_MODULE = "module";
    private static final String KEY_SUBJECT = "subject";
    private static final String KEY_TOPIC = "topic";
    private static final String KEY_MESSAGE_ID = "messageId";
    private static final String KEY_PAYLOAD = "payload";

    private static final String URL = "/xhr/message/%s/send.json";

    @Value("${remote.service.mps.host}")
    private String remoteServiceMpsHost;

    @Autowired
    private EnvService envService;

    public static void main(String[] args) {
        System.out.println(String.format(URL, "abc"));
    }

    public boolean sendMps(String topic, String payload) {
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        // 发布方cmdb
        nameValuePairs.add(new BasicNameValuePair(KEY_PRODUCT, VALUE_PRODUCT));
        // 随便定义
        nameValuePairs.add(new BasicNameValuePair(KEY_MODULE, KEY_MODULE));
        // 随便定义
        nameValuePairs.add(new BasicNameValuePair(KEY_SUBJECT, KEY_SUBJECT));

        nameValuePairs.add(new BasicNameValuePair(KEY_TOPIC, topic));

        nameValuePairs.add(new BasicNameValuePair(KEY_MESSAGE_ID, System.currentTimeMillis() + MD5Utils.md5(payload)));
        nameValuePairs.add(new BasicNameValuePair(KEY_PAYLOAD, payload));

        try {
            String url = remoteServiceMpsHost + String.format(URL, VALUE_PRODUCT);
            log.info("sendMps {} {} {} {}", url, topic, IoUtils.toJsonString(nameValuePairs), envService.getEnv());

            if (!envService.isRelease()) {
                String resp = HttpUtils.executePost(url, nameValuePairs);
                log.info("sendMps {}", resp);

                BaseResp baseResp = IoUtils.parseJson(resp, BaseResp.class);
                if (baseResp.getCode() == HttpStatus.SC_OK) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("sendMps", e);
        }
        return false;
    }
}
