/**
 * @(#)UserManager.java, 2020/10/9.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.mail.dp.dqs.client.DataQueryClient;
import com.netease.mail.dp.dqs.client.model.DataQueryConfigure;
import com.netease.mail.dp.dqs.model.condition.DataQueryCondition;
import com.netease.mail.dp.dqs.model.filter.SingleDataFilter;
import com.netease.mail.dp.dqs.model.filter.condition.Select;
import com.netease.mail.dp.dqs.model.order.OrderBy;
import com.netease.mail.dp.dqs.model.result.Response;
import com.netease.mail.dp.dqs.model.result.result.DataQueryResult;
import com.netease.mail.yanxuan.supermc.rpc.enums.MemberStatusEnum;
import com.netease.mail.yanxuan.supermc.rpc.enums.RpcResponseCode;
import com.netease.mail.yanxuan.supermc.rpc.meta.common.RpcCommonResult;
import com.netease.mail.yanxuan.supermc.rpc.meta.to.FreeItemsAndTryRecordTO;
import com.netease.mail.yanxuan.supermc.rpc.meta.to.FreeItemsTO;
import com.netease.mail.yanxuan.supermc.rpc.meta.to.MemberTO;
import com.netease.mail.yanxuan.supermc.rpc.service.RpcFreeTryService;
import com.netease.mail.yanxuan.supermc.rpc.service.RpcMemberService;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.UserOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.rmi.ServerException;
import java.sql.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> @ corp.netease.com)
 * 读取用户的订单，是否是新用户等
 */
@Component
@Slf4j
@Service
@EnableScheduling
public class UserService {
    private final static String GET_USER_PRO_ZERO_ACTIVITY_SQL = "select userid from data_mining.yanxuan_user_pro_zero_activity";

    private static final String PARAM_KEY_PRODUCT = "product";
    private static final String PARAM_VALUE_PRODUCT = "rcmd";
    private static final String PARAM_KEY_USERID = "userId";
    private static final String RESP_KEY_RESULT = "result";
    private static final Integer USER_FREE_ORDER = 1;

    private Set<Long> userProZeroActivityList;

    @Value("${user.new.flag.url}")
    private String userNewFlagUrl;
    @Value("${dqs.url}")
    private String dqsUrl;
    @Value("${dqs.model.user.order.appKey}")
    private String dqsModelUserOrderAppKey;
    @Value("${dqs.model.user.order.appSecret}")
    private String dqsModelUserOrderAppSecret;
    @Value("${dqs.model.user.order.name}")
    private String dqsModelUserOrderName;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RpcMemberService rpcMemberService;
    @Autowired
    private RpcFreeTryService rpcFreeTryService;

    private DataQueryClient dataQueryClient;

    @Value("${waiterdb.jdbc.url}")
    private String waiterdbJdbcUrl;

    @Autowired
    private EnvService envService;

    @PostConstruct
    private void init() {
        DataQueryConfigure configure = new DataQueryConfigure();
        configure.setServerUrl(dqsUrl)
                .setAppKey(dqsModelUserOrderAppKey)
                .setAppSecret(dqsModelUserOrderAppSecret)
                .setThreads(20)
                .setTimeout(1000);
        dataQueryClient = DataQueryClient.getInstance(configure);
        update();
    }

    @Scheduled(cron = "0 0 9 * * *")
    private void update() {
        log.info("start getUserInfo from hive.");
        Set<Long> userProZeroActivity = new HashSet<>();

        if (!envService.isTest()) {
            try {
                Class.forName("com.netease.mail.holmes.waiter.jdbc.WaiterDriver");
                Connection conn = DriverManager.getConnection(waiterdbJdbcUrl);

                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(GET_USER_PRO_ZERO_ACTIVITY_SQL);

                while (rs.next()) {
                    Long proZeroActivityUserId = rs.getLong("userid");

                    userProZeroActivity.add(proZeroActivityUserId);
                }

                log.info("load getProZeroActivity {} {}", "26220394605", userProZeroActivity.contains(26220394605L));
            } catch (ClassNotFoundException | SQLException e) {
                log.error("getUserInfo", e);
            }

        }

        userProZeroActivityList = userProZeroActivity;
    }

    /**
     * curl -s "http://127.0.0.1:8550/proxy/online.yanxuan-user.service.mailsaas/userNewOldFlagV2/isNewUserByUserId?product='rcmd'&userId=123456"
     *
     * @param userId
     * @return
     */
    public boolean isNewUser(Long userId) {
        if (userId == 0) {
            return false;
        }

        List<NameValuePair> pairs = new ArrayList<>();

        pairs.add(new BasicNameValuePair(PARAM_KEY_PRODUCT, PARAM_VALUE_PRODUCT));
        pairs.add(new BasicNameValuePair(PARAM_KEY_USERID, userId.toString()));

        boolean result = false;

        try {
            String respStr = HttpUtils.executeGet(userNewFlagUrl, pairs);
            log.info("isNewUser {} {}", userId, respStr);
            result = IoUtils.parseJson(respStr).get(RESP_KEY_RESULT).asBoolean();
        } catch (Exception e) {
            log.error("UserManager", e);
        }

        return result;
    }

    public UserOrder getCenterUserOrder(List<UserOrder> userOrders) {
        // 如果orderId 不为0，则会查询该orderId，结果为0-1个
        if (!CollectionUtils.isEmpty(userOrders)) {
            // 规则1：命中携带明确订单
            // 规则2：命中15天内只有一个订单
            if (userOrders.size() == 1) {
                return userOrders.get(0);
            } else {
                // 从近到远排序
                Collections.sort(userOrders);

                // 规则3：申请单，退货、换货、退款、维修、价保
                UserOrder applyOrder = checkApply(userOrders);
                if (applyOrder != null) {
                    log.info("hit applyOrder {}", applyOrder);
                    return applyOrder;
                }
                // 规则5：工单未完结
                UserOrder ticketOrder = checkTicket(userOrders);
                if (ticketOrder != null) {
                    log.info("hit ticketOrder {}", ticketOrder);

                    return ticketOrder;
                }
                // 规则6：物流未完结
                UserOrder deliveryOrder = checkDelivery(userOrders);
                if (deliveryOrder != null) {
                    log.info("hit deliveryOrder {}", deliveryOrder);
                    return deliveryOrder;
                }
                // 默认取第一个
                return userOrders.get(0);
            }
        }
        return null;
    }

    public UserOrder checkDelivery(List<UserOrder> userOrders) {
        for (UserOrder userOrder : userOrders) {
            long trackingStatus = userOrder.getTrackingStatus();
            // 目前简单实现，待发货，已发货
            if (trackingStatus >= 7 && trackingStatus <= 8) {
                return userOrder;
            }
        }
        return null;
    }

    public UserOrder checkTicket(List<UserOrder> userOrders) {
        for (UserOrder userOrder : userOrders) {
            long ticketStatus = userOrder.getTicketStatus();
            // 目前简单实现，工单异常
            if (ticketStatus >= 1 && ticketStatus <= 100) {
                return userOrder;
            }
        }
        return null;
    }

    public UserOrder checkApply(List<UserOrder> userOrders) {
        for (UserOrder userOrder : userOrders) {
            long applyTypeCode = userOrder.getApplyTypeCode();
            long applyStatus = userOrder.getApplyStatus();
            // 目前简单实现，有申请单且状态都是异常状态
            if (applyTypeCode != 0 && applyStatus >= 5 && applyStatus <= 11 && applyStatus != 6) {
                return userOrder;
            }
        }
        return null;
    }

    public List<UserOrder> queryOrderByUserId(long userId, long orderId) {

        List<UserOrder> userOrders = new ArrayList<>();
        try {
            String userOrderContextKey = BotContext.buildUserOrderContextKey(userId);
            if (stringRedisTemplate.hasKey(userOrderContextKey)) {
                log.info("get cache {}", userId);
                String value = stringRedisTemplate.opsForValue().get(userOrderContextKey);
                if (value != null) {
                    userOrders = IoUtils.parseJson(value, new TypeReference<List<UserOrder>>() {
                    });
                    log.info("cache queryOrderByUserId result {} {} ", userId, userOrders.get(0));
                }
            } else {
                // 查询指标
                DataQueryCondition condition = buildCondition(userId, orderId);
                Response<DataQueryResult> response = dataQueryClient.query(condition);
                for (List<Object> data : response.getResult().getDatas()) {
                    UserOrder userOrder = UserOrder.buildUserOrderFromDqsObj(data);
                    if (userOrder != null) {
                        userOrders.add(userOrder);
                    }
                }
                log.info("queryOrderByUserId result {} {} ", userId, userOrders.size());

                if (!userOrders.isEmpty()) {
                    Collections.sort(userOrders);
                    // 加入缓存
                    stringRedisTemplate.opsForValue().set(userOrderContextKey, IoUtils.toJsonString(userOrders), 30, TimeUnit.MINUTES);
                    log.info("queryOrderByUserId result {} {} ", userId, userOrders.get(0));
                }
            }
        } catch (Exception e) {
            log.error("queryOrderByUserId", e);
        }
        return userOrders;
    }

    private DataQueryCondition buildCondition(long userId, long orderId) {
        DataQueryCondition condition = new DataQueryCondition();

        SingleDataFilter userIdFilter = new SingleDataFilter("user_id",
                new Select(String.valueOf(userId)));

        OrderBy orderBy = new OrderBy().setField("pay_time").setAsc(false);
        condition.setModelName(dqsModelUserOrderName)
                .addFields(
                        "order_id", "order_status_desc", "create_time", "pay_time", "user_id", "spmc_status", "user_credit_level", "item_id", "item_name", "sku_id", "count", "real_price_amount", "storehouse_id", "storehouse_name", "outstore_no", "package_id", "receiver_name", "receiver_mobile", "receiver_province", "receiver_city", "receiver_district", "outstore_status", "carrier", "tracking_status", "tracking_num", "confirm_time", "apply_type_code", "apply_status", "apply_id", "global_id", "ticket_content_title", "ticket_desc_plain_text", "ticket_status"
                )
                .addDataFilter(userIdFilter)
                .setOrderBy(Collections.singletonList(orderBy))
                .setLimit(30L);

        if (orderId != 0) {
            SingleDataFilter orderIdFilter = new SingleDataFilter("order_id",
                    new Select(String.valueOf(orderId)));
            condition.addDataFilter(orderIdFilter);
        }
        log.info("buildCondition {} {}", userId, IoUtils.toJsonString(condition));

        return condition;
    }

    public boolean checkProZeroActivity(Long userId) {
        return userProZeroActivityList.contains(userId);
    }

    /**
     * 检查是否为超会会员
     */
    public boolean checkUserPro(long userId) {
        MemberTO memberTO = getMemberInfo(userId);
        if (memberTO != null) {
            MemberStatusEnum memberStatus = MemberStatusEnum.genEnumByValue(memberTO.getStatus());
            log.info("checkUserPro {}", memberStatus);
            switch (memberStatus) {
                case BOUGHT_IN_EFFECT:
                case IN_TRY:
                case FRIEND_CARD_IN_EFFECT:
                    return true;
            }
        }
        return false;
    }

    /**
     * 从rpc主站接口获取会员信息
     */
    private MemberTO getMemberInfo(long userId) {
        try {
            RpcCommonResult<MemberTO> rpcCommonResult = rpcMemberService.getMemberInfo(userId);
            return checkAndObtainResult(rpcCommonResult, "getMemberInfo");
        } catch (Exception e) {
            log.error("getMemberInfo error.", e);
        }

        return null;
    }

    private <T> T checkAndObtainResult(RpcCommonResult<T> response, String method) throws ServerException {
        if (RpcResponseCode.OK.getCode().equals(response.getCode())) {
            return response.getResult();
        } else {
            log.warn("[op:{}] response is not ok. msg = {} ", method, response.getMsg());
            throw new ServerException(response.getMsg());
        }
    }


    public FreeItemsTO getFreeItemActIds() {
        try {
            RpcCommonResult<FreeItemsTO> curFreeItems = rpcFreeTryService.getCurrentFreeItems();
            return checkAndObtainResult(curFreeItems, "getCurrentFreeItems");
        } catch (Exception e) {
            log.error("getFreeItemActIds error.", e);
        }
        return null;
    }

    private FreeItemsAndTryRecordTO getFreeTryRecords(Long actId, Long userId) {
        try {
            RpcCommonResult<FreeItemsAndTryRecordTO> freeTryRecords = rpcFreeTryService.getNormalFreeItemsAndTryRecordByUserIdAndActId(userId, actId);
            return checkAndObtainResult(freeTryRecords, "freeTryRecords");
        } catch (Exception e) {
            log.error("getFreeTryRecords error.", e);
        }
        return null;
    }

    public FreeItemsTO getFreeTryRecords(Long userId) {
        try {
            RpcCommonResult<FreeItemsTO> freeTryRecords = rpcFreeTryService.getCurrentFreeItemsByUserId(userId);
            return checkAndObtainResult(freeTryRecords, "getCurrentFreeItems");
        } catch (Exception e) {
            log.error("getFreeTryRecords error.", e);
        }
        return null;
    }

    /**
     * 从rpc主站接口获取会员0元购领取页面的商品
     */
    public Long getFreeItemIds(Long actId, Long userId) {
        FreeItemsAndTryRecordTO freeItemsAndTryRecordTO = getFreeTryRecords(actId, userId);
        log.debug("getProZeroActivity item: {}", IoUtils.toJsonString(freeItemsAndTryRecordTO));

        if (freeItemsAndTryRecordTO == null) {
            log.info("getFreeItemIds: actId {} item list is empty.", actId);
            return null;
        }

        // 判断是否已领取  空的情况为未领取
        if (freeItemsAndTryRecordTO.getFreeTryRecordTO() == null) {
            if (CollectionUtils.isEmpty(freeItemsAndTryRecordTO.getFreeItemsTO().getItemTOS())) {
                log.info("getFreeItemIds: free record items list is empty.");
                return null;
            }

            return freeItemsAndTryRecordTO.getFreeItemsTO().getItemTOS().get(0).getItemId();
        }

        Integer freeStatus = freeItemsAndTryRecordTO.getFreeTryRecordTO().getStatus();

        // 已下单为已领取
        if (USER_FREE_ORDER.equals(freeStatus)) {
            log.info("getFreeItemIds: user {} has free order.", userId);
            return null;
        }

        if (CollectionUtils.isEmpty(freeItemsAndTryRecordTO.getFreeItemsTO().getItemTOS())) {
            log.info("getFreeItemIds: free record items list is empty.");
            return null;
        }


        return freeItemsAndTryRecordTO.getFreeItemsTO().getItemTOS().get(0).getItemId();
    }

}