package com.netease.yx.bot.biz.service.mps;

import com.netease.yx.bot.core.model.entity.channel.ChannelQaRslt;
import lombok.Data;

import java.util.List;

@Data
public class TrainChange {
    private String taskId;
    // 1：待标注训练任务；2：标注训练任务；3：忽略训练任务4：推荐结果失效
    private int situation;

    // 平台
    private String platform;

    // 主站商品id
    private long itemId;

    // 生效的消息
    private List<String> messageContents;

    // 指向的结果，这里只有一个
    private ChannelQaRslt channelQaRslt;
}
