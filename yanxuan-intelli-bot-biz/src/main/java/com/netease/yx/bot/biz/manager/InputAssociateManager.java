package com.netease.yx.bot.biz.manager;

import com.netease.yx.bot.biz.common.DictService;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.service.TextPreprocessService;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.biz.applloconfig.InputAssocisateConfig;
import com.netease.yx.bot.common.util.EsUtil;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.AlgoInfo;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.inputAssociate.InputAssociateReq;
import com.netease.yx.bot.core.model.entity.inputAssociate.InputAssociateResp;
import com.netease.yx.bot.core.model.entity.item.Item;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * APP-机器人的编辑框输入联想
 * 目前不维护了
 */
@Service
@Slf4j
public class InputAssociateManager {
    private static final String KEY_CLEAN_QUESTION = "CleanQuestion";
    private static final String KEY_TERMS = "Terms";
    private static final String KEY_STATUS = "Status";
    private static final String KEY_ITEM_ID = "ItemId";
    private static final String KEY_CATE_ID = "CateId";
    private static final String KEY_KNOWLEDGE_ID = "KnowledgeId";
    private static final int MAX_LENGTH = 20;

    @Value("${recall.text.url}")
    private String textUrl;
    @Value("${recall.text.index.appName}")
    private String appName;
    @Value("${recall.text.index.indexName}")
    private String indexName;
    @Value("${recall.text.index.max}")
    private int max;

    @Autowired
    private BotApolloConfig botApolloConfig;
    @Autowired
    private InputAssocisateConfig inputAssocisateConfig;

    @Autowired
    private PipelineManager pipelineManager;

    @Autowired
    private TextPreprocessService preprocessManager;

    @Autowired
    private DictService dictService;

    @Autowired
    private EnvService envService;

    private TestMockManager testMockManager;

    public InputAssociateResp process(InputAssociateReq req) {
        String input = preprocessManager.clean(req.getQuery());
        List<String> result = new ArrayList<>();
        if (StringUtils.isEmpty(input)) {
            return new InputAssociateResp(result);
        }
        if (envService.isTest()) {
            return testMockManager.mockInputAssociate(req);
        }
        if (input.contains("人工") || input.contains("rg") || input.contains("客服")) {
            return new InputAssociateResp(result);
        }
        // 从redis中取出上下文, 主要是去获取itemId相关的，确定搜索范围
        BotContext botContext = pipelineManager.getContextFromCache(req.getSessionId());
        // 构建搜索请求，搜索
        List<String> searchResult = search(botContext, input);
        if (!CollectionUtils.isEmpty(searchResult)) {
            result.addAll(searchResult);
        }
        return new InputAssociateResp(result);
    }

    private QueryBuilder queryBuilder(BotContext botContext, String input) {
        TextBasicData textBasicData = preprocessManager.preprocess(input, false);
        // 必须要精确匹配的字段
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        // 实时在线的知识
        queryBuilder.must(QueryBuilders.matchQuery(KEY_STATUS, AlgoInfo.STATUS_VALID));

        // 判断知识范围
        // 如果有一系列商品卡片，则会取下最近的一个卡片，否则会去取商详页进来带的itemId
        long itemId = 0;
        if (!CollectionUtils.isEmpty(botContext.getCardItemIds())) {
            itemId = botContext.getCardItemIds().get(0);
        } else if (botContext.getItemId() != 0) {
            itemId = botContext.getItemId();
        }

        // 如果有一系列商品卡片，则会取下最近的一个卡片，否则会去取商详页进来带的item
        Item item = null;
        if (!CollectionUtils.isEmpty(botContext.getCardItems())) {
            item = botContext.getCardItems().get(0);
        } else if (botContext.getItem() != null) {
            item = botContext.getItem();
        }

        // 知识范围
        BoolQueryBuilder knowledgeScope = QueryBuilders.boolQuery();

        // 如果是单个商品，则会匹配 商品知识和类目
        if (itemId != 0) {
            knowledgeScope.should(QueryBuilders.termQuery(KEY_ITEM_ID, itemId));
            // 目前控制是2,3,4级物理类目
            // or关系
            if (item != null) {
                String cate2Id = String.valueOf(item.getPhyCategory2Id());
                knowledgeScope.should(QueryBuilders.matchQuery(KEY_CATE_ID, cate2Id));
                String cate3Id = String.valueOf(item.getPhyCategory3Id());
                knowledgeScope.should(QueryBuilders.matchQuery(KEY_CATE_ID, cate3Id));
                String cate4Id = String.valueOf(item.getPhyCategory4Id());
                knowledgeScope.should(QueryBuilders.matchQuery(KEY_CATE_ID, cate4Id));
            }
        }
        // 如果是订单带的商品，则为一个列表，为了避免扩大不必要的范围，只匹配商品知识本身, 不匹配类目
        else if (!CollectionUtils.isEmpty(botContext.getOrderItems())) {
            for (Item orderItem : botContext.getOrderItems()) {
                knowledgeScope.should(QueryBuilders.matchQuery(KEY_ITEM_ID, String.valueOf(orderItem.getItemId())));
            }
        }
        // 如果是订单带的商品，则为一个列表，为了避免扩大不必要的范围，只匹配商品知识本身, 不匹配类目
        else if (!CollectionUtils.isEmpty(botContext.getCardItems())) {
            for (Item cardItem : botContext.getCardItems()) {
                knowledgeScope.should(QueryBuilders.matchQuery(KEY_ITEM_ID, String.valueOf(cardItem.getItemId())));
            }
        }

        // 如果已经有条件，说明是有一定上下文范围限定的，在此之外需要加一个不带任何标签限定的条件，作为通用知识范围
        if (knowledgeScope.hasClauses()) {
            BoolQueryBuilder knowledgeScopeWithNoLabel = QueryBuilders.boolQuery();
            // 通用知识
            knowledgeScopeWithNoLabel.must(QueryBuilders.matchQuery(KEY_CATE_ID, AlgoInfo.CATE_ID_DEFAULT));
            knowledgeScope.should(knowledgeScopeWithNoLabel);
            queryBuilder.must(knowledgeScope);
        }
        // 反之，是在全量的知识里做匹配，反向筛除掉一些知识
        else {
            BoolQueryBuilder knowledgeScopeIgnored = QueryBuilders.boolQuery();

            if (!CollectionUtils.isEmpty(botApolloConfig.getFaqIgnoreList())) {
                for (Long ignoredItemId : botApolloConfig.getFaqIgnoreList()) {
                    knowledgeScopeIgnored.mustNot(QueryBuilders.matchQuery(KEY_ITEM_ID, String.valueOf(ignoredItemId)));
                }
            }
            queryBuilder.must(knowledgeScopeIgnored);
        }

        // 文本分词后的匹配字段
        Map<String, String> matchParams = new HashMap<>(2);
        // 自带的分词器
        matchParams.put(KEY_CLEAN_QUESTION, textBasicData.getCleanedText());

        // 去除停用词
        List<String> filteredWords = textBasicData.getWords().stream().filter(x -> !dictService.isStopWord(x)).collect(Collectors.toList());
        matchParams.put(KEY_TERMS, StringUtils.join(filteredWords, StringUtils.SPACE));

        BoolQueryBuilder matchShouldBool = QueryBuilders.boolQuery();
        for (Map.Entry<String, String> param : matchParams.entrySet()) {
            matchShouldBool.should(QueryBuilders.matchQuery(param.getKey(), param.getValue()));
        }
        queryBuilder.must(matchShouldBool);

        // 过滤
        BoolQueryBuilder knowledgeIDScopeIgnored = QueryBuilders.boolQuery();
        log.debug("InputAssociateService.queryBuilder ignore faq list: {}", inputAssocisateConfig.getInputAssociateIgnoreFaqIdList());
        if (!CollectionUtils.isEmpty(inputAssocisateConfig.getInputAssociateIgnoreFaqIdList())) {
            for (Long ignoredKnowledId : inputAssocisateConfig.getInputAssociateIgnoreFaqIdList()) {
                knowledgeIDScopeIgnored.mustNot(QueryBuilders.termQuery(KEY_KNOWLEDGE_ID, ignoredKnowledId));
            }
        }
        queryBuilder.must(knowledgeIDScopeIgnored);


        return queryBuilder;
    }

    private List<String> search(BotContext botContext, String input) {
        Set<String> results = new HashSet<>();
        try {
            QueryBuilder queryBuilder = queryBuilder(botContext, input);

            log.debug("InputAssociateService.search queryBuilder: {}", queryBuilder.toString());
            SearchResponse response = EsUtil.search(textUrl, appName, indexName, queryBuilder, max);
            if (ArrayUtils.isNotEmpty(response.getHits().getHits())) {
                for (SearchHit hit : response.getHits().getHits()) {
                    AlgoInfo algoInfo = IoUtils.parseJson(IoUtils.toJsonString(hit.getSourceAsMap()), AlgoInfo.class);
                    // 发现返回的数据，有的会缺失字段，需要校验一下
                    if (algoInfo.getKnowledgeId() != 0 && !StringUtils.isEmpty(algoInfo.getQuestion()) && algoInfo.getQuestion().length() <= MAX_LENGTH) {
                        results.add(algoInfo.getQuestion());
                    }
                }
            }
        } catch (Exception e) {
            log.error("InputAssociateService search", e);
        }

        return new ArrayList<>(results);
    }
}
