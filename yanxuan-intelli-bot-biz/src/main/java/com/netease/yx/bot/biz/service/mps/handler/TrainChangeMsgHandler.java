/**
 * @(#)SessionAnalysisMsgHandler.java, 4/18/23.
 * <p/>
 * Copyright 2023 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service.mps.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.common.RedisService;
import com.netease.yx.bot.biz.common.mps.MpsBaseHandler;
import com.netease.yx.bot.biz.common.mps.MpsHandler;
import com.netease.yx.bot.biz.common.mps.MpsReceiveMessageBean;
import com.netease.yx.bot.biz.service.mps.TrainChange;
import com.netease.yx.bot.biz.service.mps.TrainTask;
import com.netease.yx.bot.biz.service.sync.ItemAttributeSyncService;
import com.netease.yx.bot.biz.service.sync.KnowledgeSyncService;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.constant.RedisKey;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaRslt;
import com.netease.yx.bot.core.model.train.TrainContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 百灵会话消息处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@MpsHandler(product = "yanxuan-kfkm", topic = "kefu_qa_train_task_state_changed")
public class TrainChangeMsgHandler extends MpsBaseHandler {
    private static final String GET_QA_TRAIN_TASK_URL = "/api/qatrain/getQaTrainTask";
    private static final String GET_QA_CONTEXT_URL = "/api/qatrain/getQaConetextByTaskId";
    private static final String KEY_TASKID = "taskId";
    private static final int TASK_CHANGE = 2;
    @Autowired
    private RedisService redisService;
    @Autowired
    private EnvService envService;
    @Value("${remote.kfkm.host}")
    private String remoteKfkmHost;

    @Autowired
    private KnowledgeSyncService knowledgeSyncService;

    @Autowired
    private ItemAttributeSyncService itemAttributeSyncService;

    @SneakyThrows
    @Override
    public void process(MpsReceiveMessageBean mpsMessage) {
        TrainChange trainChange = IoUtils.parseJson(mpsMessage.getPayload(), TrainChange.class);
        log.info("TrainChangeMsgHandler {}", trainChange);
        // 组装需要的配置
        if (trainChange.getSituation() == TASK_CHANGE) {
            String taskId = trainChange.getTaskId();

            TrainTask trainTask = getTrainTask(taskId);
            //
            List<TrainContext> trainContexts = buildTrainDetail(taskId);

            if (trainTask != null && CollectionUtils.isNotEmpty(trainContexts)) {
                ChannelQaRslt channelQaRslt = trainTask.getLatestRecommendKnowledge().get(0);
                if (channelQaRslt.getKnowledgeSource() == 1) {
                    knowledgeSyncService.sync(channelQaRslt.getKnowledgeId());
                } else if (channelQaRslt.getKnowledgeSource() == 2) {
                    itemAttributeSyncService.sync(channelQaRslt.getItemId(), channelQaRslt.getKnowledgeId());
                }

                // 线上环境下，FAQ 不需要保存
                if (channelQaRslt.getKnowledgeSource() == 1 && envService.isReleaseOrOnline()) {
                    log.info("ignore faq train {}", trainTask);
                    return;
                }
                List<String> messageContents = new ArrayList<>();
                for (TrainContext trainContext : trainContexts) {
                    messageContents.add(trainContext.getMessageContent());
                }

                trainChange.setMessageContents(messageContents);
                trainChange.setPlatform(trainTask.getPlatform());
                // 选择的答案有itemId的，才会触发，否则都是按itemId为0来处理。
                if (channelQaRslt.getItemId() != 0) {
                    trainChange.setItemId(channelQaRslt.getItemId());
                }

                trainChange.setChannelQaRslt(channelQaRslt);

                // 保存配置
                String cacheKey = StringUtils.join(RedisKey.PLATFROM_ITEMID_2_TrainChange, trainChange.getPlatform(), trainChange.getItemId());

                List<TrainChange> trainChanges = redisService.getObj(cacheKey, new TypeReference<List<TrainChange>>() {
                });

                if (CollectionUtils.isEmpty(trainChanges)) {
                    trainChanges = new ArrayList<>();
                }
                Set<String> taskIds = new HashSet<>();
                List<TrainChange> newTrainChanges = new ArrayList<>();
                for (TrainChange trainChange1 : trainChanges) {
                    // 老的删除
                    if (StringUtils.equals(trainChange1.getTaskId(), taskId)) {
                        continue;
                    }

                    if (taskIds.contains(trainChange1.getTaskId())) {
                        continue;
                    }
                    taskIds.add(trainChange1.getTaskId());
                    newTrainChanges.add(trainChange1);
                }
                trainChanges.add(0, trainChange);
                log.info("save train config {} {}", cacheKey, trainChanges);
                // 一个月生效
                redisService.set(cacheKey, IoUtils.toJsonString(trainChanges), 60 * 24 * 30);
            }
        }

        log.info("TrainChangeMsgHandler {}", mpsMessage.getPayload());
    }

    private List<TrainContext> buildTrainDetail(String taskId) {
        String url = remoteKfkmHost + GET_QA_CONTEXT_URL;
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair(KEY_TASKID, taskId));

        try {
            log.info("buildTrainDetail {} {}", url, params);
            String resp = HttpUtils.executePost(url, params);
            log.info("buildTrainDetail {}", resp);
            SucResp<List<TrainContext>> sucResp = IoUtils.parseJson(resp, new TypeReference<SucResp<List<TrainContext>>>() {
            });
            if (CollectionUtils.isNotEmpty(sucResp.getData())) {
                return sucResp.getData();
            }
        } catch (IOException e) {
            log.error("buildTrainDetail", e);
        }
        return new ArrayList<>();
    }

    private TrainTask getTrainTask(String taskId) {
        String url = remoteKfkmHost + GET_QA_TRAIN_TASK_URL;
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair(KEY_TASKID, taskId));

        try {
            log.info("getTrainTask {} {}", url, params);
            String resp = HttpUtils.executePost(url, params);
            log.info("getTrainTask {}", resp);
            SucResp<TrainTask> sucResp = IoUtils.parseJson(resp, new TypeReference<SucResp<TrainTask>>() {
            });
            if (ObjectUtils.isNotEmpty(sucResp.getData())) {
                return sucResp.getData();
            }
        } catch (IOException e) {
            log.error("buildTrainDetail", e);
        }
        return null;
    }
}