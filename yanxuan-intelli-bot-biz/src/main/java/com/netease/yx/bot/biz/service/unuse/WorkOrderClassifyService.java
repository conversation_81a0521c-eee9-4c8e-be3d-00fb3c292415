package com.netease.yx.bot.biz.service.unuse;

import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.core.model.constant.ReplyState;
import com.netease.yx.bot.core.model.entity.workbench.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 需要保留，不能删除
 */
@Slf4j
@Service
public class WorkOrderClassifyService {
    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;

    @RequestMapping("classify.do")
    public SessionResp classify(@RequestParam(value = "content") String content) {

        RcmdResult rcmdResult = new RcmdResult(RcmdType.TOP_DEFAULT, new ArrayList<>(), SummaryResult.buildDefaultResult(), 0);
        rcmdResult.getCateResults().addAll(0,
                channelQaApolloConfig.getSessionRcmdCates().stream().map(x -> new CateResult(x, "", 1.0)).collect(Collectors.toList()));
        rcmdResult.setRcmdType(RcmdType.MODEL);

        return new SessionResp(ReplyState.SUCCESS, rcmdResult.getRcmdType(), rcmdResult.getCateResults(), rcmdResult.getSummaryResult(), rcmdResult.getSessionLength());
    }
}
