/**
 * @(#)FaqApolloConfig.java, 2020/6/2.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.applloconfig;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.spring.annotation.EnableAutoUpdateApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ValueMapping;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Component
@EnableAutoUpdateApolloConfig("faq")
@Data
@Slf4j
public class FaqApolloConfig {
    private static final String ODD = "x";
    private static final String SPECIAL = "y";
    private static final String ALL = "*";

    private static final String USE_TRUE = "true";
    /**
     * 关键词直接到FAQ的映射
     */
    @ValueMapping("${keyword2faq:{}}")
    private String keyword2faqStr;
    /**
     * 是否开启向量召回
     */
    @ValueMapping("${recall.vector.use:false}")
    private String vectorUse;
    /**
     * 是否使用策略
     */
    @ValueMapping("${rank.strategy.use:false}")
    private String strategyUse;
    /**
     * 在通用入口里面要忽略的商品知识id
     */
    @ValueMapping("${faq.item.ignore.list:[]}")
    private List<Long> faqIgnoreList;

    @ValueMapping("${faq.knowledge.ignore.list:[]}")
    private List<Long> faqKnowledgeIgnoreList;

    @ValueMapping("${faq.knowledge.ignore.realTimeQA.list:[20525494]}")
    private List<Long> faqKnowledgeIgnoreRealTimeQAList;

    @ValueMapping("${abt.faq.session.suffix:y}")
    private String abtFaqSessionIdSuffix;

    @ValueMapping("${abt.recall.session.suffix:y}")
    private String abtRecallSessionIdSuffix;

    @ValueMapping("${abt.white.userId.list:26220394605,17946037}")
    private String whiteUserIdList;

    @ValueMapping("${abt.max.return.num:4}")
    private int maxReturnNum;

    @ValueMapping("${faq.multianswer.es.max:10}")
    private int multiAnswerKnowledgeESMax;

    @ValueMapping("${faq.knowledge.es.max:50}")
    private int faqKnowledgeESMax;

    @ValueMapping("${algo.knowledge.index:algo_knowledge_es_index_v3}")
    private String algoKnowledgeIndex;

    @ValueMapping("${useEs6.new:0}")
    private int useEs6New;

    public Map<String, List<Long>> getKeyword2Faq() {
        Map<String, List<Long>> map = new HashMap<>();
        try {
            log.info(keyword2faqStr);
            map = JSON.parseObject(keyword2faqStr, new TypeReference<Map<String, List<Long>>>() {
            });
        } catch (Exception e) {
            log.error("FaqApolloConfig error", e);
        }
        if (map == null) {
            map = new HashMap<>();
        }
        return map;
    }

    public boolean checkVectorUse() {
        return StringUtils.equals(USE_TRUE, vectorUse);
    }

    public boolean checkStrategyUse() {
        return StringUtils.equals(USE_TRUE, strategyUse);
    }

    public boolean useNew(long sessionId) {
        long suffix = sessionId % 10;
        switch (abtFaqSessionIdSuffix) {
            case ODD:
                return suffix % 2 == 0;
            case ALL:
                return true;
            default:
                if (abtFaqSessionIdSuffix.length() == 1) {
                    return StringUtils.equals(abtFaqSessionIdSuffix, String.valueOf(suffix));
                } else {
                    return StringUtils.equals(abtFaqSessionIdSuffix, String.valueOf(sessionId));
                }
        }
    }

    public List<String> getWhiteUserIdList() {
        if (!StringUtils.isEmpty(whiteUserIdList)) {
            return Arrays.asList(whiteUserIdList.split(","));
        } else {
            return new ArrayList<>();
        }
    }

    public int getMaxReturnNum(long sessionId) {
        return useNew(sessionId) ? maxReturnNum : 4;
    }

    public boolean checkAbt(String sessionId, String config) {
        try {
            if (sessionId != null && sessionId.length() > 1) {
                String suffix = sessionId.substring(sessionId.length() - 1);

                switch (config) {
                    case ODD:
                        if (StringUtils.isNumeric(suffix)) {
                            int id = Integer.parseInt(suffix);
                            return id % 2 == 0;
                        } else {
                            return false;
                        }
                    case SPECIAL:
                        return SPECIAL.equals(suffix);
                    case ALL:
                        return true;
                    default:
                        return StringUtils.isNumeric(suffix) && config.equals(suffix);
                }
            }
        } catch (Exception e) {
            log.error("checkAbt", e);
        }

        return false;
    }
}