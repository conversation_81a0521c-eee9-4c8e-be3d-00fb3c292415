/**
 * @(#)QcIdempotentDao.java, 2018/3/22.
 * <p/>
 * Copyright 2018 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.common.mps;


/**
 * <AUTHOR>
 */
public interface IdempotentDAO {

    String DUPLICATED_RECORD_MESSAGE = "Duplicate record.";

    String DUPLICATED_ENTRY = "Duplicate entry";

    /**
     * 插入记录
     *
     * @param record 记录
     */
    void insert(String record);

    /**
     * 通过幂等记录删除记录
     *
     * @param record 幂等记录
     */
    void deleteByRecord(String record);
}
