/**
 * @(#)ProphetService.java, 2020/9/3.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.manager;

import com.netease.yanxuan.log.api.statlog.StatLogger;
import com.netease.yanxuan.log.api.statlog.StatLoggerFactory;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.service.ItemService;
import com.netease.yx.bot.biz.service.UserService;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.biz.applloconfig.FrequentlyModifiedApolloConfig;
import com.netease.yx.bot.biz.service.KnowledgeService;
import com.netease.yx.bot.biz.service.qa.ProphetRuleService;
import com.netease.yx.bot.biz.service.qa.recall.RecallServiceWithEs8;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.constant.DeviceType;
import com.netease.yx.bot.core.model.constant.PlatformType;
import com.netease.yx.bot.core.model.constant.ProphetType;
import com.netease.yx.bot.core.model.constant.SourceType;
import com.netease.yx.bot.core.model.entity.AlgoInfo;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.UserOrder;
import com.netease.yx.bot.core.model.entity.item.Item;
import com.netease.yx.bot.core.model.entity.prophet.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR> @ corp.netease.com)
 * APP-机器人主动预测，猜你想问
 * 目前主要是读取和判断配置为主，不再更新
 */
@Service
@Slf4j
public class ProphetManager {
    private static final String LOG_TOPIC = "PROPHET_PIPELINE";
    private static final String LOG_MODULE = "ProphetService";
    private static final int MAX_ORDER_FAQS = 5;

    @Autowired
    private EnvService envService;
    @Autowired
    private KnowledgeService knowledgeService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private BotApolloConfig botApolloConfig;
    @Autowired
    private FrequentlyModifiedApolloConfig frequentlyModifiedApolloConfig;
    @Autowired
    private UserService userService;
    @Autowired
    private ItemService itemService;
    @Autowired
    private ProphetRuleService prophetRuleService;
    @Autowired
    private RecallServiceWithEs8 recallServiceWithEs8;
    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;

    @Autowired
    private TestMockManager testMockManager;

    @Value("${prophet.faq.max}")
    private int faqMax;

    private ProphetRespWithLog pipelineWithLog(ProphetReq req, BotContext botContext) {
        ProphetPipelineLog prophetPipelineLog = new ProphetPipelineLog();
        prophetPipelineLog.setInput(req);
        // 获取兜底推荐
        ProphetDefaultConfig bottomConfig = botApolloConfig.getBottomDefaultConfig();

        ProphetDefaultConfig resultConfig = new ProphetDefaultConfig();
        if (ObjectUtils.isNotEmpty(bottomConfig)) {
            resultConfig = bottomConfig;
            log.info("bottom config {}", resultConfig);
        }

        long itemId = req.getItemId();
        SourceType sourceType = SourceType.get(req.getSource());

        // 渠道入口配置
        Map<String, ProphetDefaultConfig> sourceDefaultConfig = botApolloConfig.getSourceDefaultConfig();
        if (MapUtils.isNotEmpty(sourceDefaultConfig) && sourceDefaultConfig.containsKey(sourceType.name())) {
            resultConfig = sourceDefaultConfig.get(sourceType.name());
            log.info("source config {} {}", sourceType.name(), resultConfig);
        }

        // 新用户置顶
        if (botContext.isNewUser()) {
            resultConfig.getKnowledgeIds().addAll(0, botApolloConfig.getProphetNewFlagFaqListConfig());
            resultConfig.getShortcutIds().addAll(0, botApolloConfig.getProphetNewFlagShortcutListConfig());
            log.info("newuser config {}", resultConfig);
        }

        // 商品知识库
        if (itemId != 0) {
            List<Long> itemKnowledgeIds = knowledgeService.getItemId2AlgoInfoMap().getOrDefault(itemId, new ArrayList<>()).stream().map(AlgoInfo::getKnowledgeId).collect(Collectors.toList());
            // TODO 这里需要换热度
            Collections.shuffle(itemKnowledgeIds);
            resultConfig.getKnowledgeIds().addAll(0, itemKnowledgeIds);
            log.info("item config {} {}", itemId, resultConfig);
        }
        // 商详页如果是售前，则不走用户状态推荐
        boolean preSaleFlag = SourceType.ITEM_DETAIL.equals(botContext.getSourceType()) && botContext.isPreSale();
        preSaleFlag = false;
        // 用户状态推荐
        try {
            if (!preSaleFlag && botApolloConfig.useProphetUserState()) {

                // 获取用户30天的用户订单
                List<UserOrder> userOrders = userService.queryOrderByUserId(req.getUserId(), req.getOrderId());
                // 依据策略确定中心订单
                UserOrder centerUserOrder = userService.getCenterUserOrder(userOrders);

                // 测试环境mock数据
                if (envService.isTest()) {
                    centerUserOrder = testMockManager.testMockUserOrder(req);
                }

                // 工单状态加入botContext中
                if (centerUserOrder == null) {
                    botContext.setTicketStatus(-100);
                } else {
                    botContext.setTicketStatus(centerUserOrder.getTicketStatus());
                }

                prophetPipelineLog.setUserOrders(userOrders);
                prophetPipelineLog.setCenterUserOrder(centerUserOrder);

                log.info("ProphetRespWithLog {} {} {}", req.getUserId(), centerUserOrder, botContext);

                // 根据订单和上下文确定命中规则情况
                ProphetRule rule = prophetRuleService.check(centerUserOrder, botContext);
                if (ObjectUtils.isNotEmpty(rule)) {
                    log.info("userStat ProphetRule new {}", rule);
                    if (!CollectionUtils.isEmpty(rule.getFaqIdList())) {
                        resultConfig.getKnowledgeIds().addAll(0, rule.getFaqIdList());
                    }
                    if (!CollectionUtils.isEmpty(rule.getCardIdList())) {
                        resultConfig.getCardIds().addAll(0, rule.getCardIdList());
                    }
                    log.info("ProphetRule itemConfig {} {}", itemId, resultConfig);
                }
            }
        } catch (Exception e) {
            log.error("userOrder", e);
        }

        // 商品强制置顶知识
        Map<String, List<Long>> itemDefaultConfig = botApolloConfig.getProphetItemDefaultConfig();
        if (itemDefaultConfig != null && itemId != 0) {
            List<Long> itemConfigKnowledgeIds = itemDefaultConfig.getOrDefault(String.valueOf(itemId), new ArrayList<>());
            log.info("item2 config {} {}", itemId, resultConfig);
            resultConfig.getKnowledgeIds().addAll(0, itemConfigKnowledgeIds);
        }

        log.debug("ProphetService.pipelineWithLog knowledge, {}", resultConfig.getKnowledgeIds());

        List<Long> topFaqList = frequentlyModifiedApolloConfig.getProphetTopFaqListConfig();

        // FAQ全局强制置顶
        if (CollectionUtils.isNotEmpty(topFaqList)) {
            resultConfig.getKnowledgeIds().addAll(0, topFaqList);
            log.info("top config {}", resultConfig);
        }

        log.info("raw KnowledgeIds {}", resultConfig.getKnowledgeIds());

        // 快捷短语全局强制置顶
        List<Long> topShortcutList = botApolloConfig.getProphetTopShortcutListV2();
        if (CollectionUtils.isNotEmpty(topShortcutList)) {
            resultConfig.getShortcutIds().addAll(0, topShortcutList);
            log.info("top topShortcutList {}", topShortcutList);
        }

        List<Long> knowledgeIds = resultConfig.getKnowledgeIds().stream().distinct().filter(x -> recallServiceWithEs8.hasRobotAnswer(x)).limit(req.getReturnFaqNum()).collect(Collectors.toList());
        List<Long> shortcutIds = resultConfig.getShortcutIds().stream().filter(x -> knowledgeService.getShortcutIdSet().contains(x)).distinct().limit(faqMax).collect(Collectors.toList());
        List<Long> cardIds = resultConfig.getCardIds().stream().distinct().collect(Collectors.toList());

        ProphetResp resp = ProphetResp.builder()
                .knowledgeIds(knowledgeIds)
                .shortcutIds(shortcutIds)
                .cardIds(cardIds)
                .prophetType(ProphetType.DEFAULT)
                .sourceType(sourceType)
                .build();

        prophetPipelineLog.setConfig(resp.getKnowledgeIds());

        Map<String, Object> other = new HashMap<>();
        other.put(ProphetPipelineLog.KEY_OTHER_NEW_USER, botContext.isNewUser());
        prophetPipelineLog.setOther(other);

        return new ProphetRespWithLog(resp, prophetPipelineLog);
    }

    private BotContext addContext(ProphetReq req) {
        long sessionId = req.getSessionId();
        long spuId = req.getItemId();
        long orderId = req.getOrderId();
        long userId = req.getUserId();

        BotContext botContext = BotContext.builder()
                .sessionId(sessionId)
                .itemId(spuId)
                .orderId(orderId)
                .preSale(req.isPresale())
                .userId(userId)
                .platformType(PlatformType.get(req.getPlatform()))
                .sourceType(SourceType.get(req.getSource()))
                .deviceType(DeviceType.get(req.getDevice()))
                .userRGCount24H(req.getUserRGCount24H())
                .userRGCount48H(req.getUserRGCount48H())
                .userR(req.getUserR())
                .userV(req.getUserV())
                .newUser(req.isNewUser())
                .sessionInteraction(1)
                .channel(1)
                .platform("qy")
                .consultTime(System.currentTimeMillis())
                .build();

        try {
            if (spuId != 0) {
                String itemIdKey = BotContext.buildItemIdContextKey(sessionId);
                String preItemIds = stringRedisTemplate.opsForValue().get(itemIdKey);
                String itemIdValue = BotContext.buildItemIdContextVal(preItemIds, spuId);
                stringRedisTemplate.opsForValue().set(itemIdKey, itemIdValue, 30, TimeUnit.MINUTES);
                log.info("addContext spuId {} {}", itemIdKey, itemIdValue);
            }

            Item item = itemService.getItemById(req.getItemId());

            /**
             * 把订单详情取出来
             */
            List<UserOrder> userOrders = new ArrayList<>();
            /**
             * 订单里面的卡片取出来
             */
            List<Long> orderItemIds = new ArrayList<>();
            List<Item> orderItems = new ArrayList<>();

            if (orderId != 0) {
                // 这里会唯一确定一个订单
                userOrders.addAll(userService.queryOrderByUserId(userId, orderId));
                if (!CollectionUtils.isEmpty(userOrders)) {
                    /**
                     * 去重
                     */
                    orderItemIds.addAll(userOrders.stream().map(UserOrder::getItemId).collect(Collectors.toSet()));
                    /**
                     * 过滤null
                     */
                    orderItems.addAll(orderItemIds.stream().map(x -> itemService.getItemById(x)).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList()));
                }
            }
            // 这里把来自商详的item， 和可能来自订单的item都放到上下文中
            botContext = BotContext.builder()
                    .sessionId(sessionId)
                    .itemId(spuId)
                    .orderId(orderId)
                    .preSale(req.isPresale())
                    .userId(userId)
                    .item(item)
                    .orderItemIds(orderItemIds)
                    .orderItems(orderItems)
                    .userOrder(userService.getCenterUserOrder(userOrders))
                    .platformType(PlatformType.get(req.getPlatform()))
                    .sourceType(SourceType.get(req.getSource()))
                    .deviceType(DeviceType.get(req.getDevice()))
                    .userRGCount24H(req.getUserRGCount24H())
                    .userRGCount48H(req.getUserRGCount48H())
                    .userR(req.getUserR())
                    .userV(req.getUserV())
                    .newUser(req.isNewUser())
                    .sessionInteraction(1)
                    .channel(1)
                    .platform("qy")
                    .consultTime(System.currentTimeMillis())
                    .build();

            String botContextStr = IoUtils.toJsonString(botContext);
            String contextKey = BotContext.buildContextKey(sessionId);

            stringRedisTemplate.opsForValue().set(contextKey, botContextStr, 30, TimeUnit.MINUTES);
            log.info("addContext context {} {}", contextKey, botContextStr);

        } catch (Exception e) {
            log.error("predict addContext", e);
        }

        return botContext;
    }

    public ProphetResp pipeline(ProphetReq req) {

        // 记录用户访问，这个在人工辅助那边会用，怎么用未知
        long timestamp = System.currentTimeMillis() / 1000;
        String userAccessKey = BotContext.buildUserRobotAccessKey(req.getUserId());
        String userAccessHst = stringRedisTemplate.opsForValue().get(userAccessKey);
        if (userAccessHst != null && userAccessHst.length() >= 10) {
            stringRedisTemplate.opsForValue().set(userAccessKey, userAccessHst + "," + timestamp, 48, TimeUnit.HOURS);
        } else {
            stringRedisTemplate.opsForValue().set(userAccessKey, String.valueOf(timestamp), 48, TimeUnit.HOURS);
        }

        // 添加上下文
        BotContext botContext = addContext(req);
        ProphetRespWithLog prophetRespWithLog = pipelineWithLog(req, botContext);

        StatLogger statLogger = StatLoggerFactory.getLogger(LOG_MODULE);
        statLogger.log(LOG_TOPIC, ProphetPipelineLog.buildMap(prophetRespWithLog.getProphetPipelineLog()));
        return prophetRespWithLog.getProphetResp();
    }


    /**
     * 订单推荐问
     */
    public List<Long> orderRcmdQues(BotContext botContext, long orderId, long packageId) {
        log.info("order req user: {}, order: {}", botContext.getUserId(), orderId);

        List<Long> orderRcmdKnowledgeIds = new ArrayList<>();
        List<UserOrder> userOrders = userService.queryOrderByUserId(botContext.getUserId(), orderId);

        log.info("center orders origin: {}", userOrders);

        UserOrder centerUserOrder;
        List<UserOrder> userOrderFilters = userOrders.stream().filter(x -> String.valueOf(packageId).equals(x.getPackageId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userOrderFilters)) {
            centerUserOrder = userService.getCenterUserOrder(userOrders);
        } else {
            centerUserOrder = userService.getCenterUserOrder(userOrderFilters);
        }

        // 测试环境mock数据
        if (envService.isTest()) {
            ProphetReq req = new ProphetReq();
            req.setUserId(12342212);
            centerUserOrder = testMockManager.testMockUserOrder(req);
            botContext = testMockManager.testMockBotContext(req);
        }

        log.info("center orders: {}", centerUserOrder);

        ProphetRule rule = prophetRuleService.check(centerUserOrder, botContext);
        if (rule != null) {
            log.info("order rcmd userStat ProphetRule new {}", rule);
            if (!CollectionUtils.isEmpty(rule.getFaqIdList())) {

                orderRcmdKnowledgeIds.addAll(0, rule.getFaqIdList());
            }
        }

        orderRcmdKnowledgeIds = orderRcmdKnowledgeIds.stream().distinct().filter(x -> knowledgeService.getIdKnowledgeMap().containsKey(x)).collect(Collectors.toList());

        List<Long> dufaultFaqList = botApolloConfig.getOrderRcmdFaqIdList();
        log.info("order rcmd default faqs: {}", dufaultFaqList);

        if (CollectionUtils.isEmpty(orderRcmdKnowledgeIds)) {
            return dufaultFaqList;
        }

        if (orderRcmdKnowledgeIds.size() < MAX_ORDER_FAQS) {
            for (Long kid : botApolloConfig.getOrderRcmdFaqIdList()) {
                if (!orderRcmdKnowledgeIds.contains(kid)) {
                    orderRcmdKnowledgeIds.add(kid);
                }
            }
        }

        log.info("order rcmd rule faqs: {}", orderRcmdKnowledgeIds);
        if (orderRcmdKnowledgeIds.size() > MAX_ORDER_FAQS) {
            return orderRcmdKnowledgeIds.subList(0, MAX_ORDER_FAQS);
        }
        return orderRcmdKnowledgeIds;
    }
}