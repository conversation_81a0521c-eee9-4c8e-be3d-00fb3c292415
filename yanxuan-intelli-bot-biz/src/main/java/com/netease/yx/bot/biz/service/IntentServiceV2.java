/**
 * @(#)IntentManager.java, 2020/5/11.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.netease.yx.bot.biz.common.NlpServerService;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.constant.Level1IntentType;
import com.netease.yx.bot.core.model.entity.intent.IntentResp;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @ corp.netease.com)
 * 比较粗的意图
 */
@Component
@Slf4j
public class IntentServiceV2 {
    private static final String HEADER_KEY_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_VALUE_CONTENT_TYPE = "application/json";
    private static final String HEADER_KEY_HOST_TYPE = "host";
    private static final String HEADER_VALUE_HOST_TYPE = "yanxuan-intent.yx-serving.svc";
    private static final String REQUEST_KEY_INSTANCES = "instances";
    private static final String REQUEST_KEY_MODE = "mode";
    private static final String REQUEST_VALUE_MODE = "http";

    private static final String PARAM_SEN_KEY = "sen";
    private static final String PARAM_VERSION = "version";
    private static final String PARAM_VALUE_A = "A";
    private static final String PARAM_VALUE_B = "B";

    private static final Map<String, String> HEADERS = new HashMap<String, String>(2) {
        {
            put(HEADER_KEY_CONTENT_TYPE, HEADER_VALUE_CONTENT_TYPE);
            put(HEADER_KEY_HOST_TYPE, HEADER_VALUE_HOST_TYPE);
        }
    };

    @Value("${intent_url}")
    private String intentUrl;

    @Autowired
    private BotApolloConfig botApolloConfig;

    @Autowired
    private NlpServerService nlpServerService;

    public IntentRslt getIntent(String sentence) {
        IntentRslt intentRslt = null;
        try {
            Map<String, Object> reqObj = new HashMap<>(2);
            reqObj.put(REQUEST_KEY_INSTANCES, Lists.newArrayList(sentence));
            reqObj.put(REQUEST_KEY_MODE, REQUEST_VALUE_MODE);

            log.info("IntentService, request: {}, {}", intentUrl, reqObj);
            String response = HttpUtils.executePost(intentUrl, IoUtils.toJsonString(reqObj), null, HEADERS);
            log.info("IntentService, response: {}", response);
            // 返回可能是 ""，需要替换为 null
            response = response.replace("\"\"", "null");

            SucResp<IntentResp> result = IoUtils.parseJson(response, new TypeReference<SucResp<IntentResp>>() {
            });
            intentRslt = result.getData().getPredictions().get(0);
        } catch (IOException e) {
            log.error("intent exception", e);
        }

        if (intentRslt != null) {
            return intentRslt;
        } else {
            return new IntentRslt(Level1IntentType.FAQ);
        }
    }

    public IntentRslt getIntentV2(String sentence) {
        if (StringUtils.equals(sentence, "URL")) {
            return new IntentRslt(Level1IntentType.KBQA);
        }
        int code = nlpServerService.intent(sentence);
        return new IntentRslt(transform(code));
    }

    private Level1IntentType transform(int code) {
        switch (code) {
            case 0:
                return Level1IntentType.KBQA;
            case 2:
                return Level1IntentType.GUIDE;
            case 3:
                return Level1IntentType.CHITCHAT;
            case 4:
                return Level1IntentType.SPECIAL;
            default:
                return Level1IntentType.FAQ;
        }
    }

    public boolean isQuerySize(String query) {
        if (StringUtils.isEmpty(query) || query.length() <= 1) {
            return false;
        }
        return !query.contains("不") && (query.contains("码") || query.contains("斤") || query.contains("kg") ||
                query.contains("多大") || query.contains("cm") || query.contains("高") || query.contains("大小") || query.contains("合适"));
    }
}