package com.netease.yx.bot.biz.common;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.SimpleKnowledge;
import com.netease.yx.bot.core.model.entity.attr.AttrResp;
import com.netease.yx.bot.core.model.entity.attr.Query2AttrResp;
import com.netease.yx.bot.core.model.nlpserver.NlpReq;
import com.netease.yx.bot.core.model.nlpserver.ReqInstance;
import com.netease.yx.bot.core.model.nlpserver.chat.ChatResp;
import com.netease.yx.bot.core.model.nlpserver.classification.ClassificationResp;
import com.netease.yx.bot.core.model.nlpserver.faq.FaqResp;
import com.netease.yx.bot.core.model.nlpserver.faq.FaqRsltInstance;
import com.netease.yx.bot.core.model.nlpserver.secure.SecureResp;
import com.netease.yx.bot.core.model.nlpserver.similarity.SimilarityResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class NlpServerService {
    public static final String EXTRA_TIMEOUT = "timeout";
    private static final String EXTRA_MAX_TOKENS = "maxTokens";

    private static final String PRODUCT_CODE = "yanxuan-intelli-bot";
    private static final String SERVICE_CHAT_CHATGPT = "chat_chatgpt";
    private static final String SERVICE_SECURE_YIDUN = "secure_yidun";
    private static final String SERVICE_KEFU_INTENT = "sc-yx-intention-class";
    private static final String SERVICE_KEFU_SIMI = "sim-nlp_structbert_sentence-similarity_chinese-retail-base";
    private static final String SERVICE_QUERY_2_ATTR = "query-2-attribute";

    private static final String SERVICE_FAQ_COMMON = "faq_common";
    private static final String EXTRA_REQ_PROMPT = "prompt";
    private static final String EXTRA_REQ_INSTANCE_ROLE = "role";
    private static final int PASS = 0;
    private static final int UNCERTAIN = 1;
    private static final int REJECT = 2;
    private static final int ERROR = -1;

    @Value("${nlp.server.url}")
    private String nlpServerUrl;

    public <T> T process(String sen, String serviceCode, Map<String, Object> extras, TypeReference<T> typeReference) {
        return process(buildReqInstance(sen), serviceCode, extras, typeReference, 5000);
    }

    public <T> T process(List<ReqInstance> reqInstances, String serviceCode, Map<String, Object> extras, TypeReference<T> typeReference, int timeout) {
        NlpReq nlpReq = new NlpReq(reqInstances, PRODUCT_CODE, serviceCode, extras);
        try {
            log.info("NlpServerManager {} {}", nlpServerUrl, nlpReq);
            String resp = HttpUtils.executePost(nlpServerUrl, IoUtils.toJsonString(nlpReq), null, null, timeout);
            log.info("NlpServerManager {}", resp);
            return IoUtils.parseJson(resp, typeReference);
        } catch (IOException e) {
            log.error("NlpServerManager", e);
        }
        return null;
    }

    private List<ReqInstance> buildReqInstance(String sen) {
        return Stream.of(sen).map(ReqInstance::new).collect(Collectors.toList());
    }

    public String chat(List<ReqInstance> reqInstances, String prompt, int timeout) {
        Map<String, Object> extras = new HashMap<>();
        if (StringUtils.isNotEmpty(prompt)) {
            extras.put(EXTRA_REQ_PROMPT, prompt);
        }
        extras.put(EXTRA_TIMEOUT, timeout);
        extras.put(EXTRA_MAX_TOKENS, 200);

        SucResp<ChatResp> chatResp = process(reqInstances, SERVICE_CHAT_CHATGPT, extras, new TypeReference<SucResp<ChatResp>>() {
        }, timeout);
        if (ObjectUtils.isNotEmpty(chatResp) && chatResp.getCode() == HttpStatus.SC_OK && ObjectUtils.isNotEmpty(chatResp.getData()) && CollectionUtils.isNotEmpty(chatResp.getData().getChoices())) {
            return chatResp.getData().getChoices().get(0).getMessage().getContent();
        } else {
            log.error("chatgptError no resp, req:" + reqInstances);
        }
        return null;
    }

    public int checkSecure(String sen) {
        SucResp<SecureResp> secureResp = process(sen, SERVICE_SECURE_YIDUN, null, new TypeReference<SucResp<SecureResp>>() {
        });
        if (ObjectUtils.isNotEmpty(secureResp) && secureResp.getCode() == HttpStatus.SC_OK && ObjectUtils.isNotEmpty(secureResp.getData()) && CollectionUtils.isNotEmpty(secureResp.getData().getStatusList())) {
            return secureResp.getData().getStatusList().get(0);
        }
        return ERROR;
    }

    public int intent(String sen) {
        SucResp<ClassificationResp> secureResp = process(sen, SERVICE_KEFU_INTENT, null, new TypeReference<SucResp<ClassificationResp>>() {
        });
        if (ObjectUtils.isNotEmpty(secureResp) && secureResp.getCode() == HttpStatus.SC_OK && ObjectUtils.isNotEmpty(secureResp.getData()) && CollectionUtils.isNotEmpty(secureResp.getData().getLabels())) {
            return secureResp.getData().getLabels().get(0).get(0);
        }
        return ERROR;
    }

    // 未排序后的数据
    public List<SimpleKnowledge> unSortSimilarity(String question, List<String> candidates) {
        List<SimpleKnowledge> result = new ArrayList<>();
        ReqInstance reqInstance = new ReqInstance(question);
        reqInstance.setCandidates(candidates);
        SucResp<SimilarityResp> secureResp = process(Collections.singletonList(reqInstance), SERVICE_KEFU_SIMI, null, new TypeReference<SucResp<SimilarityResp>>() {
        }, 3000);
        log.info("unSortSimilarity {}", secureResp);
        if (ObjectUtils.isNotEmpty(secureResp) && secureResp.getCode() == HttpStatus.SC_OK && ObjectUtils.isNotEmpty(secureResp.getData()) && CollectionUtils.isNotEmpty(secureResp.getData().getScores())) {
            List<Double> resultScore = secureResp.getData().getScores().get(0);

            for (int i = 0; i < resultScore.size(); i++) {
                result.add(new SimpleKnowledge(i, 0, candidates.get(i), resultScore.get(i)));
            }
        }
        log.info("unsort {}", IoUtils.toJsonString(result));
        return result;
    }

    // 排序后的数据，从大到小排序
    public List<SimpleKnowledge> similarity(String question, List<String> candidates, double threshold) {
        List<SimpleKnowledge> result = new ArrayList<>();
        ReqInstance reqInstance = new ReqInstance(question);
        reqInstance.setCandidates(candidates);
        SucResp<SimilarityResp> secureResp = process(Collections.singletonList(reqInstance), SERVICE_KEFU_SIMI, null, new TypeReference<SucResp<SimilarityResp>>() {
        }, 4000);
        log.info("similarity {}", secureResp);
        if (ObjectUtils.isNotEmpty(secureResp) && secureResp.getCode() == HttpStatus.SC_OK && ObjectUtils.isNotEmpty(secureResp.getData()) && CollectionUtils.isNotEmpty(secureResp.getData().getScores())) {
            List<Double> resultScore = secureResp.getData().getScores().get(0);

            for (int i = 0; i < resultScore.size(); i++) {
                if (resultScore.get(i) > threshold) {
                    result.add(new SimpleKnowledge(i, 0, candidates.get(i), resultScore.get(i)));
                }
            }
        }
        Collections.sort(result);
        log.info("sort {}", IoUtils.toJsonString(result));
        return result;
    }

    public List<AttrResp> getAttr(String question, long itemId) {
        ReqInstance reqInstance = new ReqInstance(question);
        Map<String, Object> extra = new HashMap<>();
        extra.put("itemId", itemId);
        extra.put("topK", 3);
        extra.put("scene", "QA");

        SucResp<Query2AttrResp> secureResp = process(Collections.singletonList(reqInstance), SERVICE_QUERY_2_ATTR, extra, new TypeReference<SucResp<Query2AttrResp>>() {
        }, 4000);
        if (ObjectUtils.isNotEmpty(secureResp) && secureResp.getCode() == HttpStatus.SC_OK && ObjectUtils.isNotEmpty(secureResp.getData()) && CollectionUtils.isNotEmpty(secureResp.getData().getAttrs())) {
            return secureResp.getData().getAttrs().get(0);
        }
        return new ArrayList<>();
    }

    public List<Float> getEmbedding(String question) {
        List<Float> list = new ArrayList<>(Collections.nCopies(768, 0.5f));
        return list;
    }

    public List<FaqRsltInstance> faq(String ques, List<String> candidates, List<String> ids) {
        List<ReqInstance> reqInstances = new ArrayList<>();
        ReqInstance reqInstance = new ReqInstance(ques);
        reqInstance.setCandidates(candidates);
        Map<String, Object> extra = new HashMap<>();
        extra.put("knowledge_ids", StringUtils.join(ids, ","));
        reqInstance.setExtra(extra);
        reqInstances.add(reqInstance);
        log.info("faq {}", reqInstances);
        SucResp<FaqResp> resp = process(reqInstances, SERVICE_FAQ_COMMON, null, new TypeReference<SucResp<FaqResp>>() {
        }, 2000);
        log.info("faq {}", resp);
        if (resp != null && resp.getData() != null && CollectionUtils.isNotEmpty(resp.getData().getScores())) {
            return resp.getData().getScores().get(0);
        } else {
            return null;
        }
    }
}
