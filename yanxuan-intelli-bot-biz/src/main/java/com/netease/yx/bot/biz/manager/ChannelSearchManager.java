package com.netease.yx.bot.biz.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.netease.search.client.common.Result;
import com.netease.search.client.vo.AppInfo;
import com.netease.search.search.ESSearchRequest;
import com.netease.search.search.ESSearchResult;
import com.netease.search.search.util.SearchResponseUtil;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.common.SmartWorkService;
import com.netease.yx.bot.biz.service.TextPreprocessService;
import com.netease.yx.bot.biz.applloconfig.ItemSearchApolloConfig;
import com.netease.yx.bot.common.util.CustomLangUtils;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.search.*;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchPhraseQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全渠道商品搜索
 */
@Slf4j
@Service
public class ChannelSearchManager {
    private static final String KEY_OUT_ITEM_NAME = "out_item_name";
    private static final String KEY_ITEM_NICK_NAME = "item_nick_name";
    private static final String KEY_FRONT_SKU_SPEC_VALUE = "front_sku_spec_value";
    private static final String KEY_OUT_SKU_ID = "out_sku_id";
    private static final String KEY_OUT_ITEM_ID = "out_item_id";
    private static final String KEY_ORG_CHANNEL_ID = "org_channel_id";
    private static final String SEP = "\u0002";

    @Value("${recall.text.url}")
    private String textUrl;
    @Value("${recall.text.index.appName}")
    private String appName;

    @Autowired
    private ItemSearchApolloConfig itemSearchApolloConfig;

    @Autowired
    private EnvService envService;

    @Autowired
    private SmartWorkService smartWorkService;

    @Autowired
    private TextPreprocessService textPreprocessService;

    @Autowired
    private TestMockManager testMockManager;


    public static SearchResponse search(String searchUrl, String appName, String indexName, QueryBuilder queryBuilder, int maxSize) {
        try {
            ESSearchRequest esSearchRequest = new ESSearchRequest();
            AppInfo appInfo = new AppInfo(appName);
            esSearchRequest.setAppInfo(appInfo);
            //传入indice对象
            esSearchRequest.setIndexName(indexName);
            //构建searchBuilder对象
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            searchSourceBuilder.size(maxSize).query(queryBuilder);

            log.info("query serial:{} {}", indexName, JSON.toJSON(searchSourceBuilder.toString()));
            esSearchRequest.setSearchRequest(searchSourceBuilder.toString());
            log.info("search serial:{} {}", indexName, JSON.toJSONString(esSearchRequest));

            //构建HTTP参数对象
            NameValuePair nameValuePair = new BasicNameValuePair("data", JSON.toJSONString(esSearchRequest));
            List<NameValuePair> parameters = Lists.newArrayList(nameValuePair);
            String response = HttpUtils.executePost(searchUrl, parameters);
            //解析HTTP结果
            Result<ESSearchResult> httpResult = JSON.parseObject(response, new TypeReference<Result<ESSearchResult>>() {
            });
            if (!httpResult.isSuccess()) {
                log.error("get index error，param result msg");
                throw new RuntimeException(httpResult.getErrorMsg());
            }
            //获取搜索结果
            ESSearchResult esSearchResult = httpResult.getModel();
            //转换为通用对象
            return SearchResponseUtil.getSearchResponse(esSearchResult);
        } catch (Exception e) {
            log.error("search error");
            throw new RuntimeException(e);
        }
    }

    private static String makeInvertField(Long index, String key) {
        return String.format("%d:%s", index, key);
    }

    public SearchResp process(SearchReq searchReq, boolean duplicate) {
        if (envService.isTest()) {
            return testMockManager.mockItemSearch(searchReq);
        }
        QueryBuilder queryBuilder = buildQuery(searchReq);
        List<SearchItemInfo> itemInfos = new ArrayList<>();
        Set<String> outItemIds = new HashSet<>();
        try {
            SearchResponse searchResponse = search(textUrl, appName, itemSearchApolloConfig.getDumpIndex(), queryBuilder, 100);
            if (searchResponse.getHits().getHits().length > 0) {
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    log.debug("ChannelSearchManager {}", hit);
                    SearchItemInfo searchItemInfo = IoUtils.parseJson(JSON.toJSONString(hit.getSourceAsMap()), SearchItemInfo.class);
                    searchItemInfo.setScore(hit.getScore());

                    // 按itemId粒度去重
                    if (outItemIds.contains(String.valueOf(searchItemInfo.getItemId()))) {
                        continue;
                    }
                    // 外渠的id是否也需要去重，这个在实时问答调用的时候需要设置为false
                    if (outItemIds.contains(searchItemInfo.getOutItemId()) && duplicate) {
                        continue;
                    }
                    itemInfos.add(searchItemInfo);
                    outItemIds.add(searchItemInfo.getOutItemId());
                    outItemIds.add(String.valueOf(searchItemInfo.getItemId()));
                }
            }
            // 如果没有结果，则调用这个链路
            if (CollectionUtils.isEmpty(itemInfos)) {
                itemInfos.addAll(processWithAppIndex(searchReq));
            }

            itemInfos = itemInfos.stream().limit(searchReq.getTopK()).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("process", e);
        }
        return new SearchResp(itemInfos);
    }

    public SearchResp process(SearchReq searchReq) {
        return process(searchReq, false);
    }

    private List<SearchItemInfo> processWithAppIndex(SearchReq searchReq) {
        QueryBuilder queryBuilder = buildQueryForAppIndex(searchReq);
        List<SearchItemInfo> itemInfos = new ArrayList<>();
        try {
            SearchResponse searchResponse = search(textUrl, appName, itemSearchApolloConfig.getDumpIndexForApp(), queryBuilder, 100);
            if (searchResponse.getHits().getHits().length > 0) {
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    log.debug("ChannelSearchManager {}", hit);
                    SearchItemInfoFromAppIndex searchItemInfo = IoUtils.parseJson(JSON.toJSONString(hit.getSourceAsMap()), SearchItemInfoFromAppIndex.class);
                    searchItemInfo.setScore(hit.getScore());
                    searchItemInfo.setItemNickNameSingle(searchItemInfo.getItemNickNameSingle().replace("\u0002", ""));
                    itemInfos.add(SearchItemInfo.transform(searchItemInfo));
                }
            }
            itemInfos = itemInfos.stream().limit(searchReq.getTopK()).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("process", e);
        }

        return itemInfos;
    }

    private QueryBuilder buildQueryForAppIndex(SearchReq searchReq) {

        BoolQueryBuilder contentQuery = QueryBuilders.boolQuery();
        TextBasicData textBasicData = textPreprocessService.preprocess(searchReq.getQuery(), false);

        BoolQueryBuilder queryBuilder = buildTermMatch(textBasicData.getWords(), searchReq.getQuery());
        contentQuery.must(queryBuilder);
        BoolQueryBuilder statusBuilder = buildOnlineStatusQuery();
        contentQuery.must(queryBuilder).must(statusBuilder);
//        // 主站的搜索不需要限制
//        if (!Objects.equals(searchReq.getChannelId(), "1")){
//            contentQuery.must(statusBuilder);
//        }

        // 如果是数字，则添加一个额外兜底策略
        if (CustomLangUtils.isNumStr(searchReq.getQuery())) {
            BoolQueryBuilder multiQuery = QueryBuilders.boolQuery();

            QueryBuilder itemIdBuilder = QueryBuilders.termQuery("item_id", searchReq.getQuery());
            multiQuery.should(itemIdBuilder);
            multiQuery.should(contentQuery);
            return multiQuery;
        }
        return contentQuery;
    }

    private BoolQueryBuilder buildTermMatch(List<String> entityTerms, String rawQuery) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        BoolQueryBuilder titleBuilder = QueryBuilders.boolQuery();
        for (String term : entityTerms) {
            titleBuilder.must(QueryBuilders.termQuery("invert_field",
                    makeInvertField(CommonIndexField.WORD_SEG.getType(), term)));
        }

        BoolQueryBuilder nickNameBuilder = QueryBuilders.boolQuery();
        for (String term : entityTerms) {
            nickNameBuilder.should(QueryBuilders.termQuery("invert_field",
                    makeInvertField(CommonIndexField.NICKNAME_ATTR.getType(), term)));
        }

        BoolQueryBuilder seoBuilder = QueryBuilders.boolQuery();
        for (String term : entityTerms) {
            seoBuilder.should(QueryBuilders.termQuery("seo_item_name_attr2", term));
        }

        BoolQueryBuilder seoCategoryBuilder = QueryBuilders.boolQuery();
        for (String term : entityTerms) {
            seoCategoryBuilder.should(QueryBuilders.termQuery("seo_category_name_attr", term));
        }

        BoolQueryBuilder saleCategoryNameBuilder = QueryBuilders.boolQuery();
        for (String term : entityTerms) {
            saleCategoryNameBuilder.must(QueryBuilders.termQuery("sale_category_name_attr", term));
        }

        for (String term : entityTerms) {
            String phraseQuery = StringUtils.join(ArrayUtils.toObject(term.toCharArray()), SEP);
            MatchPhraseQueryBuilder singleWordBuilder = QueryBuilders.matchPhraseQuery("item_name_single", phraseQuery).slop(0);
            MatchPhraseQueryBuilder singleNickNameBuilder = QueryBuilders.matchPhraseQuery("item_nickname_single", phraseQuery).slop(0);
            queryBuilder.should(singleNickNameBuilder).should(singleWordBuilder);
        }

        queryBuilder.should(titleBuilder).should(nickNameBuilder)
                .should(seoCategoryBuilder)
                .should(saleCategoryNameBuilder).should(seoBuilder);

        return queryBuilder;
    }

    private BoolQueryBuilder buildOnlineStatusQuery() {
        BoolQueryBuilder cateStatusBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder statusBuilder = QueryBuilders.boolQuery();
        statusBuilder.must(QueryBuilders.termQuery("main_status", "2"));
//        statusBuilder.should(QueryBuilders.termQuery("main_status", "4"));
//        statusBuilder.mustNot(QueryBuilders.termQuery("sale_category1", "0"));
        BoolQueryBuilder cateBuilder = QueryBuilders.boolQuery();
        return cateStatusBuilder.must(statusBuilder).must(cateBuilder);
    }

    private QueryBuilder buildQuery(SearchReq searchReq) {
        BoolQueryBuilder standarQueryBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder fixQueryBuilder = QueryBuilders.boolQuery();
        standarQueryBuilder.must(QueryBuilders.termQuery(KEY_ORG_CHANNEL_ID, searchReq.getChannelId()));
        BoolQueryBuilder contentBuilder = QueryBuilders.boolQuery();
        contentBuilder.should(QueryBuilders.termQuery(KEY_OUT_SKU_ID, searchReq.getQuery()));
        contentBuilder.should(QueryBuilders.termQuery(KEY_OUT_ITEM_ID, searchReq.getQuery()));
        // 外部名称优先
        contentBuilder.should(QueryBuilders.matchQuery(KEY_OUT_ITEM_NAME, searchReq.getQuery()).boost(3));
        contentBuilder.should(QueryBuilders.matchQuery(KEY_ITEM_NICK_NAME, searchReq.getQuery()));
        contentBuilder.should(QueryBuilders.matchQuery(KEY_FRONT_SKU_SPEC_VALUE, searchReq.getQuery()).boost(0.5f));
        standarQueryBuilder.must(contentBuilder);
        fixQueryBuilder.should(standarQueryBuilder);
        // 部分数据， channelId异常，只能通过原始的sku_id 和item_id直接查询
        fixQueryBuilder.should(QueryBuilders.termQuery(KEY_OUT_SKU_ID, searchReq.getQuery()));
        fixQueryBuilder.should(QueryBuilders.termQuery(KEY_OUT_ITEM_ID, searchReq.getQuery()));

        return fixQueryBuilder;
    }
}
