package com.netease.yx.bot.biz.service.sync;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoItemAttrWithId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品属性数据同步
 */
@Service
@Slf4j
public class ItemAttributeSyncService {
    private static final String GET_ITEM_ATTR_QUERY = "/api/knowledge/itemattr/query";

    private static final String SYNC_ATTR = "/sync/attr";

    private static final String ITEM_ID = "itemId";
    private static final String ATTR_ID = "attrId";

    @Value("${remote.kfkm.host}")
    private String remoteKfkmHost;

    @Value("${remote.host.url}")
    private String remoteHostUrl;

    @Autowired
    private EnvService envService;

    public AlgoItemAttrWithId getById(long itemId, long attrId) {
        // itemId=470208152&attrId=470219470
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair(ITEM_ID, String.valueOf(itemId)));
        params.add(new BasicNameValuePair(ATTR_ID, String.valueOf(attrId)));

        try {
            String url = remoteKfkmHost + GET_ITEM_ATTR_QUERY;
            log.info("request {} {}", url, params);
            String resp = HttpUtils.executeGet(url, params);
            log.error("resp {}", resp);
            SucResp<AlgoItemAttrWithId> sucResp = IoUtils.parseJson(resp, new TypeReference<SucResp<AlgoItemAttrWithId>>() {
            });
            if (ObjectUtils.isNotEmpty(sucResp.getData())) {
                return sucResp.getData();
            }
        } catch (Exception e) {
            log.error("getById", e);
        }
        return null;
    }

    public int sync(long itemId, long attrId) {
        if (envService.isRelease()) {
            return 1;
        }
        log.info("ItemAttributeSyncService sync {} {}", itemId, attrId);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair(ITEM_ID, String.valueOf(itemId)));
        params.add(new BasicNameValuePair(ATTR_ID, String.valueOf(attrId)));

        try {
            String url = remoteHostUrl + SYNC_ATTR;
            log.info("request {} {}", url, params);
            String resp = HttpUtils.executeGet(url, params);
            log.info("resp {}", resp);
            SucResp<Integer> sucResp = IoUtils.parseJson(resp, new TypeReference<SucResp<Integer>>() {
            });
            if (ObjectUtils.isNotEmpty(sucResp.getData())) {
                return sucResp.getData();
            }
        } catch (Exception e) {
            log.error("getById", e);
        }
        return 0;
    }
}
