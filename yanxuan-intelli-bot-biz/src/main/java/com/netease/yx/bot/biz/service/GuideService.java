package com.netease.yx.bot.biz.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.netease.mail.yanxuan.supermc.rpc.meta.to.FreeItemsTO;
import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.common.FeatureStoreService;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.constant.Level1IntentType;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.guide.*;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import com.netease.yx.bot.core.model.entity.item.Item;
import com.netease.yx.bot.core.model.entity.rcmd.ItemRcmdReq;
import com.netease.yx.bot.core.model.entity.rcmd.MergeResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@Component
public class GuideService {
    public static final List<GuideRslt> MOCK_GUIDE_MULTI_ITEM_RES = Arrays.stream("468866005,468866006,468866007".split(",")).map(x -> new GuideRslt(Long.parseLong(x))).collect(Collectors.toList());
    public static final Long NULL_RCMD_ITEMID = 1L;
    public static final Long PRO_ZERO_ACTIVITY_RCMD_ITEMID = 2L;
    private static final String DEFAULT_RCMD_TEXT = "小选为您推荐好物【%s】~";
    private static final Random random = new Random();
    private static final String COLON = ":";
    private static final String KEY_GUIDE_ITEM = "guide_item_id";
    private static final String KEY_USER_PRO_ZERO_ACTIVITY = "pro_zero_activity";
    private static final String DEFAULT_RCMD_ITEM_ID = "1113001,1548001,3402020,3829099,3988507";
    private static Integer MAX_RCMD_NUM = 5;
    public final GuideRslt TEST_MOCK_GUIDE_RES = new GuideRslt(468866007L);
    private final GuideRslt EMPTY_GUIDE_RES = new GuideRslt(NULL_RCMD_ITEMID);
    private final GuideRCMDListRslt EMPTY_GUIDE_LIST_RES = new GuideRCMDListRslt(DEFAULT_RCMD_TEXT, new ArrayList<>(Collections.singletonList(EMPTY_GUIDE_RES)));
    private final List<String> DEFAULT_RCMD_TEXT_LIST = new ArrayList<>(Collections.singletonList(DEFAULT_RCMD_TEXT));
    private final List<String> DEFAULT_PRO_ZERO_ACTIVITY_TEXT_LIST = new ArrayList<>(Collections.singletonList("尊贵的pro会员，小选提醒您别忘记领取本月的0元领哦~自营仓商品满49元免邮费哦~"));
    private final List<GuideRslt> DEFAULT_RCMD_ITEM_GUIDERSLT = new ArrayList<>();
    private List<String> robotTextTemplate;
    private List<String> robotActiveTextTemplate;
    private List<String> robotProZeroActivityTextTemplate;
    private List<String> robotRCMDListTextTemplate;
    private Map<String, GuideQARCMD> guideQARCMDMap;

    @Autowired
    private IntentServiceV2 intentServiceV2;

    @Autowired
    private ItemService itemService;

    @Value("${item.rcmd.url}")
    private String itemRcmdUrl;
    @Value("${guide.search.qp}")
    private String qpSearchUrl;
    @Value("${guide.search.url}")
    private String searchUrl;
    @Value("${guide.robot.text}")
    private String robotTextPath;
    @Value("${guide.rcmd.list.robot.text}")
    private String robotRCMDListTextPath;
    @Value("${guide.active.robot.text}")
    private String robotActiveTextPath;
    @Value("${guide.pro.zero.activity.url}")
    private String guideProZeroActivityUrl;
    @Value("${guide.qa.rcmd.json}")
    private String guideQARCMDPath;


    @Autowired
    private FeatureStoreService featureStoreService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private EnvService envService;
    @Autowired
    private UserService userService;
    @Autowired
    private BotApolloConfig botApolloConfig;

    public static String buildGuideItemKey(String userId) {
        return userId + COLON + KEY_GUIDE_ITEM;
    }

    public static String buildGuideUerProZeroActivityKey(String userId) {
        return userId + COLON + KEY_USER_PRO_ZERO_ACTIVITY;
    }

    @PostConstruct
    private void init() {
        try {
            DEFAULT_RCMD_ITEM_GUIDERSLT.addAll(Arrays.stream(DEFAULT_RCMD_ITEM_ID.split(",")).map(x -> new GuideRslt(Long.parseLong(x))).collect(Collectors.toList()));
            robotTextTemplate = IoUtils.getLinesFromFile(robotTextPath);
            robotActiveTextTemplate = IoUtils.getLinesFromFile(robotActiveTextPath);
            robotRCMDListTextTemplate = IoUtils.getLinesFromFile(robotRCMDListTextPath);
            guideQARCMDMap = IoUtils.loadJsonFromFile(guideQARCMDPath, new TypeReference<Map<String, GuideQARCMD>>() {
            });

            log.debug("GuideManagerV2.init  MAX_RCMD_NUM, {}", botApolloConfig.getGuideRcmdItemMax());
        } catch (Exception e) {
            log.error("load GuideManagerV2 error", e);
            robotTextTemplate = DEFAULT_RCMD_TEXT_LIST;
            robotActiveTextTemplate = DEFAULT_RCMD_TEXT_LIST;
            robotProZeroActivityTextTemplate = DEFAULT_PRO_ZERO_ACTIVITY_TEXT_LIST;
            robotRCMDListTextTemplate = new ArrayList<>(Collections.singletonList("小选为您推荐好物~"));
        }
    }

    public GuideRCMDListRslt getResService(GuideReq guideReq) {
        IntentRslt intentResp = intentServiceV2.getIntentV2(guideReq.getQuery());
        if (!Level1IntentType.GUIDE.equals(intentResp.getFirstIntent())) {

            return EMPTY_GUIDE_LIST_RES;
        }

        return getPassiveRes(guideReq);

    }

    public GuideRCMDListRslt getPassiveRes(GuideReq guideReq) {
        String query = guideReq.getQuery();
        String uid = guideReq.getUid();

        log.info("input guide query: " + query);

        try {
            //QP解析获得商品词
            QPReq qpReq = new QPReq(query, uid, false, "1.0");
            QPResp qpResp = getQPRes(qpReq);

            if (qpResp.getNerRslt().getEntity().size() == 0) {
                return EMPTY_GUIDE_LIST_RES;
            }

            String newQuery = qpResp.getNerRslt().getEntity().get(0);
            if (qpResp.getNerRslt().getAttrValue().size() > 0) {
                newQuery = qpResp.getNerRslt().getAttrValue().get(0) + newQuery;
            }

            log.info("new query: " + newQuery);

            //搜索推荐接口
            SearchReq searchReq = new SearchReq(newQuery, uid);
            SearchResponse searchResponse = getSearchRes(searchReq);

            // 若为空
            if (CollectionUtils.isEmpty(searchResponse.getItemIdList())) {
                return EMPTY_GUIDE_LIST_RES;
            }
            // 若为空
            List<String> searchItemIds = searchResponse.getItemIdList();
            if (CollectionUtils.isEmpty(searchItemIds)) {
                return EMPTY_GUIDE_LIST_RES;
            }

            List<String> itemIdShowList = getItemIdShowList(guideReq.getUid());
            List<GuideRslt> rcmdList = new ArrayList<>();

            //获得商品itemName，重组robot话术
            for (String searchItemId : searchItemIds) {
                RcmdItemInfo rcmdItemInfo = getItem(searchItemId);

                // 1. 不为空 2.未售罄
                if ((rcmdItemInfo != null) && (rcmdItemInfo.getItem() != null) && (!rcmdItemInfo.getItem().isSoldOut())
                        && (!itemIdShowList.contains(String.valueOf(rcmdItemInfo.getItem().getItemId())))) {
                    List<String> rmcdText = new ArrayList<>(Collections.singletonList(genRobotText(rcmdItemInfo.getItem().getItemName(), robotTextTemplate)));
                    GuideRslt guideRslt = new GuideRslt(rcmdItemInfo.getItem().getItemId());
                    guideRslt.genRcmdReason(rcmdItemInfo.getItem().getItemName(), rmcdText);
                    rcmdList.add(guideRslt);
                }
            }

            return resetRCMDList(rcmdList, guideReq.getUid());

        } catch (Exception e) {
            log.error("GuideManagerV2 getPassiveRes error.", e);
        }
        return EMPTY_GUIDE_LIST_RES;
    }

    private GuideRCMDListRslt resetRCMDList(List<GuideRslt> guideRsltList, String uid) {
        MAX_RCMD_NUM = botApolloConfig.getGuideRcmdItemMax();

        // 对于推荐的前K个不重复推荐
        if (guideRsltList.size() == 0) {
            return EMPTY_GUIDE_LIST_RES;
        }

        if (guideRsltList.size() < MAX_RCMD_NUM) {
            guideRsltList.addAll(DEFAULT_RCMD_ITEM_GUIDERSLT);
        }

        for (int i = 0; i < MAX_RCMD_NUM; i++) {
            uploadItemIdShowList(uid, String.valueOf(guideRsltList.get(i).getItemId()));
        }

        String rcmdReason = genRCMDListRobotText(robotRCMDListTextTemplate);

        return new GuideRCMDListRslt(rcmdReason, guideRsltList.subList(0, MAX_RCMD_NUM));
    }

    private List<String> getItemIdShowList(String userId) {
        List<String> itemIdShowList = new ArrayList<>();
        if (userId != null) {
            String itemIdKey = buildGuideItemKey(userId);
            String itemIdValue = stringRedisTemplate.opsForValue().get(itemIdKey);
//            log.info("getItemIdShowList userid: {}  {}  {}", userId, itemIdValue, itemIdKey);
            if (itemIdValue != null) {
                itemIdShowList.addAll(Arrays.asList(itemIdValue.split(",")));
            }
        }
        return itemIdShowList;
    }

    public void uploadItemIdShowList(String userId, String itemId) {
        try {
            if (!checkItemIdValid(itemId)) {
                return;
            }
            List<String> itemIdShowList = getItemIdShowList(userId);
            itemIdShowList.add(itemId);
            String uploadContent = String.join(",", itemIdShowList);
            String itemIdKey = buildGuideItemKey(userId);
//            log.info("uploadItemIdShowList userid: {}  {}  {}", userId, uploadContent, itemIdKey);
            stringRedisTemplate.opsForValue().set(itemIdKey, uploadContent, 7, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("uploadItemIdShowList error.", e);
        }

    }

    public GuideRCMDListRslt getActiveRes(GuideReq guideReq) {

        if (envService.isTest()) {
            return new GuideRCMDListRslt(DEFAULT_RCMD_TEXT, new ArrayList<>(Collections.singletonList(TEST_MOCK_GUIDE_RES)));
        }

        try {
            List<String> itemIds = getRCMD(guideReq.getUid(), guideReq.getItemId());
            if (itemIds == null) {
                return EMPTY_GUIDE_LIST_RES;
            }

            // 如果长度为空，则重新调用，走无itemid接口
            if (itemIds.size() == 0) {
                itemIds = getRCMD(guideReq.getUid(), null);
                if (itemIds == null) {
                    return EMPTY_GUIDE_LIST_RES;
                }
            }

            List<String> itemIdShowList = getItemIdShowList(guideReq.getUid());
            List<GuideRslt> rcmdList = new ArrayList<>();
            //获得商品itemName，重组robot话术
            for (String itemId : itemIds) {
                RcmdItemInfo rcmdItemInfo = getItem(itemId);
                // 1. 商品在一段时间内未曾推荐  2. 禁止部分商品推荐
                if ((rcmdItemInfo != null)
                        && (rcmdItemInfo.getItem() != null)
                        && (!rcmdItemInfo.getItem().isSoldOut())
                        && (!itemIdShowList.contains(String.valueOf(rcmdItemInfo.getItem().getItemId())))
                        && (rcmdItemInfo.getItem().getItemId() != 3995885)) {
                    List<String> rmcdText = new ArrayList<>(Collections.singletonList(genRobotText(rcmdItemInfo.getItem().getItemName(),
                            robotActiveTextTemplate)));
                    GuideRslt guideRslt = new GuideRslt(rcmdItemInfo.getItem().getItemId());
                    guideRslt.genRcmdReason(rcmdItemInfo.getItem().getItemName(), rmcdText);
                    rcmdList.add(guideRslt);
                }
            }

            return resetRCMDList(rcmdList, guideReq.getUid());
        } catch (Exception e) {
            log.error("GuideManagerV2 getActiveRes error.", e);
        }

        return EMPTY_GUIDE_LIST_RES;
    }

    /**
     * 查看itemid 是否合法
     */
    public boolean checkItemIdValid(String itemId) {
        if (itemId == null) {
            return false;
        }
        if ("0".equals(itemId)) {
            return false;
        }
        if ("1".equals(itemId)) {
            return false;
        }

        try {
            Long itemIdN = Long.valueOf(itemId);
            Item item = itemService.getItemById(itemIdN);
            if (item == null) {
                return false;
            }

        } catch (Exception e) {
            return false;
        }

        return true;
    }


    /**
     * 获取一级意图结果
     */
    public IntentRslt getIntentRes(String query) {
        return intentServiceV2.getIntentV2(query);
    }

    /**
     * 重组机器人话术
     */
    private String genRobotText(String itemName, List<String> textTemplate) {
        int randomIdx = random.nextInt(textTemplate.size());
        String textTemp = textTemplate.get(randomIdx);
        return String.format(textTemp, itemName);
    }


    /**
     * 获取搜索接口结果
     */
    public SearchResponse getSearchRes(SearchReq searchReq) {
        SearchResponse searchResponse = null;

        try {
            String response = HttpUtils.executePost(searchUrl, IoUtils.toJsonString(searchReq), "");
            searchResponse = IoUtils.parseJson(response, SearchResponse.class);

        } catch (Exception e) {
            log.warn("GuideManagerV2-getSearchRes error:" + e);
        }

        return searchResponse;
    }

    public List<String> getRCMD(String uid, String recallParam) {
        List<String> result = null;
        String httpRes = null;
        try {
            ItemRcmdReq itemRcmdReq = null;

            if (checkItemIdValid(recallParam)) {
                itemRcmdReq = new ItemRcmdReq(uid, false, recallParam);
            } else {
                itemRcmdReq = new ItemRcmdReq(uid, false);
            }

            httpRes = HttpUtils.executePost(itemRcmdUrl, IoUtils.toJsonString(itemRcmdReq), null);
            SucResp<List<MergeResultDTO>> resp = IoUtils.parseJson(httpRes, new TypeReference<SucResp<List<MergeResultDTO>>>() {
            });
            if (HttpStatus.OK.value() == resp.getCode()) {
                List<MergeResultDTO> mergeResultDTOS = resp.getData();

                if (!CollectionUtils.isEmpty(mergeResultDTOS)) {
                    return mergeResultDTOS.stream().map(MergeResultDTO::getItemId).filter(Objects::nonNull).collect(Collectors.toList());
                }
            }

        } catch (Exception e) {
            log.error("GuideManagerV2-getRCMD error", e);
        }
        log.info("rcmd response: " + httpRes);

        return result;
    }

    /**
     * 获得item信息
     */
    public RcmdItemInfo getItem(String itemId) {
        Item item = itemService.getItemById(Long.parseLong(itemId));
        String rcmdReason = null;
        try {
            rcmdReason = itemService.getADItemName(Long.parseLong(itemId));
        } catch (Exception e) {
            log.warn("rcmd reason error", e);
        }

        RcmdItemInfo rcmdItemInfo = new RcmdItemInfo();
        rcmdItemInfo.setItem(item);
        rcmdItemInfo.setRcmdReason(rcmdReason);
        return rcmdItemInfo;
    }

    /**
     * 获得qp结果
     */
    public QPResp getQPRes(QPReq qpReq) {
        String res = "";
        QPResp qpResp = null;
        try {
            res = HttpUtils.executePost(qpSearchUrl, IoUtils.toJsonString(qpReq), "");
            SucResp<QPResp> resp = IoUtils.parseJson(res, new TypeReference<SucResp<QPResp>>() {
            });
            qpResp = resp.getData();
        } catch (Exception e) {
            log.error("GuideManagerV2-qpService error ", e);
        }
        log.info("qp response: " + IoUtils.toJsonString(qpResp));

        return qpResp;
    }


    /**
     * 0元购推荐
     */
    public GuideRCMDListRslt getProZeroActivity(BotContext botContext) {

        String userIdProZeroActivityKey = buildGuideUerProZeroActivityKey(String.valueOf(botContext.getUserId()));
        String proZeroActivityValid = stringRedisTemplate.opsForValue().get(userIdProZeroActivityKey);

        GuideRCMDListRslt guideRslt = EMPTY_GUIDE_LIST_RES;

        try {
            // 获取actId
            FreeItemsTO freeItemsTO = userService.getFreeItemActIds();

            // 1. pro会员   3. 当天3天内未提醒
            if ((botApolloConfig.checkProZeroActivityIntervalEnable() && proZeroActivityValid != null) ||
                    !userService.checkUserPro(botContext.getUserId())) {
                return EMPTY_GUIDE_LIST_RES;
            }

            // 2. 当月未领取0元购
            if (freeItemsTO != null && freeItemsTO.getActId() != null) {
                Long itemId = userService.getFreeItemIds(freeItemsTO.getActId(), botContext.getUserId());
                log.debug("getProZeroActivity from rpc api. {} {} {}", freeItemsTO.getActId(), botContext.getUserId(), itemId);
                if (itemId == null) {
                    return EMPTY_GUIDE_LIST_RES;
                }
                guideRslt = buildProZeroActivity(itemId);
            } else {
                // 检查是否领取
                if (!userService.checkProZeroActivity(botContext.getUserId())) {
                    guideRslt = buildProZeroActivity();

                }
            }
        } catch (Exception e) {
            log.error("getProZeroActivity error.", e);
            return EMPTY_GUIDE_LIST_RES;
        }

        if (botApolloConfig.checkProZeroActivityIntervalEnable()) {
            stringRedisTemplate.opsForValue().set(userIdProZeroActivityKey, "1", botApolloConfig.getProZeroActivityIntervalDays(), TimeUnit.DAYS);
        }
        log.info("getProZeroActivity result {} {}", botContext.getUserId(), guideRslt);
        return guideRslt;

    }

    public GuideRCMDListRslt buildProZeroActivity() {
        FreeItemsTO freeItemsTO = userService.getFreeItemActIds();
        log.debug("getProZeroActivity: {}", IoUtils.toJsonString(freeItemsTO));
        Long itemId = userService.getFreeItemIds(freeItemsTO.getActId(), 40030983071L);
        log.debug("getProZeroActivity: {}, {}", freeItemsTO.getActId(), itemId);
        FreeItemsTO freeItemsTOTmp = userService.getFreeTryRecords(40030983071L);
        log.debug("getProZeroActivity by user, {}", IoUtils.toJsonString(freeItemsTOTmp));

        robotProZeroActivityTextTemplate = botApolloConfig.getProZeroActivityRcmdText();
        GuideRslt guideRslt = new GuideRslt(PRO_ZERO_ACTIVITY_RCMD_ITEMID);

        guideRslt.setUrl(guideProZeroActivityUrl);
        String rcmdReason = genProZeroActivityRobotText(robotProZeroActivityTextTemplate);
        guideRslt.setRcmdReason(new ArrayList<>(Collections.singletonList(rcmdReason)));
        guideRslt.setNote("￥0");
        guideRslt.setTitle("Pro会员0元领");
        guideRslt.setDesc("0元好物 0元随单带走");
        guideRslt.setItemNickName("");
        guideRslt.setPicture("https://yanxuan-item.nosdn.127.net/c5ab0c4b494d544361d375db266d7076.png");
        return new GuideRCMDListRslt(rcmdReason, new ArrayList<>(Collections.singletonList(guideRslt)));
    }

    public GuideRCMDListRslt buildProZeroActivity(Long itemId) {

        robotProZeroActivityTextTemplate = botApolloConfig.getProZeroActivityRcmdText();
        GuideRslt guideRslt = new GuideRslt(PRO_ZERO_ACTIVITY_RCMD_ITEMID);

        guideRslt.setUrl(guideProZeroActivityUrl);
        String rcmdReason = genProZeroActivityRobotText(robotProZeroActivityTextTemplate);
        guideRslt.setRcmdReason(new ArrayList<>(Collections.singletonList(rcmdReason)));
        guideRslt.setNote("￥0");

        Item item = itemService.getItemById(itemId);
        guideRslt.setTitle("Pro会员0元领");
        guideRslt.setDesc("0元好物 0元随单带走");
        guideRslt.setPicture(item.getPicUrl());
        guideRslt.setItemNickName("");

        return new GuideRCMDListRslt(rcmdReason, new ArrayList<>(Collections.singletonList(guideRslt)));
    }


    /**
     * 随机抽取0元购话术
     */
    private String genProZeroActivityRobotText(List<String> textTemplate) {
        int randomIdx = random.nextInt(textTemplate.size());
        return textTemplate.get(randomIdx);
    }

    /**
     * 随机抽取多商品推荐话术
     */
    private String genRCMDListRobotText(List<String> textTemplate) {
        int randomIdx = random.nextInt(textTemplate.size());
        return textTemplate.get(randomIdx);
    }
}