package com.netease.yx.bot.biz.service.qa.recall;

import com.alibaba.fastjson.JSONObject;
import com.netease.yx.bot.biz.common.DictService;
import com.netease.yx.bot.biz.service.ItemService;
import com.netease.yx.bot.biz.common.NlpServerService;
import com.netease.yx.bot.biz.common.RedisService;
import com.netease.yx.bot.biz.common.es8.Es8Service;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.biz.service.qa.rank.RankService;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.constant.RedisKey;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.KnowledgeMatchRslt;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoAnswer;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoKnowledge;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoSimilarQuestion;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import essearch.shaded.co.elasticsearch.clients.elasticsearch._types.SortOrder;
import essearch.shaded.co.elasticsearch.clients.elasticsearch._types.query_dsl.BoolQuery;
import essearch.shaded.co.elasticsearch.clients.elasticsearch._types.query_dsl.NestedQuery;
import essearch.shaded.co.elasticsearch.clients.elasticsearch._types.query_dsl.QueryBuilders;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.SearchRequest;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.SearchResponse;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.search.Hit;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.search.InnerHits;
import essearch.shaded.co.elasticsearch.clients.elasticsearch.core.search.SourceFilter;
import essearch.shaded.co.elasticsearch.clients.json.JsonData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RecallServiceWithEs8 {
    private static final String VALUE_EMPTY = "empty";
    private static final int MAX_LEVEL = 4;
    private static final float ITEM_ID_BOOST = 1.5f;

    private static List<String> DOC_FIELDS = new ArrayList<>();

    static {
        DOC_FIELDS.add(AlgoKnowledge.CLEAN_STD_QUESTION);
        DOC_FIELDS.add(AlgoKnowledge.KNOWLEDGE_ID);
        DOC_FIELDS.add(AlgoKnowledge.UN_SIMILAR_QUESTIONS);
    }

    @Autowired
    private DictService dictService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private Es8Service es8Service;

    @Autowired
    private NlpServerService nlpServerService;

    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;

    @Autowired
    private BotApolloConfig botApolloConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RankService rankService;

    public List<KnowledgeMatchRslt> matchWithContextForMultiChannelV2(BotContext botContext) {
        TextBasicData textBasicData = botContext.getInputs().get(0);
        if (StringUtils.isEmpty(textBasicData.getCleanedText())) {
            return new ArrayList();
        }
        SearchRequest searchRequest = buildEsQuery(botContext, textBasicData);
        List<KnowledgeMatchRslt> matchRslts = new ArrayList<>();

        try {
            SearchResponse<JSONObject> response = es8Service.getElasticsearchClient().search(searchRequest, JSONObject.class);

            for (Hit<JSONObject> hit : response.hits().hits()) {
                AlgoKnowledge algoKnowledge = IoUtils.parseJson(hit.source().toJSONString(), AlgoKnowledge.class);
                log.info(hit.source().toJSONString());

                List<AlgoSimilarQuestion> similarQuestions = new ArrayList<>();
                if (hit.innerHits().get(AlgoKnowledge.SIMILAR_QUESTIONS).hits().hits().size() > 0) {
                    for (int i = 0; i < hit.innerHits().get(AlgoKnowledge.SIMILAR_QUESTIONS).hits().hits().size(); i++) {
                        AlgoSimilarQuestion algoSimilarQuestion = Objects.requireNonNull(hit.innerHits().get(AlgoKnowledge.SIMILAR_QUESTIONS).hits().hits().get(i).source()).to(AlgoSimilarQuestion.class);
                        similarQuestions.add(algoSimilarQuestion);
                    }
                }

                algoKnowledge.setSimilarQuestions(similarQuestions);

                List<AlgoAnswer> algoAnswers = new ArrayList<>();
                if (hit.innerHits().get(AlgoKnowledge.ANSWERS).hits().hits().size() > 0) {
                    for (int i = 0; i < hit.innerHits().get(AlgoKnowledge.ANSWERS).hits().hits().size(); i++) {

                        AlgoAnswer algoAnswer = Objects.requireNonNull(hit.innerHits().get(AlgoKnowledge.ANSWERS).hits().hits().get(i).source()).to(AlgoAnswer.class);
                        algoAnswers.add(algoAnswer);
                    }
                }
                // 答案的排序
                Collections.sort(algoAnswers);
                algoKnowledge.setAnswers(algoAnswers);

                AlgoAnswer algoAnswer = algoAnswers.get(0);
                if (StringUtils.isNotEmpty(algoAnswer.getItemInfoLabel())) {
                    matchRslts.add(new KnowledgeMatchRslt(algoKnowledge.getKnowledgeId(), algoKnowledge.getCleanStdQuestion(), -1, StringUtils.join(AlgoKnowledge.CORPUS_ID_PREFFIX_SIMILAR, algoKnowledge.getKnowledgeId()), botContext.getItemId(), algoAnswer.getAnswerId(), algoAnswer.getAnswerUse()));
                } else {
                    matchRslts.add(new KnowledgeMatchRslt(algoKnowledge.getKnowledgeId(), algoKnowledge.getCleanStdQuestion(), -1, StringUtils.join(AlgoKnowledge.CORPUS_ID_PREFFIX_SIMILAR, algoKnowledge.getKnowledgeId()), 0, algoAnswer.getAnswerId(), algoAnswer.getAnswerUse()));
                }
                for (AlgoSimilarQuestion algoSimilarQuestion : similarQuestions) {
                    if (StringUtils.isNotEmpty(algoSimilarQuestion.getCleanContent())) {
                        if (StringUtils.isNotEmpty(algoAnswer.getItemInfoLabel())) {
                            matchRslts.add(new KnowledgeMatchRslt(algoKnowledge.getKnowledgeId(), algoSimilarQuestion.getCleanContent(), -1, StringUtils.join(AlgoKnowledge.CORPUS_ID_PREFFIX_SIMILAR, algoKnowledge.getKnowledgeId(), "-", algoSimilarQuestion.getId()), botContext.getItemId(), algoAnswer.getAnswerId(), algoAnswer.getAnswerUse()));
                        } else {
                            matchRslts.add(new KnowledgeMatchRslt(algoKnowledge.getKnowledgeId(), algoSimilarQuestion.getCleanContent(), -1, StringUtils.join(AlgoKnowledge.CORPUS_ID_PREFFIX_SIMILAR, algoKnowledge.getKnowledgeId(), "-", algoSimilarQuestion.getId()), 0, algoAnswer.getAnswerId(), algoAnswer.getAnswerUse()));
                        }
                        break;
                    }
                }
            }

        } catch (Exception e) {
            log.error("matchWithContextForMultiChannelForTest", e);
        }

        return matchRslts;
    }

    private SearchRequest buildEsQuery(BotContext botContext, TextBasicData textBasicData) {
        List<String> filteredWords = textBasicData.getWords().stream().filter(x -> !dictService.isStopWord(x)).collect(Collectors.toList());

        // 标准问
        // 文本分词后的匹配字段

        BoolQuery stdQuestionQuery = QueryBuilders.bool().should(x -> x.match(y -> y.field(AlgoKnowledge.CLEAN_STD_QUESTION).query(textBasicData.getCleanedText())))
                .should(x -> x.match(y -> y.field(AlgoKnowledge.CLEAN_STD_QUESTION_TERMS).query(StringUtils.join(filteredWords, StringUtils.SPACE)))).build();

        BoolQuery similarQuestionQuery = QueryBuilders.bool().should(x -> x.match(y -> y.field(AlgoKnowledge.SIMILAR_QUESTIONS_CLEAN_CONTENT).query(textBasicData.getCleanedText())))
                .should(x -> x.match(y -> y.field(AlgoKnowledge.SIMILAR_QUESTIONS_CLEAN_CONTENT_TERMS).query(StringUtils.join(filteredWords, StringUtils.SPACE)))).build();

        NestedQuery similarQuestionNestQuery = QueryBuilders.nested().path(AlgoKnowledge.SIMILAR_QUESTIONS).query(similarQuestionQuery._toQuery()).innerHits(x -> new InnerHits.Builder().sort(z -> z.score(m -> m.order(SortOrder.Desc)))).build();

        BoolQuery contentMatchQuery = QueryBuilders.bool().should(stdQuestionQuery._toQuery()).should(similarQuestionNestQuery._toQuery()).build();

        BoolQuery.Builder answerBoolQueryBuilder = QueryBuilders.bool();

        List<Long> itemIds = botContext.getCardItemIds();
        if (CollectionUtils.isEmpty(itemIds) && botContext.getItemId() != 0) {
            // 为空，则添加默认的itemId;
            itemIds = new ArrayList<>();
            itemIds.add(botContext.getItemId());
        }
        if (CollectionUtils.isNotEmpty(itemIds)) {

            BoolQuery.Builder answerScopeBoolQueryBuilder = QueryBuilders.bool();

            for (int index = 0; index < itemIds.size(); index++) {
                long itemId = itemIds.get(index);

                // 如果 有itemId，则按 itemId，4级id，3级id，2级id，1级id，通用来给予一定的权重匹配
                if (itemId != 0) {
                    answerScopeBoolQueryBuilder.should(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_ITEM_INFO_LABEL).query(String.valueOf(itemId))));
                    // 最后一个商品会额外附加类目筛选
                    if (index == itemIds.size() - 1) {
                        List<String> phyIds = itemService.phyCateIds(itemId);

                        if (CollectionUtils.size(phyIds) == MAX_LEVEL) {
                            for (int i = 0; i < MAX_LEVEL; i++) {
                                List<String> subPhyIds = phyIds.subList(0, MAX_LEVEL - i);
                                String termMatchIdStr = String.join(StringUtils.SPACE, subPhyIds);

                                answerScopeBoolQueryBuilder.should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_ITEM_CATE_LABEL_STR_KEYWORD).value(termMatchIdStr)));
                            }
                        }
                    }
                }
            }
            if (itemIds.size() > 1) {
                log.info("multiItem search {} {}", itemIds, botContext.getItemId());
            }

            BoolQuery.Builder answerCommonBoolQueryBuilder = QueryBuilders.bool().must(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_ITEM_INFO_LABEL_KEYWORD).query(VALUE_EMPTY))).mustNot(x -> x.exists(y -> y.field(AlgoKnowledge.ANSWER_ITEM_CATE_LABEL)));

            answerScopeBoolQueryBuilder.should(answerCommonBoolQueryBuilder.build()._toQuery());

            answerBoolQueryBuilder.must(answerScopeBoolQueryBuilder.build()._toQuery());
        } else {
            // 不带卡片，目前的策略是所有商品的都不匹配
            BoolQuery.Builder answerCommonBoolQueryBuilder = QueryBuilders.bool().must(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_ITEM_INFO_LABEL_KEYWORD).query(VALUE_EMPTY))).mustNot(x -> x.exists(y -> y.field(AlgoKnowledge.ANSWER_ITEM_CATE_LABEL)));

            answerBoolQueryBuilder.must(answerCommonBoolQueryBuilder.build()._toQuery());
        }

        BoolQuery.Builder channelScopeBoolQueryBuilder = QueryBuilders.bool();

        long channel = botContext.getChannel();
        if (channel > 0) {
            channelScopeBoolQueryBuilder.should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_CHANNEL).value(channel)));
        }
        String platform = botContext.getPlatform();
        if (StringUtils.isNotEmpty(platform)) {
            channelScopeBoolQueryBuilder.should(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_PLATFORM).query(platform)));
        }
        answerBoolQueryBuilder.must(channelScopeBoolQueryBuilder.build()._toQuery());

        // 生效时间的处理
        BoolQuery.Builder answerTimeQuery = QueryBuilders.bool();
        // 通用
        answerTimeQuery.should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_EXPIRY_TIME).value(0)));

        answerTimeQuery.should(x -> x.bool(
                y -> y.must(z -> z.range(z1 -> z1.field(AlgoKnowledge.ANSWER_EXPIRY_TIME).gte(JsonData.of(botContext.getConsultTime()))))
                        .must(z -> z.range(z1 -> z1.field(AlgoKnowledge.ANSWER_EFFECTIVE_TIME).lte(JsonData.of(botContext.getConsultTime()))))
        ));

        answerBoolQueryBuilder.must(answerTimeQuery.build()._toQuery());

        int sessionInteraction = botContext.getSessionInteraction();

//        if (sessionInteraction == BotContext.SESSION_INTERACTION_ROBOT) {
//            // 主站机器人不能直接出 智能解决方案类型的答案
//            answerBoolQueryBuilder.mustNot(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_TYPE).value(AlgoAnswer.ANSWER_TYPE_INTELLI_SOLUTION)));
//        }

        if (sessionInteraction == BotContext.SESSION_INTERACTION_HUMAN){
            answerBoolQueryBuilder.mustNot(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_TYPE).value(AlgoAnswer.ANSWER_TYPE_SOP_FLOW)));
        }

        // 相似问校验，不区分用途
        if ( sessionInteraction != BotContext.SESSION_INTERACTION_CHECK){
            BoolQuery.Builder answerUseQuery = QueryBuilders.bool().should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_USE).value(sessionInteraction == BotContext.SESSION_INTERACTION_ROBOT ? AlgoAnswer.ANSWER_USE_OUT : AlgoAnswer.ANSWER_USE_INTERNAL)));
            answerUseQuery.should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_USE).value(AlgoAnswer.ANSWER_USE_INTERNAL_AND_OUT)));
            answerBoolQueryBuilder.must(answerUseQuery.build()._toQuery());
        }

        NestedQuery answerNestQuery = QueryBuilders.nested().path(AlgoKnowledge.ANSWERS).query(answerBoolQueryBuilder.build()._toQuery()).innerHits(x -> new InnerHits.Builder()).build();

        // 生效时间的处理
        BoolQuery.Builder timeQuery = QueryBuilders.bool();
        // 通用
        timeQuery.should(x -> x.term(y -> y.field(AlgoKnowledge.EXPIRY_TIME).value(0)));

        timeQuery.should(x -> x.bool(
                y -> y.must(z -> z.range(z1 -> z1.field(AlgoKnowledge.EXPIRY_TIME).gte(JsonData.of(botContext.getConsultTime()))))
                        .must(z -> z.range(z1 -> z1.field(AlgoKnowledge.EFFECTIVE_TIME).lte(JsonData.of(botContext.getConsultTime()))))
        ));

        BoolQuery.Builder statusQuery = QueryBuilders.bool();
        statusQuery.should(x -> x.term(y -> y.field(AlgoKnowledge.STATUS).value(4)));

        // 测试窗里才会出还未生效的知识
        if (botContext.isTestMode()) {
            statusQuery.should(x -> x.term(y -> y.field(AlgoKnowledge.STATUS).value(3)));
        }

        BoolQuery.Builder finalQuery = QueryBuilders.bool()
                .must(contentMatchQuery._toQuery())
                .must(answerNestQuery._toQuery())
                .must(statusQuery.build()._toQuery())
                .must(timeQuery.build()._toQuery())
                .must(x -> x.term(y -> y.field(AlgoKnowledge.KNOWLEDGE_TYPE).value(1)));

        SearchRequest.Builder requestBuilder = new SearchRequest.Builder();
        requestBuilder.index(channelQaApolloConfig.getKnowledgeEsIndexWithAnswerUse());
        requestBuilder.source(x -> x.filter(SourceFilter.of(y -> y.includes(DOC_FIELDS))));

        requestBuilder.query(
                finalQuery.build()._toQuery()
        );
        requestBuilder.size(20);
        SearchRequest searchRequest = requestBuilder.build();
        log.info("getSimilarQuestionTextSearchRequest {}", searchRequest.toString());
        return searchRequest;
    }

    // 是否是包含了解决方案答案类型的知识，这种情况下不能在机器人里面展示
    // 输入knowledgeId，如果符合要求就输出true，不符合就输出false
    public boolean hasIntelliSolutionAnswer(long knowledgeId) {
        String cacheKey = StringUtils.join(RedisKey.KNOWLEDGE_ID_2_SOLUTION_ANSWER, knowledgeId);
        if (StringUtils.equals(redisService.get(cacheKey), "1")) {
            return true;
        }
        BoolQuery.Builder answerCommonBoolQueryBuilder = QueryBuilders.bool().must(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_TYPE).value(AlgoAnswer.ANSWER_TYPE_INTELLI_SOLUTION)));
        NestedQuery answerNestQuery = QueryBuilders.nested().path(AlgoKnowledge.ANSWERS).query(answerCommonBoolQueryBuilder.build()._toQuery()).innerHits(x -> new InnerHits.Builder()).build();

        BoolQuery.Builder finalQuery = QueryBuilders.bool()
                .must(answerNestQuery._toQuery())
                .must(x -> x.term(y -> y.field(AlgoKnowledge.KNOWLEDGE_ID).value(knowledgeId)));

        SearchRequest.Builder requestBuilder = new SearchRequest.Builder();
        requestBuilder.index(channelQaApolloConfig.getKnowledgeEsIndex());
        requestBuilder.source(x -> x.filter(SourceFilter.of(y -> y.includes(DOC_FIELDS))));

        requestBuilder.query(
                finalQuery.build()._toQuery()
        );
        requestBuilder.size(1);
        SearchRequest searchRequest = requestBuilder.build();
        log.info("getCommonAnswerIdV2 {}", searchRequest.toString());

        try {
            SearchResponse<JSONObject> response = es8Service.getElasticsearchClient().search(searchRequest, JSONObject.class);
            if (response.hits().hits().size() > 0) {
                redisService.set(cacheKey, "1", 30);
                return true;
            }
        } catch (Exception e) {
            log.error("checkIntelliSolution", e);
        }
        return false;
    }

    // 是否包含对外有效的答案
    public boolean hasRobotAnswer(long knowledgeId){
        String cacheKey = StringUtils.join(RedisKey.KNOWLEDGE_ID_2_OUT_ANSWER, knowledgeId);
        if (StringUtils.equals(redisService.get(cacheKey), "1")) {
            return true;
        }

        BoolQuery.Builder answerCommonBoolQueryBuilder = QueryBuilders.bool().should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_USE).value(AlgoAnswer.ANSWER_USE_OUT))).should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_USE).value(AlgoAnswer.ANSWER_USE_INTERNAL_AND_OUT)));
        NestedQuery answerNestQuery = QueryBuilders.nested().path(AlgoKnowledge.ANSWERS).query(answerCommonBoolQueryBuilder.build()._toQuery()).innerHits(x -> new InnerHits.Builder()).build();

        BoolQuery.Builder finalQuery = QueryBuilders.bool()
                .must(answerNestQuery._toQuery())
                .must(x -> x.term(y -> y.field(AlgoKnowledge.KNOWLEDGE_ID).value(knowledgeId)))
                .must(x -> x.term(y -> y.field(AlgoKnowledge.STATUS).value(4)));

        SearchRequest.Builder requestBuilder = new SearchRequest.Builder();
        requestBuilder.index(channelQaApolloConfig.getKnowledgeEsIndex());
        requestBuilder.source(x -> x.filter(SourceFilter.of(y -> y.includes(DOC_FIELDS))));

        requestBuilder.query(
                finalQuery.build()._toQuery()
        );
        requestBuilder.size(1);
        SearchRequest searchRequest = requestBuilder.build();
        log.info("getCommonAnswerIdV2 {}", searchRequest.toString());

        try {
            SearchResponse<JSONObject> response = es8Service.getElasticsearchClient().search(searchRequest, JSONObject.class);
            if (response.hits().hits().size() > 0) {
                redisService.set(cacheKey, "1", 1);
                return true;
            }
        } catch (Exception e) {
            log.error("checkIntelliSolution", e);
        }
        return false;
    }

    public List<Long> getAnswerIdByKnowledgeId(long knowledgeId, long channelId, String platform, long itemId) {
        BoolQuery.Builder answerBoolQueryBuilder = QueryBuilders.bool();

        if (itemId != 0) {
            answerBoolQueryBuilder.must(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_ITEM_INFO_LABEL).query(String.valueOf(itemId)).boost(ITEM_ID_BOOST)));
        } else {
            BoolQuery.Builder answerCommonBoolQueryBuilder = QueryBuilders.bool().must(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_ITEM_INFO_LABEL_KEYWORD).query(VALUE_EMPTY))).mustNot(x -> x.exists(y -> y.field(AlgoKnowledge.ANSWER_ITEM_CATE_LABEL)));

            answerBoolQueryBuilder.must(answerCommonBoolQueryBuilder.build()._toQuery());
        }

        BoolQuery.Builder channelScopeBoolQueryBuilder = QueryBuilders.bool();
        if (channelId != 0) {
            channelScopeBoolQueryBuilder.should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_CHANNEL).value(channelId)));
        }
        if (StringUtils.isNotEmpty(platform)) {
            channelScopeBoolQueryBuilder.should(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_PLATFORM).query(platform)));
        }

        answerBoolQueryBuilder.must(channelScopeBoolQueryBuilder.build()._toQuery());

        NestedQuery answerNestQuery = QueryBuilders.nested().path(AlgoKnowledge.ANSWERS).query(answerBoolQueryBuilder.build()._toQuery()).innerHits(x -> new InnerHits.Builder()).build();

        BoolQuery.Builder finalQuery = QueryBuilders.bool()
                .must(answerNestQuery._toQuery())
                .must(x -> x.term(y -> y.field(AlgoKnowledge.KNOWLEDGE_ID).value(knowledgeId)))
                .must(x -> x.term(y -> y.field(AlgoKnowledge.STATUS).value(4)));

        SearchRequest.Builder requestBuilder = new SearchRequest.Builder();
        requestBuilder.index(channelQaApolloConfig.getKnowledgeEsIndex());
        requestBuilder.source(x -> x.filter(SourceFilter.of(y -> y.includes(DOC_FIELDS))));

        requestBuilder.query(
                finalQuery.build()._toQuery()
        );
        requestBuilder.size(20);
        SearchRequest searchRequest = requestBuilder.build();
        log.info("getCommonAnswerIdV2 {}", searchRequest.toString());

        List<Long> answerIds = new ArrayList<>();
        try {
            SearchResponse<JSONObject> response = es8Service.getElasticsearchClient().search(searchRequest, JSONObject.class);

            for (Hit<JSONObject> hit : response.hits().hits()) {
                AlgoKnowledge algoKnowledge = IoUtils.parseJson(hit.source().toJSONString(), AlgoKnowledge.class);

                if (hit.innerHits().get(AlgoKnowledge.ANSWERS).hits().hits().size() > 0) {
                    for (int i = 0; i < hit.innerHits().get(AlgoKnowledge.ANSWERS).hits().hits().size(); i++) {

                        AlgoAnswer algoAnswer = Objects.requireNonNull(hit.innerHits().get(AlgoKnowledge.ANSWERS).hits().hits().get(i).source()).to(AlgoAnswer.class);
                        answerIds.add(algoAnswer.getAnswerId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("getCommonAnswerIdV2", e);
        }
        return answerIds;
    }

    // 用来判断FAQ知识库中的答案是否包含itemid的特定知识(人工配的知识)，包含则返回true，不包含则返回false
    public boolean hasItemAnswerInFaq(long answerId,long itemId,BotContext botContext) {
        BoolQuery.Builder answerBoolQueryBuilder = QueryBuilders.bool();
        // item的筛选
        BoolQuery.Builder answerScopeBoolQueryBuilder = QueryBuilders.bool();

        // 存放item_id
        answerScopeBoolQueryBuilder.must(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_ITEM_INFO_LABEL).query(String.valueOf(itemId))));
        answerScopeBoolQueryBuilder.must(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_ID).query(String.valueOf(answerId))));

        answerBoolQueryBuilder.must(answerScopeBoolQueryBuilder.build()._toQuery());
        BoolQuery.Builder channelScopeBoolQueryBuilder = QueryBuilders.bool();

        long channel = botContext.getChannel();
        if (channel > 0) {
            channelScopeBoolQueryBuilder.should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_CHANNEL).value(channel)));
        }
        String platform = botContext.getPlatform();
        if (StringUtils.isNotEmpty(platform)) {
            channelScopeBoolQueryBuilder.should(x -> x.match(y -> y.field(AlgoKnowledge.ANSWER_PLATFORM).query(platform)));
        }
        answerBoolQueryBuilder.must(channelScopeBoolQueryBuilder.build()._toQuery());

        // 生效时间的处理
        BoolQuery.Builder answerTimeQuery = QueryBuilders.bool();
        // 通用
        answerTimeQuery.should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_EXPIRY_TIME).value(0)));

        answerTimeQuery.should(x -> x.bool(
                y -> y.must(z -> z.range(z1 -> z1.field(AlgoKnowledge.ANSWER_EXPIRY_TIME).gte(JsonData.of(botContext.getConsultTime()))))
                        .must(z -> z.range(z1 -> z1.field(AlgoKnowledge.ANSWER_EFFECTIVE_TIME).lte(JsonData.of(botContext.getConsultTime()))))
        ));

        answerBoolQueryBuilder.must(answerTimeQuery.build()._toQuery());

        int sessionInteraction = botContext.getSessionInteraction();

        //        if (sessionInteraction == BotContext.SESSION_INTERACTION_ROBOT) {
//            // 主站机器人不能直接出 智能解决方案类型的答案
//            answerBoolQueryBuilder.mustNot(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_TYPE).value(AlgoAnswer.ANSWER_TYPE_INTELLI_SOLUTION)));
//        }

        if (sessionInteraction == BotContext.SESSION_INTERACTION_HUMAN){
            answerBoolQueryBuilder.mustNot(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_TYPE).value(AlgoAnswer.ANSWER_TYPE_SOP_FLOW)));
        }

        // 相似问校验，不区分用途
        if ( sessionInteraction != BotContext.SESSION_INTERACTION_CHECK){
            BoolQuery.Builder answerUseQuery = QueryBuilders.bool().should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_USE).value(sessionInteraction == BotContext.SESSION_INTERACTION_ROBOT ? AlgoAnswer.ANSWER_USE_OUT : AlgoAnswer.ANSWER_USE_INTERNAL)));
            answerUseQuery.should(x -> x.term(y -> y.field(AlgoKnowledge.ANSWER_USE).value(AlgoAnswer.ANSWER_USE_INTERNAL_AND_OUT)));
            answerBoolQueryBuilder.must(answerUseQuery.build()._toQuery());
        }

        NestedQuery answerNestQuery = QueryBuilders.nested().path(AlgoKnowledge.ANSWERS).query(answerBoolQueryBuilder.build()._toQuery()).innerHits(x -> new InnerHits.Builder()).build();

        // 生效时间的处理
        BoolQuery.Builder timeQuery = QueryBuilders.bool();
        // 通用
        timeQuery.should(x -> x.term(y -> y.field(AlgoKnowledge.EXPIRY_TIME).value(0)));

        timeQuery.should(x -> x.bool(
                y -> y.must(z -> z.range(z1 -> z1.field(AlgoKnowledge.EXPIRY_TIME).gte(JsonData.of(botContext.getConsultTime()))))
                        .must(z -> z.range(z1 -> z1.field(AlgoKnowledge.EFFECTIVE_TIME).lte(JsonData.of(botContext.getConsultTime()))))
        ));
        BoolQuery.Builder statusQuery = QueryBuilders.bool();
        statusQuery.should(x -> x.term(y -> y.field(AlgoKnowledge.STATUS).value(4)));

        BoolQuery.Builder finalQuery = QueryBuilders.bool()
                .must(answerNestQuery._toQuery())
                .must(statusQuery.build()._toQuery())
                .must(timeQuery.build()._toQuery())
                .must(x -> x.term(y -> y.field(AlgoKnowledge.KNOWLEDGE_TYPE).value(1)));

        SearchRequest.Builder requestBuilder = new SearchRequest.Builder();
        requestBuilder.index(channelQaApolloConfig.getKnowledgeEsIndexWithAnswerUse());
        requestBuilder.source(x -> x.filter(SourceFilter.of(y -> y.includes(DOC_FIELDS))));

        requestBuilder.query(
                finalQuery.build()._toQuery()
        );
        requestBuilder.size(20);
        SearchRequest searchRequest = requestBuilder.build();
        log.info("getHasItemAnswerInFaqSearchRequest {}", searchRequest.toString());

        try {
            SearchResponse<JSONObject> response = es8Service.getElasticsearchClient().search(searchRequest, JSONObject.class);
            if (!response.hits().hits().isEmpty()) {
//                log.info("getHasItemAnswerInFaqSearchRequest hit 0 {}", response.hits().hits().get(0).toString());
                return true;
            }

        } catch (Exception e) {
            log.error("HasItemAnswerInFaqSearchRequest", e);
        }
        return false;
    }
}
