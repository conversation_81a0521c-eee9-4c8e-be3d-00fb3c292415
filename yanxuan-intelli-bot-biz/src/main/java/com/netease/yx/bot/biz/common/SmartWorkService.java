package com.netease.yx.bot.biz.common;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SmartWorkService {
    private static final String URL_TEMPLATE = "http://smart-infer.hz.infra.mail:31938/v1/models/%s:predict";
    private static final String HOST_TEMPLATE = "%s.yx-serving.svc";

    private static final String HEADER_KEY_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_VALUE_CONTENT_TYPE = "application/json";
    private static final String HEADER_KEY_HOST_TYPE = "host";
    private static final String REQUEST_KEY_INSTANCES = "instances";
    private static final String REQUEST_KEY_MODE = "mode";
    private static final String REQUEST_VALUE_MODE = "http";

    // 输入单个请求，返回默认HTTP，且自定义返回对象反序列化
    public <T> T httpStandardSingleReq(String modelName, Object object, TypeReference<T> typeReference) throws IOException {
        String response = httpStandardSingleReq(modelName, object);

        if (StringUtils.isNotEmpty(response)) {
            return IoUtils.parseJson(response, typeReference);
        }
        return null;
    }

    // 输入单个请求，返回默认HTTP
    private String httpStandardSingleReq(String modelName, Object object) {
        return httpStandardSingleReq(modelName, object, REQUEST_VALUE_MODE);
    }

    // 输入单个请求，指定返回格式
    public String httpStandardSingleReq(String modelName, Object object, String mode) {
        return standardReq(modelName, Lists.newArrayList(object), mode);
    }

    // 输入多个请求，指定返回格式，且自定义返回对象反序列化
    public <T> T standardReq(String modelName, List instanceObject, String mode, TypeReference<T> typeReference) throws IOException {
        String response = standardReq(modelName, instanceObject, mode);

        if (StringUtils.isNotEmpty(response)) {
            return IoUtils.parseJson(response, typeReference);
        }
        return null;
    }

    // 输入多个请求，指定返回格式
    public String standardReq(String modelName, List instanceObject, String mode) {
        Map<String, Object> reqObj = new HashMap<>(2);
        reqObj.put(REQUEST_KEY_INSTANCES, instanceObject);
        reqObj.put(REQUEST_KEY_MODE, mode);

        return req(modelName, reqObj);
    }

    // 自定义输入，且自定义返回对象反序列化
    public <T> T req(String modelName, Object reqObj, Class<T> tClass) throws IOException {
        String response = req(modelName, reqObj);
        if (StringUtils.isNotEmpty(response)) {
            return IoUtils.parseJson(response, tClass);
        }
        return null;
    }

    // 最原始的请求
    public String req(String modelName, Object reqObj) {
        Map<String, String> headers = new HashMap<String, String>(2);
        headers.put(HEADER_KEY_CONTENT_TYPE, HEADER_VALUE_CONTENT_TYPE);
        headers.put(HEADER_KEY_HOST_TYPE, String.format(HOST_TEMPLATE, modelName));

        String url = String.format(URL_TEMPLATE, modelName);

        log.info("req, request: {}, {}, {}", url, IoUtils.toJsonString(reqObj), headers);
        try {
            String response = HttpUtils.executePost(url, IoUtils.toJsonString(reqObj), null, headers);
            log.info("req, request: {} , response: {}", url, response);
            return response;
        } catch (IOException e) {
            log.error("req {}", url, e);
        }
        return null;
    }


}
