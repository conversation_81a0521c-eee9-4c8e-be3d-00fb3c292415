package com.netease.yx.bot.biz.service.qa.rank;

import com.netease.yx.bot.biz.common.DictService;
import com.netease.yx.bot.biz.service.ItemService;
import com.netease.yx.bot.biz.common.NlpServerService;
import com.netease.yx.bot.biz.common.RedisService;
import com.netease.yx.bot.biz.common.es8.Es8Service;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.biz.service.qa.emb.BertEmbeddingManager;
import com.netease.yx.bot.biz.service.qa.emb.BertMatchManager;
import com.netease.yx.bot.core.model.entity.KnowledgeMatchRslt;
import com.netease.yx.bot.core.model.entity.SimpleKnowledge;
import com.netease.yx.bot.core.model.entity.kfkm.AlgoKnowledge;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import com.netease.yx.bot.core.model.nlpserver.faq.FaqRsltInstance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RankService {
    private static final String VALUE_EMPTY = "empty";
    private static final int MAX_LEVEL = 4;
    private static final float ITEM_ID_BOOST = 1.5f;
    private static final String FEATURE_STORE_KEY_BERT_VEC = "bert_vec";
    private static final double FUZZY_THRESHOLD = 0.5;
    private static List<String> DOC_FIELDS = new ArrayList<>();

    static {
        DOC_FIELDS.add(AlgoKnowledge.CLEAN_STD_QUESTION);
        DOC_FIELDS.add(AlgoKnowledge.KNOWLEDGE_ID);
        DOC_FIELDS.add(AlgoKnowledge.UN_SIMILAR_QUESTIONS);
    }

    public final Integer MAX_LENGTH = 32;
    public final String HEADER_VALUE_HOST_TYPE = "tf-sort.yx-serving.svc";
    public final String HEADER_VALUE_BERT_EMB_HOST_TYPE = "aibot-match-sort-emb.yx-serving.svc";
    @Value("${rank.bert.embedding.url}")
    public String embeddingUrl;
    @Autowired
    private DictService dictService;
    @Autowired
    private ItemService itemService;
    @Autowired
    private Es8Service es8Service;
    @Autowired
    private NlpServerService nlpServerService;
    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;
    @Autowired
    private BotApolloConfig botApolloConfig;
    @Autowired
    private RedisService redisService;
    @Autowired
    private BertMatchManager bertMatchManager;
    @Autowired
    private BertEmbeddingManager bertEmbeddingManager;
    @Value("${rank.bert.match.url}")
    private String bertMatchUrl;

    public List<KnowledgeMatchRslt> leileiFaq(List<KnowledgeMatchRslt> matchRslts, TextBasicData textBasicData) {
        Map<String, List<Float>> vecMap = bertEmbeddingManager.getEmbeddingFromCache(matchRslts.stream().map(KnowledgeMatchRslt::getCorpusId).collect(Collectors.toSet()), FEATURE_STORE_KEY_BERT_VEC);
        return matchWithVecEmb(textBasicData, matchRslts, vecMap);
    }

    public List<KnowledgeMatchRslt> matchWithVecEmb(TextBasicData query, List<KnowledgeMatchRslt> candidates, Map<String, List<Float>> vecMap) {
        Map<String, KnowledgeMatchRslt> recallMatchProcessDataMap = new HashMap<>(candidates.size());
        log.info("BertServiceImpl.matchWithVecEmb {}", candidates);

        candidates.forEach(x -> recallMatchProcessDataMap.put(x.getCorpusId(), x));

        List<String> corpusIds = new ArrayList<>();
        List<List<Float>> vecList = new ArrayList<>();
        for (Map.Entry<String, List<Float>> entry : vecMap.entrySet()) {
            corpusIds.add(entry.getKey());
            vecList.add(entry.getValue());
        }

        List<Float> scores = bertMatchManager.getDistance(query.getCleanedText(), vecList, MAX_LENGTH, bertMatchUrl, HEADER_VALUE_HOST_TYPE, null);
        List<KnowledgeMatchRslt> results = new ArrayList<>();

        log.info("BertServiceImpl.matchWithVecEmb vec-size-{}, score-size-{}, candidates-size-{}", vecList.size(), scores.size(), candidates.size());
        for (int i = 0; i < scores.size(); i++) {
            float score = scores.get(i);
            if (score >= FUZZY_THRESHOLD) {
                String corpusId = corpusIds.get(i);
                KnowledgeMatchRslt pd = recallMatchProcessDataMap.get(corpusId);
                pd.setScore(score);
                results.add(pd);
            }
        }

        log.info("BertServiceImpl: result, {}, {}", results, scores);
        log.info("BertServiceImpl: candidate size-{}\tscore size-{}\tresult size-{}", candidates.size(), scores.size(), results.size());
        return results;
    }

    // 通用FAQ匹配，偏字面匹配
    public List<KnowledgeMatchRslt> commonFaq(List<KnowledgeMatchRslt> matchRslts, TextBasicData textBasicData) {
        List<String> candidates = matchRslts.stream().map(KnowledgeMatchRslt::getQuestion).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(candidates)) {
            List<String> ids = matchRslts.stream().map(KnowledgeMatchRslt::getCorpusId).collect(Collectors.toList());

            Map<String, KnowledgeMatchRslt> knowledgeMatchRespMap = new HashMap<>();
            for (KnowledgeMatchRslt knowledgeMatchRslt : matchRslts) {
                knowledgeMatchRespMap.put(knowledgeMatchRslt.getCorpusId(), knowledgeMatchRslt);
            }

            // 排序好的
            List<FaqRsltInstance> attributeNames = nlpServerService.faq(textBasicData.getCleanedText(), candidates, ids);
            log.info("getCommonFaqSimilarityThreshold {} {}", channelQaApolloConfig.getCommonFaqSimilarityThreshold(), attributeNames);

            attributeNames = attributeNames.stream().filter(x -> x.getScore() >= channelQaApolloConfig.getCommonFaqSimilarityThreshold()).collect(Collectors.toList());

            List<KnowledgeMatchRslt> filterMatchRslts = new ArrayList<>();
            for (FaqRsltInstance faqRsltInstance : attributeNames) {
                filterMatchRslts.add(knowledgeMatchRespMap.get(faqRsltInstance.getLabel()));
            }

            Collections.sort(filterMatchRslts);

            log.info("getCommonFaqSimilarityThreshold filter {} {}", channelQaApolloConfig.getCommonFaqSimilarityThreshold(), matchRslts);
            return filterMatchRslts;
        }
        return matchRslts;
    }

    // 基于客服语义相似度算法，浅层语义化
    // 能推荐个水吗和三个月大的猫吃哪个， 分数会很相似
    public List<KnowledgeMatchRslt> serviceQa(List<KnowledgeMatchRslt> matchRslts, TextBasicData textBasicData) {
        List<String> candidates = matchRslts.stream().map(KnowledgeMatchRslt::getQuestion).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(candidates)) {
            // 排序好的
            List<SimpleKnowledge> attributeNames = nlpServerService.unSortSimilarity(textBasicData.getCleanedText(), candidates);

            List<KnowledgeMatchRslt> filterRslts = new ArrayList<>();
            for (SimpleKnowledge attributeName : attributeNames) {
                matchRslts.get(attributeName.getIndex()).setScore(attributeName.getScore());

                if (attributeName.getScore() >= channelQaApolloConfig.getSimilarityThreshold()) {
                    filterRslts.add(matchRslts.get(attributeName.getIndex()));
                }
            }

            Collections.sort(filterRslts);

            // 因为相似问扩充复制，所以重新去重
            List<KnowledgeMatchRslt> duplicateAttrs = new ArrayList<>();

            Set<Long> attrIdSet = new HashSet<>();
            for (KnowledgeMatchRslt knowledgeMatchRslt : filterRslts) {
                if (attrIdSet.contains(knowledgeMatchRslt.getKnowledgeId())) {
                    continue;
                }
                attrIdSet.add(knowledgeMatchRslt.getKnowledgeId());
                duplicateAttrs.add(knowledgeMatchRslt);
            }
            return duplicateAttrs;
        }
        return matchRslts;
    }
}
