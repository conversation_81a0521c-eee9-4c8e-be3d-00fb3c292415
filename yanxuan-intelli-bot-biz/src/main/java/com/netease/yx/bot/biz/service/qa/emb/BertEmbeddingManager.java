package com.netease.yx.bot.biz.service.qa.emb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.netease.yanxuan.feast.sdk.vo.FeatureRequest;
import com.netease.yanxuan.feast.sdk.vo.FeatureResult;
import com.netease.yx.bot.biz.common.FeatureStoreService;
import com.netease.yx.bot.common.util.HttpUtils;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.faq.MatchServiceEmbHttpResponse;
import com.netease.yx.bot.core.model.entity.faq.MatchTokenizerInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class BertEmbeddingManager {

    private static final String HEADER_KEY_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_VALUE_CONTENT_TYPE = "application/json";
    private static final String HEADER_KEY_HOST_TYPE = "host";
    private static final String REQUEST_OBJ_KEY = "instances";
    private static final List<Float> ERROR_EMBEDDING = new ArrayList<>(Collections.nCopies(768, 0.0f));

    private static final String FEATURE_STORE_DOMAIN = "kfkm_question_id";
    private static final String FEATURE_STORE_KEY_BERT_VEC = "bert_vec";
    private static final String FEATURE_STORE_KNOWLEDGE_ID = "knowledge_id";

    private static final Set<String> FEATURE_NAMES = new HashSet<String>() {{
        add(FEATURE_STORE_KEY_BERT_VEC);
        add(FEATURE_STORE_KNOWLEDGE_ID);
    }};

    @Autowired
    private FeatureStoreService featureStoreService;

    @Autowired
    private TextProcessor4BertMatch preprocessor;

    public static void main(String[] args) {
        String str = " 【慵懒窝进椅子里 ，挪威真皮懒人休闲椅】的安装方式";
        String cleanStr = str.replaceAll("（.*）", "").replaceAll("【.*】", "");
        System.out.println(cleanStr);
    }

    public Map<String, List<Float>> getEmbeddingFromCache(Set<String> corpusIds, String keyName) {
        log.debug("BertEmbeddingManager corpusIds: {}", corpusIds);
        FeatureRequest request = FeatureRequest.builder().idType(FEATURE_STORE_DOMAIN).featureNames(FEATURE_NAMES).ids(corpusIds).build();

        Map<String, List<Float>> result = new HashMap<>();
        List<FeatureResult> featureResults = featureStoreService.getFeature(request);
        for (FeatureResult featureResult : featureResults) {
            if (featureResult != null) {
                String corpusId = featureResult.getId();
                Map<String, String> feature = featureResult.getFeatures();
                if (!MapUtils.isEmpty(feature) && feature.containsKey(keyName)) {
                    String vecStr = feature.get(keyName);
                    List<Float> vec = JSON.parseObject(vecStr, new TypeReference<List<Float>>() {
                    });
                    result.put(corpusId, vec);
                }
            }
        }
        if (!MapUtils.isEmpty(result)) {
            log.info("getEmbeddingFromCache size {}", result.size());
        }
        log.debug("BertEmbeddingManager.getEmbeddingFromCache result size {}", result.size());
        return result;
    }

    public List<List<Float>> getEmbedding(List<String> senList, Integer maxLength, String url, String headerHost) {
        List<List<Float>> matchServiceEmbResponse = new ArrayList<>();
        List<Integer> errorIndex = new ArrayList<>();
        List<String> validSen = new ArrayList<>();

        for (int i = 0; i < senList.size(); i++) {
            String str = senList.get(i) == null ? StringUtils.EMPTY : senList.get(i);
//            String cleanStr = str.replaceAll("（.*）", "").replaceAll("【.*】","");
            if (StringUtils.isEmpty(str)) {
                errorIndex.add(i);
            } else {
                validSen.add(str);
            }
        }
        try {
            List<MatchTokenizerInput> tokenizerInput = preprocessor.getInput(validSen, maxLength);
            Map<String, String> headers = buildHeaders(headerHost);
            Map<String, List<MatchTokenizerInput>> reqObj = new HashMap<>(1);

            reqObj.put(REQUEST_OBJ_KEY, tokenizerInput);
            String result = HttpUtils.executePost(url, JSON.toJSONString(reqObj), null, headers);

            MatchServiceEmbHttpResponse matchServiceEmbHttpResponse = IoUtils.parseJson(result, MatchServiceEmbHttpResponse.class);
            if (matchServiceEmbHttpResponse != null && matchServiceEmbHttpResponse.getPredictions() != null) {
                matchServiceEmbResponse = matchServiceEmbHttpResponse.getPredictions();
            }

        } catch (Exception e) {
            log.error("error in SortMatchEmbeddingImpl", e);
        }

        for (Integer index : errorIndex) {
            matchServiceEmbResponse.add(index, ERROR_EMBEDDING);
        }

        return matchServiceEmbResponse;
    }

    private Map<String, String> buildHeaders(String headerHost) {
        Map<String, String> headers = new HashMap<>(2);
        headers.put(HEADER_KEY_CONTENT_TYPE, HEADER_VALUE_CONTENT_TYPE);
        headers.put(HEADER_KEY_HOST_TYPE, headerHost);
        return headers;
    }

}
