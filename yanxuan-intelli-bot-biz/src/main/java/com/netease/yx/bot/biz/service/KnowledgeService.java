/**
 * @(#)KnowledgeManager.java, 2020/9/15.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.biz.service;

import com.netease.yx.bot.biz.applloconfig.ChannelQaApolloConfig;
import com.netease.yx.bot.biz.service.qa.recall.RecallServiceWithEs8;
import com.netease.yx.bot.core.model.entity.AlgoInfo;
import com.netease.yx.bot.core.model.entity.knowledge.Knowledge;
import com.netease.yx.bot.core.model.entity.knowledge.Shortcut;
import com.netease.yx.bot.core.model.mapper.knowledge.KnowledgeMapper;
import com.netease.yx.bot.core.model.mapper.knowledge.ShortcutMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Service
@Slf4j
@EnableScheduling
@Data
public class KnowledgeService {
    @Autowired
    private KnowledgeMapper knowledgeMapper;

    @Autowired
    private ShortcutMapper shortcutMapper;

    @Autowired
    private ChannelQaApolloConfig channelQaApolloConfig;

    @Autowired
    private RecallServiceWithEs8 recallServiceWithEs8;

    private List<Knowledge> knowledges;
    private List<Shortcut> shortcuts;

    private Map<Long, Knowledge> idKnowledgeMap;
    private Map<Long, List<AlgoInfo>> itemId2AlgoInfoMap;
    private Map<Long, List<AlgoInfo>> cateId2AlgoInfoMap;
    private Set<Long> shortcutIdSet;
    private Map<Long, List<Long>> cate2shortcutMap;

    @PostConstruct
    private void init() {
        knowledges = new ArrayList<>();
        shortcuts = new ArrayList<>();

        idKnowledgeMap = new HashMap<>();
        itemId2AlgoInfoMap = new HashMap<>();
        cateId2AlgoInfoMap = new HashMap<>();
        shortcutIdSet = new HashSet<>();
        cate2shortcutMap = new HashMap<>();

        try {
            reloadDb();
        } catch (Exception e) {
            log.error("reloadDb, ", e);
        }

    }

    @Scheduled(cron = "0 */${schedule.knowledge.interval.minutes} * * * *")
    private void reloadDb() {
        knowledges = knowledgeMapper.selectAll();
        log.info("reloadDb knowledges {}", knowledges.size());
        if (knowledges.size() > 0) {
            log.info(knowledges.get(0).toString());
        }

        shortcuts = shortcutMapper.selectAll();
        log.info("reloadDb shortcuts {} {}", shortcuts.size(), shortcuts.stream().limit(5).collect(Collectors.toSet()));
        shortcutIdSet = shortcuts.stream().map(Shortcut::getId).collect(Collectors.toSet());
        if (shortcuts.size() > 0) {
            log.info(shortcuts.get(0).toString());
        }

        Map<Long, Knowledge> newIdKnowledgeMap = new HashMap<>(knowledges.size());
        knowledges.forEach(x -> newIdKnowledgeMap.put(x.getId(), x));
        idKnowledgeMap = newIdKnowledgeMap;

        for (Knowledge knowledge : knowledges) {
            knowledge.parseCate2();
        }

        Map<Long, List<Long>> newCate2shortcutMap = new HashMap<>();
        for (Shortcut shortcut : shortcuts) {
            if (shortcut.getKnowledgeId() != 0) {
                Knowledge knowledge = newIdKnowledgeMap.getOrDefault(shortcut.getKnowledgeId(), null);
                if (knowledge != null && knowledge.getCate2() != 0) {
                    long cate2Id = knowledge.getCate2();
                    // 商品咨询类的气泡暂时不放
                    if (cate2Id == 101295L) {
                        continue;
                    }
                    if (!newCate2shortcutMap.containsKey(cate2Id)) {
                        newCate2shortcutMap.put(cate2Id, new ArrayList<>());
                    }
                    newCate2shortcutMap.get(cate2Id).add(shortcut.getId());
                }
            }
        }

        cate2shortcutMap = newCate2shortcutMap;
    }

    public Knowledge getKnowledgeById(long id) {
        return idKnowledgeMap.getOrDefault(id, null);
    }
}