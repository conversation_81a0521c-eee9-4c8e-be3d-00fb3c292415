package com.netease.yx.bot.biz.common;

import com.google.common.collect.Sets;
import com.netease.yanxuan.feast.sdk.vo.FeatureRequest;
import com.netease.yanxuan.feast.sdk.vo.FeatureResult;
import com.netease.yx.bot.common.util.EsUtil;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.AlgoInfo;
import com.netease.yx.bot.core.model.entity.alert.AlertResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class AlertService {
    public final static String FEATURE_STORE_KEY_BERT_VEC = "bert_vec";
    public final static String FEATURE_STORE_KEY_BERT_TWO_TURN_VEC = "bert_vec_context";
    private static final String KEY_STATUS = "Status";
    private static final String KEY_KNOWLEDGE_ID = "KnowledgeId";
    private final static String FEATURE_STORE_BERT_DOMAIN = "kfkm_question_id";
    private static final String FEATURE_STORE_KNOWLEDGE_ID = "knowledge_id";
    private static final int MAX_RETRY_TIMES = 3;
    private static final Set<String> FEATURE_NAMES = new HashSet<String>() {{
        add(FEATURE_STORE_KEY_BERT_VEC);
        add(FEATURE_STORE_KNOWLEDGE_ID);
        add(FEATURE_STORE_KEY_BERT_TWO_TURN_VEC);
    }};
    private List<Long> knwoledgeIdList = new ArrayList<>(12321);
    @Value("${alert.knolwedgeid.path}")
    private String alertKnowledgeIdPath;
    @Value("${recall.text.url}")
    private String textUrl;
    @Value("${recall.text.index.appName}")
    private String appName;
    @Value("${recall.text.index.indexName}")
    private String indexName;
    @Value("${recall.text.index.max}")
    private int max;

    @Autowired
    private FeatureStoreService featureStoreService;

    @PostConstruct
    private void init() {
        try {
            knwoledgeIdList = IoUtils.getLinesFromFile(alertKnowledgeIdPath).stream().map(Long::parseLong).collect(Collectors.toList());
        } catch (Exception e) {
            log.info("AlertManager.init error", e);
        }
    }

    private BoolQueryBuilder createQueryBuilder(long knowledgeId) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery(KEY_KNOWLEDGE_ID, String.valueOf(knowledgeId)));
        BoolQueryBuilder knowledgeScopeHumanSource = QueryBuilders.boolQuery();
        knowledgeScopeHumanSource.should(QueryBuilders.termQuery(KEY_STATUS, AlgoInfo.STATUS_FAQ));
        knowledgeScopeHumanSource.should(QueryBuilders.termQuery(KEY_STATUS, AlgoInfo.STATUS_INTERNAL));
        queryBuilder.must(knowledgeScopeHumanSource);
        return queryBuilder;
    }

    public AlertResp checkElasticSearch() {
        int correctCount = 0;

        for (Long knowledgeId : knwoledgeIdList) {
            // 构建es条件
            BoolQueryBuilder queryBuilder = createQueryBuilder(knowledgeId);
            SearchResponse response = EsUtil.search(textUrl, appName, indexName, queryBuilder, max);

            if (response.getHits().getHits().length > 0) {
                correctCount += 1;

                for (SearchHit hit : response.getHits().getHits()) {
                    AlgoInfo algoInfo = null;
                    try {
                        algoInfo = IoUtils.parseJson(IoUtils.toJsonString(hit.getSourceAsMap()), AlgoInfo.class);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    log.debug("AlertManager.checkElasticSearch {}, {}", algoInfo.getCorpusId(), algoInfo.getQuestion());
                }
            }
        }

        if (correctCount >= knwoledgeIdList.size() * 0.8) {
            return new AlertResp(true);
        }

        return new AlertResp(false);
    }

    public AlertResp checkBertVectorSmartwork(String keyName) {

        int esRespCount = 0;
        int smartworkRespCount = 0;

        for (Long knowledgeId : knwoledgeIdList) {
            // 构建es条件
            BoolQueryBuilder queryBuilder = createQueryBuilder(knowledgeId);
            SearchResponse response = EsUtil.search(textUrl, appName, indexName, queryBuilder, max);
            if (response.getHits().getHits().length > 0) {
                for (SearchHit hit : response.getHits().getHits()) {
                    esRespCount++;

                    AlgoInfo algoInfo = null;
                    try {
                        algoInfo = IoUtils.parseJson(IoUtils.toJsonString(hit.getSourceAsMap()), AlgoInfo.class);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    log.debug("AlertManager.checkBertVectorSmartwork.algoInfo.{} {}, {}", keyName, algoInfo.getCorpusId(), algoInfo.getQuestion());

                    FeatureRequest request = FeatureRequest.builder()
                            .idType(FEATURE_STORE_BERT_DOMAIN)
                            .featureNames(FEATURE_NAMES)
                            .ids(Sets.newHashSet(String.valueOf(algoInfo.getCorpusId())))
                            .build();
                    List<FeatureResult> featureResults = new ArrayList<>();

                    int tryCount = 0;
                    while (tryCount < MAX_RETRY_TIMES) {

                        try {
                            featureResults = featureStoreService.getFeature(request);
                            tryCount = MAX_RETRY_TIMES;
                        } catch (Exception e) {
                            tryCount++;

                            if (tryCount >= MAX_RETRY_TIMES) {
                                throw new RuntimeException(e);
                            }
                        }
                    }

                    for (FeatureResult featureResult : featureResults) {

                        if (featureResult != null) {
                            Map<String, String> feature = featureResult.getFeatures();
                            if (!MapUtils.isEmpty(feature) && feature.containsKey(keyName)) {
                                smartworkRespCount++;
                                log.debug("AlertManager.checkBertVectorSmartwork.vector.{} {}", keyName, feature.get(keyName));
                            }
                        }
                    }
                }
            }
        }

        if (smartworkRespCount >= esRespCount * 0.8) {
            return new AlertResp(true);
        }

        return new AlertResp(false);
    }
}
