<assembly
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3"
        xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 http://maven.apache.org/xsd/assembly-1.1.3.xsd">
    <id>yanxuan-intelli-bot</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <files>
        <file>
            <source>../deploy/env/setenv-${env}.sh</source>
            <outputDirectory>/</outputDirectory>
            <destName>setenv.sh</destName>
            <fileMode>0755</fileMode>
            <lineEnding>unix</lineEnding>
            <filtered>true</filtered>
        </file>
        <file>
            <source>../build/yanxuan-intelli-bot.jar</source>
            <outputDirectory>/</outputDirectory>
            <fileMode>0755</fileMode>
        </file>
    </files>
    <fileSets>
        <fileSet>
            <directory>../conf</directory>
            <outputDirectory>./conf</outputDirectory>
        </fileSet>
    </fileSets>
</assembly>
