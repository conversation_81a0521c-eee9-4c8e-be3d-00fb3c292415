package com.netease.yx.bot.assembly;

/**
 * Created by you<PERSON><PERSON> on 2017/4/26.
 */

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

/**
 * -Dspring.profiles.active=dev|test|pressure|release|online
 *
 * <AUTHOR>
 */
@SpringBootApplication
@ComponentScan(basePackages = {
        "com.netease.yx.bot",
        "com.netease.qian.rpc.provider",
        "com.netease.mail.yanxuan"
})
@ImportResource(locations = {"classpath:applicationContext.xml"})
@MapperScan(basePackages = {"com.netease.yx.bot.core.model.mapper.algo"})
@EnableApolloConfig
public class Application {

    public static void main(String[] args) throws Exception {
        SpringApplication.run(Application.class, args);
    }
}
