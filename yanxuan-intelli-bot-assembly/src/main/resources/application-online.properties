spring.datasource.db1.driverClassName=com.mysql.jdbc.Driver
spring.datasource.db1.username=yx_bot
spring.datasource.db1.password=yx_bot
spring.datasource.db1.jdbc-url=***********************************************************
spring.datasource.db1.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.db2.driverClassName=com.mysql.jdbc.Driver
spring.datasource.db2.username=yx_bot_v2
spring.datasource.db2.password=yx_bot_v2
spring.datasource.db2.jdbc-url=***************************************************************
spring.datasource.db2.type=com.alibaba.druid.pool.DruidDataSource
### version
version=1.0.0.0
## faq
intent_url=http://smart-infer.hz.infra.mail:31938/v1/models/yanxuan-intent:predict
spring.redis.cluster.nodes=10.200.210.173:16383,10.200.210.174:16383,10.200.210.175:16383,10.200.210.176:16383
spring.redis.lettuce.pool.max-active=50
spring.redis.lettuce.pool.max-wait=10000
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.cluster.refresh.adaptive=true
spring.redis.lettuce.cluster.refresh.period=30000
prophet.faq.max=5
# schedule
schedule.knowledge.interval.minutes=10
waiterdb.jdbc.url=********************************************************************************************
#waiterdb.jdbc.url=********************************************************************************************
schedule.item.interval.minutes=240
user.new.flag.url=http://127.0.0.1:8550/proxy/online.yanxuan-user.service.mailsaas/userNewOldFlagV2/isNewUserByUserId
dqs.url=http://127.0.0.1:8550/proxy/online.dp-dqs.service.mailsaas
dqs.model.user.order.appKey=yanxuan-intelli-bot
dqs.model.user.order.appSecret=jxTZOweQ
dqs.model.user.order.name=dm_yx_kf_intelligence_d
recall.text.url=http://127.0.0.1:8550/proxy/online.alg-admin.service.mailsaas/alg-admin/search.htm
recall.text.index.appName=drc_algorithm_recommend
recall.text.index.indexName=kfkm_question_algo_info_text_v3
recall.text.index.max=10
prophet.rule.schema.query.url=http://127.0.0.1:8550/proxy/online.cs-ai-kfbot.service.mailsaas/config/prophet/rule/schema/query
prophet.rule.config.query.url=http://127.0.0.1:8550/proxy/online.cs-ai-kfbot.service.mailsaas/sync/prophet/rule/query
schedule.prophet.rule.interval.minutes=5
dict.stop_words=conf/other/stopwords.dict
dict.synonyms_words=conf/other/synonyms.dict
dict.related_words=conf/other/related_words.txt
dict.highlight_words=conf/other/highlight_words.txt
item.rcmd.url=http://127.0.0.1:8550/proxy/online.athena.service.mailsaas/rcmd/recall
intent.bert.vocab=conf/other/bert_vocab.txt
guide.search.url=http://127.0.0.1:8550/proxy/online.yanxuan-item-search.service.mailsaas/search/mainsearch.do
guide.search.qp=http://127.0.0.1:8550/proxy/online.yanxuan-query-parser.service.mailsaas/qp/pipeline.do
guide.robot.text=conf/data/guide/guideFixText.txt
guide.rcmd.list.robot.text=conf/data/guide/guideMultiItemRCMDFixText.txt
guide.active.robot.text=conf/data/guide/guideActiveFixText.txt
guide.pro.zero.activity.robot.text=conf/data/guide/guideProZeroActivityFixText.txt
guide.pro.zero.activity.url=https://m.you.163.com/column/index
guide.qa.rcmd.json=conf/data/guide/guideFAQRCMD.json
chat.resp.path=conf/data/chat/chat_response.json
chat.classify.rule=conf/data/chat/chat_rule.json
chat.intent.zh.map.path=conf/data/chat/chat_intent_zh_map.json
rg.smart.visible.kfgroup.status.url=http://127.0.0.1:8550/proxy/online.cs-gateway-opendata.service.mailsaas/online/kefuGroup/query
rg.smart.visible.aftersale.rule.path=conf/data/smartRGVisible/aftersaleRule.json
rg.smart.visible.presale.rule.path=conf/data/smartRGVisible/presaleRule.json
rg.Assist.noAnswer.template=conf/data/rgAssist/noAnswer.txt
alert.knolwedgeid.path=conf/data/alert/knowledgeId.txt
chat.rg.resp.path=conf/data/chat/chat_response_rg.json
item.base.info.url=http://127.0.0.1:8550/proxy/online.ic-basis.service.mailsaas/spu/queryById
nlp.server.url=http://127.0.0.1:8550/proxy/online.yanxuan-nlp-server.service.mailsaas/nlp
doc.qa.json=conf/other/doc_qa_map.json
remote.service.mps.host=http://127.0.0.1:8550/proxy/online.yanxuan-mps.service.mailsaas/
remote.kfkm.host=http://127.0.0.1:8550/proxy/online.yanxuan-kfkm.service.mailsaas/
es.nodes=**************,**************,**************
es.port=7000
remote.host.url=http://127.0.0.1:8550/proxy/online.yanxuan-nlp-server.service.mailsaas
spring.kafka.bootstrap.servers=*************:9092,*************:9092,*************:9092
spring.kafka.bootstrap.servers2=*************:9092,*************:9092,*************:9092,*************:9092,*************:9092,*************:9092
# 通用的消费者设置
spring.kafka.consumer.session.timeout.ms=30000
spring.kafka.consumer.max.poll.records=1
spring.kafka.consumer.enable.auto.commit=true
spring.kafka.consumer.auto.commit.interval.ms=5000
spring.kafka.consumer.group.id=bot_online
spring.kafka.consumer.concurrency=3
spring.kafka.consumer.sleep.time.second=1
# 启动后都是监听启动后产生的新消息，如果是设置为"earliest"，就是监听最近还在服务器上的所有有效消息，一般是一周内的消息
spring.kafka.consumer.auto.offset.reset=latest
spring.kafka.topic.test=BINLOG-MYSQL-**************-13306-YX_BOT-YX_ES_QA
spring.kafka.topic.similarQ=BINLOG-MYSQL-**************-12306-YX_KEFU_KM-TB_YX_KFKM_KNOWLEDGE_SIMILAR_Q
spring.kafka.topic.knowledge=BINLOG-MYSQL-**************-12306-YX_KEFU_KM-TB_YX_KFKM_KNOWLEDGE
spring.kafka.topic.knowledgeAnswer=BINLOG-MYSQL-**************-12306-YX_KEFU_KM-TB_YX_KFKM_KNOWLEDGE_ANSWER
spring.kafka.topic.ysf_session=BINLOG-DDB-10.200.175.52-8888-YSF_SESSION
spring.kafka.topic.ysf_message=BINLOG-DDB-10.200.175.52-8888-YSF_MESSAGE
dqs_url=http://127.0.0.1:8550/proxy/online.dp-ras.service.mailsaas/ras/data/query
sgcKey=ZmsRmlxkj0fLXaAwMpBimYuhWtFr8b9YBm8qgiFfItoAVAmAWWQdK0R0b5W/Ft2IxTq3Jp8ikOUknKJEDnVm1A==
waiter.user=mayongqiang
waiter.password=2ia3oo4z
waiter.tidb-env=online
waiter.url=holmes-waiter.hz.infra.mail
rank.bert.match.url=http://smart-infer.hz.infra.mail:31938/v1/models/tf-sort:predict
rank.bert.embedding.url=http://smart-infer.hz.infra.mail:31938/v1/models/aibot-match-sort-emb:predict
rank.bert.vocab=conf/match/bert_vocab.txt
rank.bert.stop_words=conf/match/stop_words.txt
