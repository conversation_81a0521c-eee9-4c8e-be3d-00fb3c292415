<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-4.2.xsd">

    <bean id="eudemonRateLimitInterceptor"
          class="com.netease.mail.yanxuan.eudemon.EudemonRateLimitInterceptor">
    </bean>

    <bean id="dalServiceInteceptorProxyCreator"
          class="org.springframework.aop.framework.autoproxy.BeanNameAutoProxyCreator">
        <property name="proxyTargetClass" value="true"/>
        <property name="interceptorNames">
            <list>
                <value>eudemonRateLimitInterceptor</value>
            </list>
        </property>
        <property name="beanNames">
            <list>
                <value>*Controller</value>
            </list>
        </property>
    </bean>

</beans>