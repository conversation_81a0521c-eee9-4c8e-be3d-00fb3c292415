spring.datasource.db1.driverClassName=com.mysql.jdbc.Driver
spring.datasource.db1.username=yx_bot
spring.datasource.db1.password=yx_bot
# spring.datasource.db1.jdbc-url=************************************************************
# ????????
spring.datasource.db1.jdbc-url=**********************************************************
spring.datasource.db1.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.db2.driverClassName=com.mysql.jdbc.Driver
spring.datasource.db2.username=yx_kefu_km
spring.datasource.db2.password=yx_kefu_km
#spring.datasource.db2.jdbc-url=****************************************************************
# ????????
spring.datasource.db2.jdbc-url=**************************************************************
spring.datasource.db2.type=com.alibaba.druid.pool.DruidDataSource
### version
version=1.0.0.0
## faq
intent_url=http://smart-infer.hz.infra.mail:31938/v1/models/yanxuan-intent:predict
#spring.redis.cluster.nodes=10.131.98.178:16387,10.131.98.179:16387,10.131.99.203:16387
# ?????redis
spring.redis.cluster.nodes=10.104.0.206:16396,10.104.0.207:16396,10.104.0.208:16396
spring.redis.password=Uc479AxxUAlI
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=10000
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.cluster.refresh.adaptive=true
spring.redis.lettuce.cluster.refresh.period=30000
prophet.faq.max=5
# schedule
schedule.knowledge.interval.minutes=10
waiterdb.jdbc.url=*********************************************************************************************************
schedule.item.interval.minutes=240
user.new.flag.url=http://127.0.0.1:8550/proxy/test.yanxuan-user.service.mailsaas/userNewOldFlagV2/isNewUserByUserId
dqs.url=http://127.0.0.1:8550/proxy/test.dp-dqs.service.mailsaas
dqs.model.user.order.appKey=yanxuan-intelli-bot
dqs.model.user.order.appSecret=jxTZOweQ
dqs.model.user.order.name=dm_yx_kf_intelligence_d
recall.text.url=http://127.0.0.1:8550/proxy/test.alg-admin.service.mailsaas/alg-admin/search.htm
recall.text.index.appName=drc_algorithm_recommend
recall.text.index.indexName=kfkm_question_algo_info_text
recall.text.index.max=50
prophet.rule.schema.query.url=http://127.0.0.1:8550/proxy/test.cs-ai-kfbot.service.mailsaas/config/prophet/rule/schema/query
prophet.rule.config.query.url=http://127.0.0.1:8550/proxy/test.cs-ai-kfbot.service.mailsaas/sync/prophet/rule/query
schedule.prophet.rule.interval.minutes=1
# local data
dict.stop_words=conf/other/stopwords.dict
dict.synonyms_words=conf/other/synonyms.dict
dict.related_words=conf/other/related_words.txt
dict.highlight_words=conf/other/highlight_words.txt
item.rcmd.url=http://127.0.0.1:8550/proxy/test.athena.service.mailsaas/rcmd/recall
intent.bert.vocab=conf/other/bert_vocab.txt
guide.search.url=http://127.0.0.1:8550/proxy/test.yanxuan-item-search.service.mailsaas/search/mainsearch.do
guide.search.qp=http://127.0.0.1:8550/proxy/test.yanxuan-query-parser.service.mailsaas/qp/pipeline.do
guide.test.text=conf/data/tmp/guideText.txt
guide.robot.text=conf/data/guide/guideFixText.txt
guide.rcmd.list.robot.text=conf/data/guide/guideMultiItemRCMDFixText.txt
guide.active.robot.text=conf/data/guide/guideActiveFixText.txt
guide.pro.zero.activity.robot.text=conf/data/guide/guideProZeroActivityFixText.txt
guide.pro.zero.activity.url=https://m.you.163.com/column/index
guide.qa.rcmd.json=conf/data/guide/guideFAQRCMD.json
chat.resp.path=conf/data/chat/chat_response.json
chat.classify.rule=conf/data/chat/chat_rule.json
chat.intent.zh.map.path=conf/data/chat/chat_intent_zh_map.json
rg.smart.visible.kfgroup.status.url=http://127.0.0.1:8550/proxy/test.cs-gateway-opendata.service.mailsaas/online/kefuGroup/query
rg.smart.visible.aftersale.rule.path=conf/data/smartRGVisible/aftersaleRule.json
rg.smart.visible.presale.rule.path=conf/data/smartRGVisible/presaleRule.json
rg.Assist.noAnswer.template=conf/data/rgAssist/noAnswer.txt
alert.knolwedgeid.path=conf/data/alert/knowledgeId.txt
chat.rg.resp.path=conf/data/chat/chat_response_rg.json
item.base.info.url=http://127.0.0.1:8550/proxy/online.ic-basis.service.mailsaas/spu/queryById
nlp.server.url=http://127.0.0.1:8550/proxy/test.yanxuan-nlp-server.service.mailsaas/nlp
doc.qa.json=doc_qa_map.json
remote.service.mps.host=http://127.0.0.1:8550/proxy/test.yanxuan-mps.service.mailsaas/
remote.kfkm.host=http://127.0.0.1:8550/proxy/test.yanxuan-kfkm.service.mailsaas/
es.nodes=************,************,************
es.port=9200
remote.host.url=http://127.0.0.1:8550/proxy/test.yanxuan-nlp-server.service.mailsaas
# kafka ע������Դ�Ĳ�ͬ
spring.kafka.bootstrap.servers=*************:9093,*************:9093,*************:9093
spring.kafka.bootstrap.servers2=*************:9093,*************:9093,*************:9093
# ͨ�õ�����������
spring.kafka.consumer.session.timeout.ms=30000
spring.kafka.consumer.max.poll.records=1
spring.kafka.consumer.enable.auto.commit=true
spring.kafka.consumer.auto.commit.interval.ms=5000
spring.kafka.consumer.group.id=bot
spring.kafka.consumer.concurrency=2
spring.kafka.consumer.sleep.time.second=10
spring.kafka.consumer.auto.offset.reset=latest
spring.kafka.topic.test=TEST-BINLOG-MYSQL-**************-5306-YX_KEFU_KM-TB_YX_KFKM_KNOWLEDGE_HEAT
spring.kafka.topic.similarQ=TEST-BINLOG-MYSQL-**************-5306-YX_KEFU_KM-TB_YX_KFKM_KNOWLEDGE_SIMILAR_Q
spring.kafka.topic.knowledge=TEST-BINLOG-MYSQL-**************-5306-YX_KEFU_KM-TB_YX_KFKM_KNOWLEDGE
spring.kafka.topic.knowledgeAnswer=TEST-BINLOG-MYSQL-**************-5306-YX_KEFU_KM-TB_YX_KFKM_KNOWLEDGE_ANSWER
spring.kafka.topic.ysf_session=TEST-BINLOG-DDB-**************-8888-YSF_SESSION
spring.kafka.topic.ysf_message=TEST-BINLOG-DDB-**************-8888-YSF_MESSAGE
dqs_url=http://127.0.0.1:8550/proxy/test.dp-ras.service.mailsaas/ras/data/query
sgcKey=ZmsRmlxkj0fLXaAwMpBimYuhWtFr8b9YBm8qgiFfItoAVAmAWWQdK0R0b5W/Ft2IxTq3Jp8ikOUknKJEDnVm1A==
waiter.user=chenhan04
waiter.password=0gixac4y
waiter.tidb-env=online
waiter.url=holmes-waiter.hz.infra.mail
rank.bert.match.url=http://smart-infer.hz.infra.mail:31938/v1/models/tf-sort:predict
rank.bert.embedding.url=http://smart-infer.hz.infra.mail:31938/v1/models/aibot-match-sort-emb:predict
rank.bert.vocab=conf/match/bert_vocab.txt
rank.bert.stop_words=conf/match/stop_words.txt
