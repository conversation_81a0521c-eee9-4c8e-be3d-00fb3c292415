<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true" scanPeriod="60 seconds">
    <contextName>yanxuan-intelli-bot</contextName>

    <!--log level -->
    <springProfile name="dev">
        <property name="log.level" value="info"/>
        <property name="log.path" value="./logs/yanxuan-intelli-bot"/>
        <property name="log.maxHistory" value="5"/>
    </springProfile>

    <!--log level -->
    <springProfile name="test,pressure,release">
        <property name="log.level" value="info"/>
        <property name="log.path" value="/home/<USER>/yanxuan-intelli-bot/"/>
        <property name="log.maxHistory" value="30"/>
    </springProfile>

    <springProfile name="online">
        <property name="log.level" value="info"/>
        <property name="log.path" value="/home/<USER>/yanxuan-intelli-bot/"/>
        <property name="log.maxHistory" value="2"/>
    </springProfile>

    <appender name="bot-stat" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%msg</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/stat.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>${log.maxHistory}</maxHistory>
        </rollingPolicy>
    </appender>

    <logger name="holmes-logger" additivity="false" level="info">
        <appender-ref ref="bot-stat"/>
    </logger>

    <appender name="main"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>{time=%d{yyyy-MM-dd HH:mm:ss.SSS}, traceId=%X{XTraceId}, level=%p} [%c{0}] %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/main.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>${log.maxHistory}</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="error"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>{time=%d{yyyy-MM-dd HH:mm:ss.SSS}, traceId=%X{XTraceId}, level=%p} [%X{PtxId}] [%c{0}] %m%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/error.log.%d{yyyy-MM-dd}
            </fileNamePattern>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>error</level>
        </filter>
    </appender>

    <!--    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">-->
    <!--        <encoder>-->
    <!--            <pattern>{time=%d{yyyy-MM-dd HH:mm:ss.SSS}, traceId=%X{XTraceId}, level=%p} [%X{PtxId}] [%c{0}] %m%n-->
    <!--            </pattern>-->
    <!--        </encoder>-->
    <!--    </appender>-->

    <appender name="apollo"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>{time=%d{yyyy-MM-dd HH:mm:ss.SSS}, traceId=%X{XTraceId}, level=%p} [%c{0}] %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/apollo.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>${log.maxHistory}</maxHistory>
        </rollingPolicy>
    </appender>

    <logger name="com.ctrip.framework.apollo" additivity="false" level="info">
        <appender-ref ref="apollo"/>
    </logger>

    <appender name="eudemon-info"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%p] [%c{0}] %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/eudemon-info.log.%d{yyyy-MM-dd}.gz
            </fileNamePattern>
        </rollingPolicy>
    </appender>

    <appender name="eudemon-limit"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%p] [%c{0}] %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/eudemon-limit.log.%d{yyyy-MM-dd}.gz
            </fileNamePattern>
        </rollingPolicy>
    </appender>


    <logger name="EUDEMON_LOGGER" level="${log.level}" additivity="false">
        <appender-ref ref="eudemon-info"/>
    </logger>

    <logger name="EUDEMON_LIMIT_INFO" level="${log.level}"
            additivity="false">
        <appender-ref ref="eudemon-limit"/>
    </logger>

    <root level="${log.level}">
        <appender-ref ref="main"/>
        <appender-ref ref="error"/>
        <!--        <appender-ref ref="console"/>-->
    </root>

</configuration>