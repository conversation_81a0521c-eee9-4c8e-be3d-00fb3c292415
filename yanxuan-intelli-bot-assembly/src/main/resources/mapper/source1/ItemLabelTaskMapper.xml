<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.netease.yx.bot.core.model.mapper.algo.ItemLabelTaskMapper">

    <resultMap id="resultMap" type="com.netease.yx.bot.core.model.entity.label.ItemLabelTask">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="ItemId" property="itemId" jdbcType="BIGINT"/>
        <result column="InsertTime" property="insertTime" jdbcType="BIGINT"/>
        <result column="ItemPreviousLabelTask" property="itemPreviousLabelTaskStr" jdbcType="VARCHAR"/>
        <result column="Status" property="status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="allColumns">
        Id, ItemId, InsertTime, ItemPreviousLabelTask,Status
    </sql>

    <select id="selectAll" resultMap="resultMap">
        SELECT
        <include refid="allColumns"/>
        FROM `TB_YX_ITEM_LABEL_TASK`
    </select>

    <select id="selectByTime" resultMap="resultMap"
            parameterType="com.netease.yx.bot.core.model.entity.label.ItemLabelTaskSelectReq">
        SELECT
        <include refid="allColumns"/>
        FROM `TB_YX_ITEM_LABEL_TASK`
        WHERE
        InsertTime &gt;= #{startTime} and InsertTime &lt;= #{endTime}
    </select>


    <insert id="insert" parameterType="com.netease.yx.bot.core.model.entity.label.ItemLabelTask">
        INSERT INTO `TB_YX_ITEM_LABEL_TASK`
        (ItemId, InsertTime, ItemPreviousLabelTask,Status)
        VALUES
        ( #{itemId}, UNIX_TIMESTAMP(NOW()), #{itemPreviousLabelTaskStr})
    </insert>

    <update id="updateStatus" parameterType="com.netease.yx.bot.core.model.entity.label.ItemLabelTask">
        UPDATE
        `TB_YX_ITEM_LABEL_TASK`
        SET
        Status = #{status}
        WHERE
        Id = #{id}
    </update>

</mapper>