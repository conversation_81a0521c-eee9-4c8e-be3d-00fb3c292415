<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.netease.yx.bot.core.model.mapper.knowledge.KnowledgeMapper">

    <resultMap id="resultMap" type="com.netease.yx.bot.core.model.entity.knowledge.Knowledge">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="StdQuestion" property="stdQuestion" jdbcType="VARCHAR"/>
        <result column="KnowledgeType" property="knowledgeType" jdbcType="TINYINT"/>
        <result column="BusCate" property="busCate" jdbcType="VARCHAR"/>
        <result column="KmCate" property="kmCate" jdbcType="VARCHAR"/>
        <result column="ItemInfoLabel" property="itemInfoLabel" jdbcType="VARCHAR"/>
        <result column="ItemCateLabel" property="itemCateLabel" jdbcType="VARCHAR"/>
        <result column="OtherLabel" property="otherLabel" jdbcType="VARCHAR"/>
        <result column="Status" property="status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="allColumns">
        Id, StdQuestion, KnowledgeType, BusCate, KmCate, ItemInfoLabel, ItemCateLabel, OtherLabel, Status
    </sql>

    <select id="selectAll" resultMap="resultMap">
        SELECT
        <include refid="allColumns"/>
        FROM `TB_YX_KFKM_KNOWLEDGE_V2`
        WHERE Status=4 and KnowledgeType=1
    </select>
</mapper>