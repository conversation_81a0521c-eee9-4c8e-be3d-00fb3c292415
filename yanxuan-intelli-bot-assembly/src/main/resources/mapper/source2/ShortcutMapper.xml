<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.netease.yx.bot.core.model.mapper.knowledge.ShortcutMapper">

    <resultMap id="resultMap" type="com.netease.yx.bot.core.model.entity.knowledge.Shortcut">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="SMALLINT"/>
        <result column="answerType" property="answerType" jdbcType="SMALLINT"/>
        <result column="knowledgeId" property="knowledgeId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="allColumns">
        id, title, content, type,answerType,knowledgeId
    </sql>

    <select id="selectAll" resultMap="resultMap">
        select distinct TB_YX_KFKM_SHORTCUT.id,title, TB_YX_KFKM_SHORTCUT.content, TB_YX_KFKM_SHORTCUT.type, TB_YX_KFKM_SHORTCUT.answerType, TB_YX_KFKM_SHORTCUT_KNOWLEDGE_REL.knowledgeId from TB_YX_KFKM_SHORTCUT

        left join TB_YX_KFKM_SHORTCUT_KNOWLEDGE_REL on TB_YX_KFKM_SHORTCUT.id=TB_YX_KFKM_SHORTCUT_KNOWLEDGE_REL.shortcutId
        left join TB_YX_KFKM_KNOWLEDGE_V2 on TB_YX_KFKM_KNOWLEDGE_V2.id = TB_YX_KFKM_SHORTCUT_KNOWLEDGE_REL.knowledgeid
        left join TB_YX_KFKM_KNOWLEDGE_ANSWER_V2  on TB_YX_KFKM_KNOWLEDGE_V2.id = TB_YX_KFKM_KNOWLEDGE_ANSWER_V2 .knowledgeid

        where (TB_YX_KFKM_KNOWLEDGE_V2.Status=4 and TB_YX_KFKM_KNOWLEDGE_V2.KnowledgeType=1 and (answerUse=2 or answerUse=3)) or type=1
    </select>
</mapper>