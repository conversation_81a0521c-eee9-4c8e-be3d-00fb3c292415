{"swagger": "2.0", "info": {"description": "The api document is built by JDK-1.8.0_301 at: 2025-07-23 15:32:35 +0800", "version": "0", "title": "接口文档"}, "basePath": "/", "tags": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "报警接口"}, {"name": "ChannelController", "description": "全渠道商品搜索，实时问答"}, {"name": "CheckController", "description": "各种检查 相似问的有效性检查 知识重复的检查"}, {"name": "DMController", "description": "主站APP的机器人问答，接口很老"}, {"name": "ElasticController"}, {"name": "EmbeddingController"}, {"name": "GuideV2Controller"}, {"name": "HealthCheckController", "description": "Consul健康检查服务 由于历史问题，两个health接口都需要保留"}, {"name": "IntentProductController"}, {"name": "MpsMessageApiController"}, {"name": "QualityInspectionController", "description": "人工质检 接口保留，但不处理具体逻辑"}, {"name": "RGAssistController", "description": "人工辅助"}, {"name": "SingleModuleController"}, {"name": "SyncController"}, {"name": "TestController", "description": "报警接口"}, {"name": "WorkOrderController", "description": "七鱼客服工作台相关"}], "paths": {"/api/mps/mpsSend.do": {"post": {"tags": ["MpsMessageApiController"], "operationId": "MpsMessageApiController.mpsSend", "parameters": [{"in": "body", "name": "mpsReceiveMessageBean", "required": true, "schema": {"$ref": "#/definitions/MpsReceiveMessageBean"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/api/mps/receive": {"post": {"tags": ["MpsMessageApiController"], "summary": "接收MPS消息", "description": "接收MPS消息", "operationId": "MpsMessageApiController.receive", "parameters": [{"in": "body", "name": "messages", "description": "消息\n", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/MpsReceiveMessageBean"}}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/SucResp%3CObject%3E"}}}}}, "/api/mps/smartSessionClose.do": {"post": {"tags": ["MpsMessageApiController"], "operationId": "MpsMessageApiController.smartSessionClose", "parameters": [{"in": "body", "name": "mpsMessage", "required": true, "schema": {"$ref": "#/definitions/MpsReceiveMessageBean"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/api/mps/trainChange.do": {"post": {"tags": ["MpsMessageApiController"], "operationId": "MpsMessageApiController.trainChange", "parameters": [{"in": "body", "name": "mpsMessage", "required": true, "schema": {"$ref": "#/definitions/MpsReceiveMessageBean"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/emb/bert_embedding.do": {"post": {"tags": ["EmbeddingController"], "operationId": "EmbeddingController.embedding", "parameters": [{"in": "body", "name": "sen", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/emb/bert_embedding_v2.do": {"post": {"tags": ["EmbeddingController"], "operationId": "EmbeddingController.embeddingV2", "parameters": [{"in": "body", "name": "bertEmbReq", "required": true, "schema": {"$ref": "#/definitions/BertEmbReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/es/bulk/insert": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.main", "parameters": [{"in": "body", "name": "simpleBulkRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleBulkRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/es/index/delete": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.delete", "parameters": [{"in": "body", "name": "simpleBulkRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleBulkRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/es/search/knn": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.knn", "parameters": [{"in": "body", "name": "simpleKnnRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleKnnRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/es/search/matchAll": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.matchAll", "parameters": [{"in": "body", "name": "simpleKnnRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleKnnRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/es/search/search": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.search", "parameters": [{"in": "body", "name": "simpleKnnRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleKnnRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/i/health": {"post": {"tags": ["HealthCheckController"], "summary": "健康检查接口", "description": "健康检查接口", "operationId": "HealthCheckController.checkHealth", "parameters": [], "responses": {"200": {"description": "successful operation"}}}}, "/sync/attr.do": {"post": {"tags": ["SyncController"], "operationId": "SyncController.attr", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "attrId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/sync/knowledge.do": {"post": {"tags": ["SyncController"], "operationId": "SyncController.knowledge", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/workbench/classify.do": {"post": {"tags": ["WorkOrderController"], "operationId": "WorkOrderController.doPost", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "content", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/SessionResp"}}}}}, "/yanxuan-intelli-bot/batch_multi_channel_qa.do": {"post": {"tags": ["ChannelController"], "operationId": "ChannelController.batchMultiChannelQaForTrain", "parameters": [{"in": "body", "name": "batchChannelQaReq", "required": true, "schema": {"$ref": "#/definitions/BatchChannelQaReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/chat_classify.do": {"post": {"tags": ["IntentProductController"], "summary": "闲聊分类", "description": "闲聊分类", "operationId": "IntentProductController.chatClassify", "parameters": [{"in": "body", "name": "req", "description": "", "required": true, "schema": {"$ref": "#/definitions/ChatClassifyReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/check_manual.do": {"post": {"tags": ["CheckController"], "summary": "检查是否是人工", "description": "检查是否是人工", "operationId": "CheckController.checkManual", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/check_similar_knowledge.do": {"post": {"tags": ["CheckController"], "operationId": "CheckController.check", "parameters": [{"in": "body", "name": "checkSimilarKnowledgeReq", "required": true, "schema": {"$ref": "#/definitions/CheckSimilarKnowledgeReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/check_similar_question.do": {"post": {"tags": ["CheckController"], "operationId": "CheckController.check_1", "parameters": [{"in": "body", "name": "checkSimilarReq", "required": true, "schema": {"$ref": "#/definitions/CheckSimilarQuestionReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/check_transfer_rg.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.checkTransferRg", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "sessionId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/checkBotKnowledgeES": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "AlertController.checkBotKnowledgeES", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/checkBotSmartworkBertContextVec": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "AlertController.checkBotSmartworkBertContextVec", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/checkBotSmartworkBertVec": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "AlertController.checkBotSmartworkBertVec", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/get_item_info.do": {"post": {"tags": ["GuideV2Controller"], "summary": "item 信息", "description": "item 信息", "operationId": "GuideV2Controller.itemInfo", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/guide.do": {"post": {"tags": ["GuideV2Controller"], "summary": "导购主接口", "description": "导购主接口", "operationId": "GuideV2Controller.guide", "parameters": [{"in": "body", "name": "req", "description": "", "required": true, "schema": {"$ref": "#/definitions/GuideReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/guide_active.do": {"post": {"tags": ["GuideV2Controller"], "summary": "导购主动推荐", "description": "导购主动推荐", "operationId": "GuideV2Controller.guideActive", "parameters": [{"in": "body", "name": "req", "description": "", "required": true, "schema": {"$ref": "#/definitions/GuideReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/guide_intent.do": {"post": {"tags": ["GuideV2Controller"], "summary": "意图接口调用", "description": "意图接口调用", "operationId": "GuideV2Controller.intent", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/health.do": {"post": {"tags": ["HealthCheckController"], "operationId": "HealthCheckController.doPost", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "properties": {"MAP.KEY": {"type": "string"}}}}}}}, "/yanxuan-intelli-bot/input_associate.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.inputAssociate", "parameters": [{"in": "body", "name": "inputAssociateReq", "required": true, "schema": {"$ref": "#/definitions/InputAssociateReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_first.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.intentFirst", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_product.do": {"post": {"tags": ["IntentProductController"], "summary": "售后意图分类", "description": "售后意图分类", "operationId": "IntentProductController.intentProduct", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_product_cls.do": {"post": {"tags": ["IntentProductController"], "summary": "售后分类服务", "description": "售后分类服务", "operationId": "IntentProductController.intentProductCls", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_product_fs.do": {"post": {"tags": ["IntentProductController"], "summary": "售后fewshot服务", "description": "售后fewshot服务", "operationId": "IntentProductController.intentProductFs", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_product_ver.do": {"post": {"tags": ["IntentProductController"], "summary": "售后意图分类AB版本", "description": "售后意图分类AB版本", "operationId": "IntentProductController.intentProductVer", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/item_base_info.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.getSrcId", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/item_label_task_insert.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.insert", "parameters": [{"in": "body", "name": "itemLabelTask", "required": true, "schema": {"$ref": "#/definitions/ItemLabelTask"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/item_label_task_select.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.select", "parameters": [{"in": "body", "name": "itemLabelTaskSelectReq", "required": true, "schema": {"$ref": "#/definitions/ItemLabelTaskSelectReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/item_label_task_update.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.select_1", "parameters": [{"in": "body", "name": "itemLabelTask", "required": true, "schema": {"$ref": "#/definitions/ItemLabelTask"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/multi_channel_qa.do": {"post": {"tags": ["ChannelController"], "operationId": "ChannelController.multiChannelQa", "parameters": [{"in": "body", "name": "channelQaReq", "required": true, "schema": {"$ref": "#/definitions/ChannelQaReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/multi_channel_qa_addi.do": {"post": {"tags": ["ChannelController"], "operationId": "ChannelController.multiChannelQaAddi", "parameters": [{"in": "body", "name": "channelQaReq", "required": true, "schema": {"$ref": "#/definitions/ChannelQaAddiReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/multi_channel_search.do": {"post": {"tags": ["ChannelController"], "operationId": "ChannelController.check", "parameters": [{"in": "body", "name": "searchReq", "required": true, "schema": {"$ref": "#/definitions/SearchReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/ngram.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.ngramPreprocess", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/prophet.do": {"post": {"tags": ["SingleModuleController"], "summary": "服务先知接口", "description": "服务先知接口", "operationId": "SingleModuleController.prophet", "parameters": [{"in": "body", "name": "prophetReq", "description": "", "required": true, "schema": {"$ref": "#/definitions/ProphetReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/qp.do": {"post": {"tags": ["GuideV2Controller"], "summary": "搜索QP接口", "description": "搜索QP接口", "operationId": "GuideV2Controller.qp", "parameters": [{"in": "body", "name": "req", "description": "", "required": true, "schema": {"$ref": "#/definitions/GuideReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/quality_inspection.do": {"post": {"tags": ["QualityInspectionController"], "operationId": "QualityInspectionController.qualityInspection", "parameters": [{"in": "body", "name": "request", "required": true, "schema": {"$ref": "#/definitions/QualityInspectionRequest"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/QualityInspectionResponse"}}}}}, "/yanxuan-intelli-bot/rcmd.do": {"post": {"tags": ["GuideV2Controller"], "summary": "召回接口调用", "description": "召回接口调用", "operationId": "GuideV2Controller.rcmd", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/rg_assist_guide.do": {"post": {"tags": ["RGAssistController"], "operationId": "RGAssistController.guide", "parameters": [{"in": "body", "name": "RGAssistGuideReq", "required": true, "schema": {"$ref": "#/definitions/RGAssistGuideReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/rg_assist_guide_rcmd_reason.do": {"post": {"tags": ["RGAssistController"], "operationId": "RGAssistController.rcmd", "parameters": [{"in": "body", "name": "rgAssistRcmdReasonReq", "required": true, "schema": {"$ref": "#/definitions/RGAssistRcmdReasonReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/rg_assist_guide_scan.do": {"post": {"tags": ["RGAssistController"], "operationId": "RGAssistController.guide_1", "parameters": [{"in": "body", "name": "RGAssistGuideTrackReq", "required": true, "schema": {"$ref": "#/definitions/RGAssistGuideTrackReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/search.do": {"post": {"tags": ["GuideV2Controller"], "summary": "搜搜接口调用", "description": "搜搜接口调用", "operationId": "GuideV2Controller.search", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/shortcut_rcmd.do": {"post": {"tags": ["SingleModuleController"], "summary": "快捷短语气泡词预测接口", "description": "快捷短语气泡词预测接口", "operationId": "SingleModuleController.prophet_1", "parameters": [{"in": "body", "name": "shortcutRcmdReq", "description": "", "required": true, "schema": {"$ref": "#/definitions/ShortcutRcmdReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/attr.do": {"post": {"tags": ["TestController"], "operationId": "TestController.attr", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "attrId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/chat.do": {"post": {"tags": ["TestController"], "operationId": "TestController.chat", "parameters": [{"in": "body", "name": "req", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/ReqInstance"}}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/getAnswerId.do": {"post": {"tags": ["TestController"], "operationId": "TestController.getAnswerId", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "channelId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "platform", "in": "formData", "required": true, "type": "string"}, {"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/getFeature.do": {"post": {"tags": ["TestController"], "operationId": "TestController.getFeature", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "domain", "in": "formData", "required": true, "type": "string"}, {"name": "featureNames", "in": "formData", "required": true, "type": "string"}, {"name": "ids", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/intent.do": {"post": {"tags": ["TestController"], "operationId": "TestController.intent", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "sen", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/knowledge.do": {"post": {"tags": ["TestController"], "operationId": "TestController.knowledge", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/knowledgeId2outAnswer.do": {"post": {"tags": ["TestController"], "operationId": "TestController.knowledgeId2outAnswer", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/knowledgeSolution.do": {"post": {"tags": ["TestController"], "operationId": "TestController.knowledgeSolution", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/processWithSimpleModel.do": {"post": {"tags": ["TestController"], "operationId": "TestController.processWithSimpleModel", "parameters": [{"in": "body", "name": "botContext", "required": true, "schema": {"$ref": "#/definitions/BotContext"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/recallWithEs6.do": {"post": {"tags": ["TestController"], "operationId": "TestController.chat_1", "parameters": [{"in": "body", "name": "botContext", "required": true, "schema": {"$ref": "#/definitions/BotContext"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/redis.do": {"post": {"tags": ["TestController"], "operationId": "TestController.redisGet", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "key", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/redisDelete.do": {"post": {"tags": ["TestController"], "operationId": "TestController.redisDelete", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "key", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/secure.do": {"post": {"tags": ["TestController"], "operationId": "TestController.secure", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "sen", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/similarity.do": {"post": {"tags": ["TestController"], "operationId": "TestController.similarity", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "input", "in": "formData", "required": true, "type": "string"}, {"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "threshold", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/text.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.pipeline", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/usercenter-prophet.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.userCenterProphet", "parameters": [{"in": "body", "name": "prophetReq", "description": "", "required": true, "schema": {"$ref": "#/definitions/ProphetReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/yx_bot_kb.do": {"post": {"tags": ["DMController"], "operationId": "DMController.process", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BotResp"}}}}}}, "definitions": {"AlgoAnswer": {"properties": {"answerContent": {"type": "string"}, "answerId": {"type": "integer", "format": "int64"}, "answerType": {"type": "integer", "format": "int32"}, "answerUse": {"type": "integer", "format": "int32"}, "channel": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "createTime": {"type": "integer", "format": "int64"}, "deleteFlag": {"type": "integer", "format": "int32"}, "editTime": {"type": "integer", "format": "int64"}, "effectiveTime": {"type": "integer", "format": "int64"}, "expiryTime": {"type": "integer", "format": "int64"}, "itemCateLabel": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "itemCateLabelStr": {"type": "string"}, "itemInfoLabel": {"type": "string"}, "otherLabel": {"type": "array", "items": {"type": "string"}}, "platform": {"type": "array", "items": {"type": "string"}}, "relevanceKnowledge": {"type": "string"}, "relevanceShortCut": {"type": "string"}, "updateTime": {"type": "integer", "format": "int64"}}}, "AlgoKeyword": {"properties": {"cleanKeyword": {"type": "string"}, "keyword": {"type": "string"}, "status": {"type": "integer", "format": "int32"}}}, "AlgoKnowledge": {"properties": {"answerNum": {"type": "integer", "format": "int32"}, "answers": {"type": "array", "items": {"$ref": "#/definitions/AlgoAnswer"}}, "busCate": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "cleanStdQuestion": {"type": "string"}, "cleanStdQuestionTerms": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "editTime": {"type": "integer", "format": "int64"}, "effectiveTime": {"type": "integer", "format": "int64"}, "effectiveType": {"type": "integer", "format": "int32"}, "expiryTime": {"type": "integer", "format": "int64"}, "keywordGroups": {"type": "array", "items": {"$ref": "#/definitions/AlgoKeyword"}}, "kmCate": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "knowledgeId": {"type": "integer", "format": "int64"}, "knowledgeType": {"type": "integer", "format": "int32"}, "knowledgeUse": {"type": "integer", "format": "int32"}, "similarQuestions": {"type": "array", "items": {"$ref": "#/definitions/AlgoSimilarQuestion"}}, "status": {"type": "integer", "format": "int32"}, "stdQuestion": {"type": "string"}, "stqQuestionVector": {"type": "array", "items": {"type": "number", "format": "float"}}, "unSimilarQuestions": {"type": "array", "items": {"$ref": "#/definitions/AlgoSimilarQuestion"}}, "updateTime": {"type": "integer", "format": "int64"}}}, "AlgoSimilarQuestion": {"properties": {"cleanContent": {"type": "string"}, "cleanContentTerms": {"type": "string"}, "content": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "status": {"type": "integer", "format": "int32"}}}, "BaseResp": {"properties": {"code": {"type": "integer", "format": "int32"}}}, "BatchChannelQaReq": {"properties": {"qaReqList": {"type": "array", "items": {"$ref": "#/definitions/ChannelQaReq"}}}}, "BertEmbReq": {"properties": {"headerHost": {"type": "string"}, "maxLength": {"type": "integer", "format": "int32"}, "sen": {"type": "array", "items": {"type": "string"}}, "url": {"type": "string"}}}, "BotContext": {"properties": {"activeRcmdCount": {"type": "integer", "format": "int32"}, "cardItemIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "cardItems": {"type": "array", "items": {"$ref": "#/definitions/Item"}}, "channel": {"type": "integer", "format": "int64"}, "consultTime": {"type": "integer", "format": "int64"}, "deviceType": {"type": "string", "enum": ["NULL", "ANDROID", "IOS", "OTHER"]}, "hstTexts": {"type": "array", "items": {"type": "string"}}, "inputs": {"type": "array", "items": {"$ref": "#/definitions/TextBasicData"}}, "invokeSource": {"type": "string", "enum": ["ROBOT", "HUMAN", "LIVE"]}, "item": {"$ref": "#/definitions/Item"}, "itemId": {"type": "integer", "format": "int64"}, "itemIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "itemPhyCategoryIdStr": {"type": "string"}, "multiChannelOrderId": {"type": "string"}, "multiChannelPipelineLogs": {"type": "array", "items": {"$ref": "#/definitions/MultiChannelPipelineLog"}}, "multiChannelSessionId": {"type": "string"}, "newUser": {"type": "boolean"}, "orderId": {"type": "integer", "format": "int64"}, "orderItemIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "orderItems": {"type": "array", "items": {"$ref": "#/definitions/Item"}}, "pipelineLogs": {"type": "array", "items": {"$ref": "#/definitions/MainPipelineLog"}}, "platform": {"type": "string"}, "platformItemId": {"type": "string"}, "platformRawItemCardId": {"type": "string"}, "platformType": {"type": "string", "enum": ["NULL", "APP", "WECHAT", "WEB"]}, "preSale": {"type": "boolean"}, "sessionId": {"type": "integer", "format": "int64"}, "sessionInteraction": {"type": "integer", "format": "int32"}, "sourceType": {"type": "string", "enum": ["NULL", "ALL", "ORDER_DETAIL", "AFTER_SALE_PROCESS", "AFTER_SALE_SERVICE", "USER_CENTER", "ITEM_DETAIL", "MESSAGE_CENTER", "CUSTOMER_REVIEWS", "WECHAT", "WEB", "BALANCE", "PRO_MEMBERSHIP", "CROWDFUNDING_ITEM"]}, "testMode": {"type": "boolean"}, "ticketStatus": {"type": "integer", "format": "int32"}, "turnCount": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int64"}, "userOrder": {"$ref": "#/definitions/UserOrder"}, "userR": {"type": "integer", "format": "int32"}, "userRGCount24H": {"type": "integer", "format": "int32"}, "userRGCount48H": {"type": "integer", "format": "int32"}, "userV": {"type": "integer", "format": "int32"}}}, "BotReq": {"properties": {"data": {"type": "string"}, "faqIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "isOrderQues": {"type": "boolean"}, "itemId": {"type": "integer", "format": "int64"}, "kfGroupId": {"type": "integer", "format": "int64"}, "messageId": {"type": "string"}, "orderId": {"type": "integer", "format": "int64"}, "packageId": {"type": "integer", "format": "int64"}, "sessionId": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}}}, "BotResp": {"properties": {"chatIntent": {"type": "string"}, "faqRslt": {"$ref": "#/definitions/FaqIdRslt"}, "guideRcmdReason": {"type": "string"}, "guideRslt": {"type": "array", "items": {"$ref": "#/definitions/GuideRslt"}}, "intentRslt": {"$ref": "#/definitions/IntentRslt"}, "isRGVisible": {"type": "boolean"}, "noAnswer": {"type": "boolean"}, "statType": {"type": "integer", "format": "int32"}, "state": {"type": "string", "enum": ["SUCCESS", "PARAM_ERROR", "PARAM_NOT_ENOUGH", "INTERNAL_ERROR"]}, "text": {"type": "string"}}}, "CateResult": {"properties": {"cateName": {"type": "string"}, "cateSummaryResult": {"$ref": "#/definitions/SummaryResult"}, "leafCateId": {"type": "integer", "format": "int64"}, "score": {"type": "number", "format": "double"}}}, "ChannelQaAddiReq": {"properties": {"channelId": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "itemIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "itemPhyCategoryIdStr": {"type": "string"}, "orderId": {"type": "string"}, "platformItemId": {"type": "string"}, "platformRawItemCardId": {"type": "string"}, "serviceId": {"type": "string"}, "sessionId": {"type": "string"}}}, "ChannelQaReq": {"properties": {"batch": {"type": "boolean"}, "channelId": {"type": "integer", "format": "int64"}, "consultTime": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int32"}, "messageContent": {"$ref": "#/definitions/MessageContent"}, "messageId": {"type": "string"}, "messageSource": {"type": "integer", "format": "int32"}, "messageType": {"type": "integer", "format": "int32"}, "platform": {"type": "string"}, "rawMsg": {"type": "string"}, "serviceId": {"type": "string"}, "sessionId": {"type": "string"}, "sessionInteraction": {"type": "integer", "format": "int32"}, "testMode": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}, "topK": {"type": "integer", "format": "int32"}, "userId": {"type": "string"}}}, "ChannelQaResp": {"properties": {"channelQaAddiReq": {"$ref": "#/definitions/ChannelQaAddiReq"}, "coreIntents": {"type": "array", "items": {"$ref": "#/definitions/CoreIntent"}}, "faqList": {"type": "array", "items": {"$ref": "#/definitions/ChannelQaRslt"}}, "intentTypeV2": {"type": "string", "enum": ["CHITCHAT", "KBQA", "FAQ", "SPECIAL", "ITEM_CARD", "ORDER_CARD", "GUIDE", "UNKNOWN"]}, "scoreType": {"type": "integer", "format": "int32"}}}, "ChannelQaRslt": {"properties": {"answerId": {"type": "integer", "format": "int64"}, "attrSource": {"type": "integer", "format": "int32"}, "cateId": {"type": "integer", "format": "int64"}, "corpusId": {"type": "string"}, "intentId": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "itemPhyCategoryIdStr": {"type": "string"}, "knowledgeId": {"type": "integer", "format": "int64"}, "knowledgeSource": {"type": "integer", "format": "int32"}, "knowledgeType": {"type": "integer", "format": "int32"}, "platformItemId": {"type": "string"}, "platformRawItemCardId": {"type": "string"}, "question": {"type": "string"}, "score": {"type": "number", "format": "double"}, "showAnswer": {"type": "string"}, "showQuestion": {"type": "string"}}}, "ChatClassifyReq": {"properties": {"sen": {"type": "string"}}}, "CheckSimilarKnowledgeReq": {"properties": {"extra": {"type": "object", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}, "needCheckKnowledgeMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/NeedCheckKnowledge"}, "properties": {"MAP.KEY": {"type": "integer", "format": "int64"}}}, "topK": {"type": "integer", "format": "int32"}}}, "CheckSimilarQuestionReq": {"properties": {"checkTypes": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "knowledges": {"type": "array", "items": {"$ref": "#/definitions/AlgoKnowledge"}}}}, "CoreIntent": {"properties": {"confidence": {"type": "string"}, "idx": {"type": "integer", "format": "int32"}, "intent": {"type": "string"}, "intentCh": {"type": "string"}, "knowledgeId": {"type": "integer", "format": "int64"}, "knowledgeType": {"type": "string"}, "prob": {"type": "number", "format": "double"}}}, "FaqIdRslt": {"properties": {"faqIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "faqQues": {"type": "array", "items": {"type": "string"}}, "scores": {"type": "array", "items": {"type": "number", "format": "double"}}, "state": {"type": "string", "enum": ["SUCCESS", "PARAM_ERROR", "PARAM_NOT_ENOUGH", "INTERNAL_ERROR"]}, "type": {"type": "integer", "format": "int32"}}}, "FilterTerm": {"properties": {"func": {"type": "string", "enum": ["MATCH", "TERM"]}, "key": {"type": "string"}, "nested": {"type": "boolean"}, "nestedPath": {"type": "string"}, "value": {"type": "string"}}}, "GuideReq": {"properties": {"itemId": {"type": "string"}, "query": {"type": "string"}, "sessionId": {"type": "string"}, "uid": {"type": "string"}}}, "GuideRslt": {"properties": {"desc": {"type": "string"}, "itemId": {"type": "integer", "format": "int64"}, "itemNickName": {"type": "string"}, "note": {"type": "string"}, "picture": {"type": "string"}, "rcmdReason": {"type": "array", "items": {"type": "string"}}, "rcmdTitle": {"type": "array", "items": {"type": "string"}}, "title": {"type": "string"}, "url": {"type": "string"}}}, "InputAssociateReq": {"properties": {"query": {"type": "string"}, "sessionId": {"type": "integer", "format": "int64"}}}, "IntentRslt": {"properties": {"coreIntents": {"type": "array", "items": {"$ref": "#/definitions/CoreIntent"}}, "firstIntent": {"type": "string", "enum": ["KBQA", "GUIDE", "CHITCHAT", "FAQ", "SPECIAL", "ITEM_CARD", "ORDER_CARD", "UNKNOWN"]}, "secondIntent": {"type": "string", "enum": ["SPECIAL_MANUAL", "SPECIAL_COMPLAIN"]}}}, "Item": {"properties": {"itemId": {"type": "integer", "format": "int64"}, "itemName": {"type": "string"}, "nickName": {"type": "string"}, "phyCategory1Id": {"type": "integer", "format": "int64"}, "phyCategory2Id": {"type": "integer", "format": "int64"}, "phyCategory3Id": {"type": "integer", "format": "int64"}, "phyCategory4Id": {"type": "integer", "format": "int64"}, "picUrl": {"type": "string"}, "soldOut": {"type": "boolean"}}}, "ItemCard": {"properties": {"itemId": {"type": "string"}, "skuId": {"type": "string"}, "url": {"type": "string"}}}, "ItemLabelTask": {"properties": {"id": {"type": "integer", "format": "int64"}, "insertTime": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "itemPreviousLabelTask": {"type": "object", "description": "this is a `java.lang.Object`"}, "itemPreviousLabelTaskStr": {"type": "string"}, "status": {"type": "integer", "format": "int32"}}}, "ItemLabelTaskSelectReq": {"properties": {"endTime": {"type": "integer", "format": "int64"}, "startTime": {"type": "integer", "format": "int64"}}}, "JSONObject": {"properties": {"map": {"type": "object", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}}}, "KbqaResp": {"properties": {"kbqaType": {"type": "string", "enum": ["SIZE_PIC", "GOODS_PROPERTY"]}, "score": {"type": "number", "format": "double"}, "showText": {"type": "string"}}}, "KnowledgeMatchResp": {"properties": {"matchRslts": {"type": "array", "items": {"$ref": "#/definitions/KnowledgeMatchRslt"}}, "matchType": {"type": "integer", "format": "int32"}}}, "KnowledgeMatchRslt": {"properties": {"answerId": {"type": "integer", "format": "int64"}, "answerUse": {"type": "integer", "format": "int32"}, "cateId": {"type": "integer", "format": "int64"}, "corpusId": {"type": "string"}, "itemId": {"type": "integer", "format": "int64"}, "knowledgeId": {"type": "integer", "format": "int64"}, "question": {"type": "string"}, "score": {"type": "number", "format": "double"}}}, "MainPipelineLog": {"properties": {"context": {"type": "object", "description": "The circular reference: `BotContext`", "x-circular-ref": "BotContext"}, "faqCombine": {"$ref": "#/definitions/FaqIdRslt"}, "hstTextBasicData": {"type": "array", "items": {"$ref": "#/definitions/TextBasicData"}}, "input": {"$ref": "#/definitions/BotReq"}, "intent": {"$ref": "#/definitions/IntentRslt"}, "kbqa": {"$ref": "#/definitions/KbqaResp"}, "match": {"$ref": "#/definitions/KnowledgeMatchResp"}, "other": {"type": "object", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}, "output": {"$ref": "#/definitions/BotResp"}}}, "MessageContent": {"properties": {"itemList": {"type": "array", "items": {"$ref": "#/definitions/ItemCard"}}, "orderList": {"type": "array", "items": {"$ref": "#/definitions/OrderCard"}}, "text": {"type": "string"}, "url": {"type": "string"}}}, "MpsReceiveMessageBean": {"properties": {"messageId": {"type": "string"}, "payload": {"type": "string"}, "product": {"type": "string"}, "topic": {"type": "string"}}}, "MultiChannelPipelineLog": {"properties": {"channelQaAddiReq": {"$ref": "#/definitions/ChannelQaAddiReq"}, "context": {"type": "object", "description": "The circular reference: `BotContext`", "x-circular-ref": "BotContext"}, "hstTextBasicData": {"type": "array", "items": {"$ref": "#/definitions/TextBasicData"}}, "input": {"$ref": "#/definitions/ChannelQaReq"}, "other": {"type": "object", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}, "output": {"$ref": "#/definitions/ChannelQaResp"}}}, "NeedCheckKnowledge": {"properties": {"busCate": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "itemCateLabel": {"type": "string"}, "itemInfoLabel": {"type": "string"}, "kmCate": {"type": "string"}, "knowledgeType": {"type": "integer", "format": "int32"}, "knowledgeUse": {"type": "integer", "format": "int32"}, "similarQuestions": {"type": "array", "items": {"type": "string"}}, "stdQuestion": {"type": "string"}}}, "OrderCard": {"properties": {"orderId": {"type": "string"}, "packageList": {"type": "array", "items": {"type": "object", "description": "this is a `java.lang.Object`"}}, "url": {"type": "string"}}}, "ProphetReq": {"properties": {"device": {"type": "string"}, "itemId": {"type": "integer", "format": "int64"}, "newUser": {"type": "boolean"}, "orderId": {"type": "integer", "format": "int64"}, "platform": {"type": "string"}, "presale": {"type": "boolean"}, "returnFaqNum": {"type": "integer", "format": "int32"}, "sessionId": {"type": "integer", "format": "int64"}, "source": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "userR": {"type": "integer", "format": "int32"}, "userRGCount24H": {"type": "integer", "format": "int32"}, "userRGCount48H": {"type": "integer", "format": "int32"}, "userV": {"type": "integer", "format": "int32"}}}, "QualityInspectionRequest": {"properties": {"sessionInfo": {"type": "array", "items": {"$ref": "#/definitions/TextRoundInfo"}}, "sid": {"type": "integer", "format": "int64"}}}, "QualityInspectionResponse": {"properties": {"problemIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "ReqInstance": {"properties": {"candidates": {"type": "array", "items": {"type": "string"}}, "extra": {"type": "object", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}, "hst": {"type": "string"}, "id": {"type": "string"}, "sen": {"type": "string"}}}, "RGAssistGuideReq": {"properties": {"itemId": {"type": "string"}, "userId": {"type": "string"}}}, "RGAssistGuideTrackReq": {"properties": {"userId": {"type": "integer", "format": "int64"}}}, "RGAssistRcmdReasonReq": {"properties": {"channelId": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}}}, "SearchReq": {"properties": {"channelId": {"type": "integer", "format": "int64"}, "query": {"type": "string"}, "topK": {"type": "integer", "format": "int32"}}}, "SessionResp": {"properties": {"cateResults": {"type": "array", "items": {"$ref": "#/definitions/CateResult"}}, "rcmdType": {"type": "string", "enum": ["TOP_DEFAULT", "MODEL"]}, "sessionLength": {"type": "integer", "format": "int32"}, "state": {"type": "string", "enum": ["SUCCESS", "PARAM_ERROR", "PARAM_NOT_ENOUGH", "INTERNAL_ERROR"]}, "summaryResult": {"$ref": "#/definitions/SummaryResult"}}}, "ShortcutRcmdReq": {"properties": {"historyKnowledgeIds": {"type": "array", "items": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "rcmdNum": {"type": "integer", "format": "int32"}, "sessionId": {"type": "integer", "format": "int64"}, "yxUserId": {"type": "integer", "format": "int64"}}}, "SimpleBulkRequest": {"properties": {"ids": {"type": "array", "items": {"type": "string"}}, "index": {"type": "string"}, "objects": {"type": "array", "items": {"$ref": "#/definitions/JSONObject"}}}}, "SimpleKnnRequest": {"properties": {"candidates": {"type": "integer", "format": "int32"}, "fields": {"type": "array", "items": {"type": "string"}}, "filterTerms": {"type": "array", "items": {"$ref": "#/definitions/FilterTerm"}}, "index": {"type": "string"}, "knnField": {"type": "string"}, "topK": {"type": "integer", "format": "int32"}, "vector": {"type": "array", "items": {"type": "number", "format": "float"}}}}, "SucResp<Object>": {"properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "description": "this is a `java.lang.Object`", "x-generic-type": "T"}}, "x-generic-types": ["T"]}, "SummaryResult": {"properties": {"summary": {"type": "string"}, "summaryConfidence": {"type": "string", "enum": ["EXCELLENT", "GOOD", "NORMAL", "BAD"]}}}, "TextBasicData": {"properties": {"cleanedText": {"type": "string"}, "rawText": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "words": {"type": "array", "items": {"type": "string"}}}}, "TextRoundInfo": {"properties": {"inputText": {"type": "string"}, "isAutoReply": {"type": "integer", "format": "int64"}, "messageCreateTs": {"type": "integer", "format": "int64"}, "msgStatus": {"type": "integer", "format": "int64"}, "staffId": {"type": "integer", "format": "int64"}, "textType": {"type": "integer", "format": "int64"}}}, "UserOrder": {"properties": {"address": {"type": "string"}, "applyId": {"type": "integer", "format": "int64"}, "applyStatus": {"type": "integer", "format": "int64"}, "applyTypeCode": {"type": "integer", "format": "int64"}, "carrier": {"type": "string"}, "confirmTime": {"type": "integer", "format": "int64"}, "count": {"type": "integer", "format": "int64"}, "createTime": {"type": "integer", "format": "int64"}, "globalId": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "itemName": {"type": "string"}, "mbrLevel": {"type": "integer", "format": "int32"}, "orderId": {"type": "integer", "format": "int64"}, "orderStatus": {"type": "integer", "format": "int32"}, "outstoreNo": {"type": "string"}, "outstoreStatus": {"type": "string"}, "packageId": {"type": "string"}, "payTime": {"type": "integer", "format": "int64"}, "realPriceAmount": {"type": "number", "format": "double"}, "receiverCity": {"type": "string"}, "receiverDistrict": {"type": "string"}, "receiverMobile": {"type": "string"}, "receiverName": {"type": "string"}, "receiverProvince": {"type": "string"}, "skuId": {"type": "integer", "format": "int64"}, "spmcStatus": {"type": "integer", "format": "int32"}, "storehouseId": {"type": "integer", "format": "int64"}, "storehouseName": {"type": "string"}, "ticketContentTitle": {"type": "string"}, "ticketDescPlainText": {"type": "string"}, "ticketStatus": {"type": "integer", "format": "int32"}, "trackingNum": {"type": "string"}, "trackingStatus": {"type": "integer", "format": "int64"}, "userCreditLevel": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}}}}, "x-project-version": "1.0-SNAPSHOT", "x-swagger-plugin-version": "2.0.2"}