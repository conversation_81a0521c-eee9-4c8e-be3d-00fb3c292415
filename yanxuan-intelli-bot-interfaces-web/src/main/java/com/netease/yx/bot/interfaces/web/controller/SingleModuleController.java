/**
 * @(#)SingleModuleController.java, 2020/5/14.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.service.ItemService;
import com.netease.yx.bot.biz.service.TextPreprocessService;
import com.netease.yx.bot.biz.service.IntentServiceV2;
import com.netease.yx.bot.biz.manager.InputAssociateManager;
import com.netease.yx.bot.biz.manager.PipelineManager;
import com.netease.yx.bot.biz.manager.ProphetManager;
import com.netease.yx.bot.biz.manager.ShortcutRcmdManager;
import com.netease.yx.bot.common.util.preprocess.Ngram;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.inputAssociate.InputAssociateReq;
import com.netease.yx.bot.core.model.entity.inputAssociate.InputAssociateResp;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import com.netease.yx.bot.core.model.entity.label.ItemLabelTask;
import com.netease.yx.bot.core.model.entity.label.ItemLabelTaskSelectReq;
import com.netease.yx.bot.core.model.entity.preprocess.NgramText;
import com.netease.yx.bot.core.model.entity.prophet.ProphetReq;
import com.netease.yx.bot.core.model.entity.prophet.ProphetResp;
import com.netease.yx.bot.core.model.entity.shourtcut.ShortcutRcmdReq;
import com.netease.yx.bot.core.model.entity.shourtcut.ShortcutRcmdResp;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import com.netease.yx.bot.core.model.mapper.algo.ItemLabelTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ corp.netease.com)
 * 一些独立的模块API
 */
@RestController
@RequestMapping("/yanxuan-intelli-bot")
@Slf4j
public class SingleModuleController {
    private static final Integer PROCESSING_INPUT_ERROR = 400;
    private static final String HTTP_INPUT_ERROR = "Request error!";
    @Autowired
    private ProphetManager prophetManager;
    @Autowired
    private ShortcutRcmdManager shortcutRcmdManager;
    @Autowired
    private InputAssociateManager inputAssociateManager;
    @Autowired
    private EnvService envService;
    @Autowired
    private TextPreprocessService textPreprocessService;
    @Autowired
    private Ngram ngram;
    @Autowired
    private IntentServiceV2 intentServiceV2;

    @Autowired
    private ItemService itemService;

    @Autowired
    private PipelineManager pipelineManager;

    @Autowired
    private ItemLabelTaskMapper itemLabelTaskMapper;

    /**
     * 服务先知接口
     *
     * @param prophetReq
     * @return
     */
    @PostMapping("prophet.do")
    public ResponseEntity<BaseResp> prophet(@RequestBody @Validated ProphetReq prophetReq) {
        prophetReq.setReturnFaqNum(5);
        log.info("Prophet input request={}", prophetReq);
        ProphetResp resp = prophetManager.pipeline(prophetReq);
        log.info("prophet response: {}", resp);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(resp));
    }

    /**
     * @param prophetReq
     * @return
     */
    @PostMapping("usercenter-prophet.do")
    public ResponseEntity<BaseResp> userCenterProphet(@RequestBody @Validated ProphetReq prophetReq) {
        prophetReq.setReturnFaqNum(6);
        ProphetResp resp;

        if (envService.isTest()) {
            String kids = "20143115,20143134,20143132,20143112,20142612,20143145";
            List<Long> knowledgeIds = Arrays.stream(kids.split(",")).map(Long::parseLong).collect(Collectors.toList());
            String shortstrIds = "1,2,3,4,5";
            List<Long> shortIds = Arrays.stream(shortstrIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
            resp = ProphetResp.builder()
                    .knowledgeIds(knowledgeIds)
                    .cardIds(shortIds)
                    .shortcutIds(shortIds)
                    .build();
        } else {
            resp = prophetManager.pipeline(prophetReq);
        }

        log.info("userCenterProphet response: {}", resp);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(resp));
    }

    /**
     * 快捷短语气泡词预测接口
     *
     * @param shortcutRcmdReq
     * @return
     */
    @PostMapping("shortcut_rcmd.do")
    public ResponseEntity<BaseResp> prophet(@RequestBody @Validated ShortcutRcmdReq shortcutRcmdReq) {
        ShortcutRcmdResp resp = shortcutRcmdManager.process(shortcutRcmdReq);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(resp));
    }

    @PostMapping("input_associate.do")
    public ResponseEntity<BaseResp> inputAssociate(@RequestBody @Validated InputAssociateReq inputAssociateReq) {
        InputAssociateResp inputAssociateResp = inputAssociateManager.process(inputAssociateReq);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(inputAssociateResp));
    }

    @PostMapping("text.do")
    public ResponseEntity<BaseResp> pipeline(@RequestParam(value = "content") String query) {
        TextBasicData textInfo = textPreprocessService.preprocess(query, false);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(textInfo));
    }

    @PostMapping("ngram.do")
    public ResponseEntity<BaseResp> ngramPreprocess(@RequestParam(value = "content") String query) {
        NgramText res = ngram.generate(query);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(res));
    }

    @PostMapping("intent_first.do")
    public ResponseEntity<BaseResp> intentFirst(@RequestParam(value = "content") String query) {
        IntentRslt res = intentServiceV2.getIntent(query);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(res));
    }

    @PostMapping("item_base_info.do")
    public ResponseEntity<BaseResp> getSrcId(@RequestParam(value = "itemId") long itemId) {
        long srcId = itemService.getSrcId(itemId);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(srcId));
    }

    @RequestMapping("check_transfer_rg.do")
    public ResponseEntity<BaseResp> checkTransferRg(@RequestParam(value = "sessionId") long sessionId) {
        Map<String, Object> resultMap = new HashMap<>();
        if (envService.isTest()) {
            resultMap.put("transferFlag", sessionId % 2 == 0);
        } else {
            resultMap.put("transferFlag", pipelineManager.checkTransferRg(sessionId));
        }
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(resultMap));
    }

    @RequestMapping("item_label_task_insert.do")
    public ResponseEntity<BaseResp> insert(@RequestBody ItemLabelTask itemLabelTask) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(itemLabelTaskMapper.insert(itemLabelTask)));
    }

    @RequestMapping("item_label_task_select.do")
    public ResponseEntity<BaseResp> select(@RequestBody ItemLabelTaskSelectReq itemLabelTaskSelectReq) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(itemLabelTaskMapper.selectByTime(itemLabelTaskSelectReq)));
    }

    @RequestMapping("item_label_task_update.do")
    public ResponseEntity<BaseResp> select(@RequestBody ItemLabelTask itemLabelTask) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(itemLabelTaskMapper.updateStatus(itemLabelTask)));
    }
}