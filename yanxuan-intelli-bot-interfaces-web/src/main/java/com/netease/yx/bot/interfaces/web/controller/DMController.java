package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.common.EnvService;
import com.netease.yx.bot.biz.applloconfig.BotApolloConfig;
import com.netease.yx.bot.biz.manager.PipelineManager;
import com.netease.yx.bot.biz.manager.TestMockManager;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.core.model.entity.BotReq;
import com.netease.yx.bot.core.model.entity.BotResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 主站APP的机器人问答，接口很老
 */
@RestController
@RequestMapping("/yanxuan-intelli-bot")
@Slf4j
public class DMController {
    @Autowired
    private BotApolloConfig botApolloConfig;
    @Autowired
    private PipelineManager pipelineManager;
    @Autowired
    private EnvService envService;
    @Autowired
    private TestMockManager testMockManager;

    @RequestMapping("yx_bot_kb.do")
    public BotResp process(@RequestParam(value = "content") String jsonStr) {
        try {
            // 参数检查
            BotReq req = null;
            try {
                req = IoUtils.parseJson(jsonStr, BotReq.class);
            } catch (Exception e) {
                log.error("error param. content={}.", jsonStr);
            }
            log.info("req {}", req);

            //json传参转换失败
            if (req == null) {
                return BotResp.buildParamErrorRslt();
            }
            // 正常pipeline
            return pipelineManager.pipeline(req);
        } catch (Exception e) {
            log.error("process", e);
        }

        return BotResp.buildIntalnalErrorRslt();
    }
}
