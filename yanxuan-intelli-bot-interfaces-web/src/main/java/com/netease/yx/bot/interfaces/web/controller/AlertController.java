package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.common.AlertService;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.alert.AlertResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 报警接口
 */
@RestController
@RequestMapping("/yanxuan-intelli-bot")
@Slf4j
public class AlertController {

    @Autowired
    private AlertService alertService;

    @RequestMapping("/checkBotKnowledgeES")
    public ResponseEntity<BaseResp> checkBotKnowledgeES() {
        AlertResp resp = alertService.checkElasticSearch();
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(resp));
    }

    @RequestMapping("/checkBotSmartworkBertVec")
    public ResponseEntity<BaseResp> checkBotSmartworkBertVec() {
        AlertResp resp = alertService.checkBertVectorSmartwork(AlertService.FEATURE_STORE_KEY_BERT_VEC);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(resp));
    }

    @RequestMapping("/checkBotSmartworkBertContextVec")
    public ResponseEntity<BaseResp> checkBotSmartworkBertContextVec() {
        AlertResp resp = alertService.checkBertVectorSmartwork(AlertService.FEATURE_STORE_KEY_BERT_TWO_TURN_VEC);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(resp));
    }
}
