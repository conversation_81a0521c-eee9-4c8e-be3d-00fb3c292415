package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yanxuan.feast.sdk.vo.FeatureRequest;
import com.netease.yanxuan.feast.sdk.vo.FeatureResult;
import com.netease.yx.bot.biz.common.FeatureStoreService;
import com.netease.yx.bot.biz.service.ItemService;
import com.netease.yx.bot.biz.common.NlpServerService;
import com.netease.yx.bot.biz.common.RedisService;
import com.netease.yx.bot.biz.service.qa.ItemAttributeService;
import com.netease.yx.bot.biz.service.qa.FaqMatchPipelineService;
import com.netease.yx.bot.biz.service.qa.recall.RecallServiceWithEs6;
import com.netease.yx.bot.biz.service.qa.recall.RecallServiceWithEs8;
import com.netease.yx.bot.biz.service.sync.ItemAttributeSyncService;
import com.netease.yx.bot.biz.service.sync.KnowledgeSyncService;
import com.netease.yx.bot.biz.manager.ChannelQaManager;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.nlpserver.ReqInstance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报警接口
 */
@RestController
@RequestMapping("/yanxuan-intelli-bot/test")
@Slf4j
public class TestController {
    @Autowired
    private ChannelQaManager channelQaManager;

    @Autowired
    private ItemAttributeService itemAttributeService;

    @Autowired
    private NlpServerService nlpServerService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private KnowledgeSyncService knowledgeSyncService;

    @Autowired
    private ItemAttributeSyncService itemAttributeSyncService;

    @Autowired
    private RecallServiceWithEs8 recallServiceWithEs8;

    @Autowired
    private RecallServiceWithEs6 recallServiceWithEs6;

    @Autowired
    private FaqMatchPipelineService faqMatchPipelineService;

    @Autowired
    private FeatureStoreService featureStoreService;

    @PostMapping("getFeature.do")
    public ResponseEntity<BaseResp> getFeature(@RequestParam(value = "domain") String domain, @RequestParam(value = "featureNames") String featureNames, @RequestParam(value = "ids") String ids) {
        ;
        FeatureRequest request = FeatureRequest.builder().idType(domain).featureNames(Arrays.stream(StringUtils.split(featureNames, ",")).collect(Collectors.toSet())).ids(Arrays.stream(StringUtils.split(ids, ",")).collect(Collectors.toSet())).build();

        Map<String, Map<String, String>> result = new HashMap<>();
        List<FeatureResult> featureResults = featureStoreService.getFeature(request);
        for (FeatureResult featureResult : featureResults) {
            if (featureResult != null) {
                String corpusId = featureResult.getId();
                Map<String, String> feature = featureResult.getFeatures();
                result.put(corpusId, feature);
            }
        }

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(result));
    }

    @PostMapping("similarity.do")
    public ResponseEntity<BaseResp> similarity(@RequestParam(value = "input") String input, @RequestParam(value = "itemId") long itemId, @RequestParam(value = "threshold", defaultValue = "0.7") long threshold) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(itemAttributeService.similarityForAttr(input, itemId, threshold)));
    }

    @PostMapping("chat.do")
    public ResponseEntity<BaseResp> chat(@RequestBody @Validated List<ReqInstance> req) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(nlpServerService.chat(req, null, 8000)));
    }

    @PostMapping("secure.do")
    public ResponseEntity<BaseResp> secure(@RequestParam String sen) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(nlpServerService.checkSecure(sen)));
    }

    @PostMapping("intent.do")
    public ResponseEntity<BaseResp> intent(@RequestParam String sen) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(nlpServerService.intent(sen)));
    }

    @PostMapping("redis.do")
    public ResponseEntity<BaseResp> redisGet(@RequestParam String key) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(redisService.get(key)));
    }

    @PostMapping("redisDelete.do")
    public ResponseEntity<BaseResp> redisDelete(@RequestParam String key) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(redisService.delete(key)));
    }

    @PostMapping("knowledge.do")
    public ResponseEntity<BaseResp> knowledge(@RequestParam long knowledgeId) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(knowledgeSyncService.getById(knowledgeId)));
    }

    @RequestMapping("knowledgeSolution.do")
    public ResponseEntity<BaseResp> knowledgeSolution(@RequestParam long knowledgeId) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(recallServiceWithEs8.hasIntelliSolutionAnswer(knowledgeId)));
    }

    @RequestMapping("attr.do")
    public ResponseEntity<BaseResp> attr(@RequestParam long itemId, @RequestParam long attrId) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(itemAttributeSyncService.getById(itemId, attrId)));
    }

    @RequestMapping("getAnswerId.do")
    public ResponseEntity<BaseResp> getAnswerId(@RequestParam long knowledgeId, @RequestParam long channelId, @RequestParam String platform, @RequestParam long itemId) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(recallServiceWithEs8.getAnswerIdByKnowledgeId(knowledgeId, channelId, platform, itemId)));
    }

    @PostMapping("recallWithEs6.do")
    public ResponseEntity<BaseResp> chat(@RequestBody @Validated BotContext botContext) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(recallServiceWithEs6.recallWithContextMultiChannel(botContext)));
    }

    @PostMapping("processWithSimpleModel.do")
    public ResponseEntity<BaseResp> processWithSimpleModel(@RequestBody @Validated BotContext botContext) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(faqMatchPipelineService.processWithSimpleModel(botContext)));
    }

    @RequestMapping("knowledgeId2outAnswer.do")
    public ResponseEntity<BaseResp> knowledgeId2outAnswer(@RequestParam long knowledgeId) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(recallServiceWithEs8.hasRobotAnswer(knowledgeId)));
    }
}
