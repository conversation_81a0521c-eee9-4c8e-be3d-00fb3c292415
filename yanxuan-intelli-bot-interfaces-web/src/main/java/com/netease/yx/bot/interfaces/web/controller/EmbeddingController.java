package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.service.qa.emb.BertEmbReq;
import com.netease.yx.bot.biz.service.qa.emb.BertEmbeddingManager;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/emb")
public class EmbeddingController {
    private static final Integer MAX_LENGTH = 32;
    private static final String HEADER_VALUE_BERT_EMB_HOST_TYPE = "aibot-match-sort-emb.yx-serving.svc";
    private static final String URL = "http://smart-infer.hz.infra.mail:31938/v1/models/aibot-match-sort-emb:predict";

    @Autowired
    private BertEmbeddingManager bertEmbeddingManager;

    @PostMapping("bert_embedding.do")
    public ResponseEntity<BaseResp> embedding(@RequestBody List<String> sen) {
        BertEmbReq bertEmbReq = new BertEmbReq(sen, MAX_LENGTH, URL, HEADER_VALUE_BERT_EMB_HOST_TYPE);
        return embeddingV2(bertEmbReq);
    }

    @PostMapping("bert_embedding_v2.do")
    public ResponseEntity<BaseResp> embeddingV2(@RequestBody BertEmbReq bertEmbReq) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(bertEmbeddingManager.getEmbedding(bertEmbReq.getSen(),
                bertEmbReq.getMaxLength(), bertEmbReq.getUrl(), bertEmbReq.getHeaderHost())));
    }
}
