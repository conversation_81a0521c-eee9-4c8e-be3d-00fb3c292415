package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.manager.CheckKnowledgeManager;
import com.netease.yx.bot.biz.manager.CheckManualManager;
import com.netease.yx.bot.biz.manager.CheckSimilarQValidManager;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.check.CheckResp;
import com.netease.yx.bot.core.model.entity.check.CheckSimilarKnowledgeReq;
import com.netease.yx.bot.core.model.entity.checkSimilar.CheckSimilarQuestionReq;
import com.netease.yx.bot.core.model.entity.checkSimilar.CheckSimilarSingleResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 各种检查
 * 相似问的有效性检查
 * 知识重复的检查
 */
@RestController
@RequestMapping("/yanxuan-intelli-bot")
@Slf4j
public class CheckController {
    @Autowired
    private CheckManualManager checkManualManager;

    @Autowired
    private CheckKnowledgeManager checkKnowledgeManager;

    @Autowired
    private CheckSimilarQValidManager checkSimilarQValidManager;

    // 检查特定知识是否重复
    @RequestMapping("check_similar_knowledge.do")
    public ResponseEntity<BaseResp> check(@RequestBody CheckSimilarKnowledgeReq checkSimilarKnowledgeReq) {
        CheckResp checkResp = checkKnowledgeManager.process(checkSimilarKnowledgeReq);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(checkResp));
    }

    // 检查相似问是否合法
    // 这个不是线上用的，是离线批量调用，整治知识库使用
    // 可废弃
    @RequestMapping("check_similar_question.do")
    public ResponseEntity<BaseResp> check(@RequestBody CheckSimilarQuestionReq checkSimilarReq) {
        List<CheckSimilarSingleResp> checkResp = checkSimilarQValidManager.process(checkSimilarReq);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(checkResp));
    }

    /**
     * 检查是否是人工
     *
     * @param query
     * @return
     */
    @PostMapping("check_manual.do")
    public ResponseEntity<BaseResp> checkManual(@RequestParam(value = "query") String query) {
        boolean checkRslt = checkManualManager.checkManual(query);

        Map<String, Object> result = new HashMap<>(1);
        result.put("checkManual", checkRslt);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(result));
    }
}
