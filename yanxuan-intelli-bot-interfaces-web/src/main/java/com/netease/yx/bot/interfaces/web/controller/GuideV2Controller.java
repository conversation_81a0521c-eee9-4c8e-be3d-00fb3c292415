package com.netease.yx.bot.interfaces.web.controller;


import com.netease.yx.bot.biz.service.GuideService;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.guide.*;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/yanxuan-intelli-bot")
@Slf4j
public class GuideV2Controller {
    @Autowired
    private GuideService guideService;

    /**
     * 导购主接口
     *
     * @param req
     * @return response
     */
    @PostMapping("guide.do")
    public ResponseEntity<BaseResp> guide(@RequestBody @Validated GuideReq req) {
        GuideRCMDListRslt guideRslt = guideService.getResService(req);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(guideRslt));
    }

    /**
     * 导购主动推荐
     *
     * @param req
     * @return response
     */
    @PostMapping("guide_active.do")
    public ResponseEntity<BaseResp> guideActive(@RequestBody @Validated GuideReq req) {
        GuideRCMDListRslt guideRslt = guideService.getActiveRes(req);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(guideRslt));
    }

    /**
     * 搜索QP接口
     *
     * @param req
     * @return response
     */
    @PostMapping("qp.do")
    public ResponseEntity<BaseResp> qp(@RequestBody @Validated GuideReq req) {
        QPReq qpReq = new QPReq(req.getQuery(), req.getUid());
        QPResp qpResp = guideService.getQPRes(qpReq);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(qpResp));
    }

    /**
     * 搜搜接口调用
     *
     * @param jsonStr
     * @return response
     */
    @PostMapping("search.do")
    public ResponseEntity<BaseResp> search(@RequestParam(value = "content") String jsonStr) {
        log.info("search input " + jsonStr);
        SearchReq req = null;
        try {
            req = IoUtils.parseJson(jsonStr, SearchReq.class);
            log.info("load success");
            log.info("request input " + IoUtils.toJsonString(req));
        } catch (Exception e) {
            log.error("error param. content={}.", jsonStr);
            return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(null));
        }
        log.info("query: " + req.getQuery() + " uid: " + req.getUid());

        SearchResponse searchResponse = guideService.getSearchRes(req);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(searchResponse));
    }

    /**
     * 召回接口调用
     *
     * @param jsonStr
     * @return response
     */
    @PostMapping("rcmd.do")
    public ResponseEntity<BaseResp> rcmd(@RequestParam(value = "content") String jsonStr) {
        log.info("search input " + jsonStr);
        GuideRcmdReq req = new GuideRcmdReq();
        try {
            req = IoUtils.parseJson(jsonStr, GuideRcmdReq.class);
            log.info("load success");
            log.info("request input " + IoUtils.toJsonString(req));
        } catch (Exception e) {
            log.error("error param. content={}.", jsonStr);
            return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(null));
        }
        String query = req.getQuery();
        String recallParam = req.getRecallParam();
        log.info("query: " + query + " recallParam: " + recallParam);

        List<String> rcmdItem = guideService.getRCMD(query, recallParam);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(rcmdItem));
    }

    /**
     * 意图接口调用
     *
     * @param jsonStr
     * @return response
     */
    @PostMapping("guide_intent.do")
    public ResponseEntity<BaseResp> intent(@RequestParam(value = "content") String jsonStr) {
        log.info("search input " + jsonStr);
        GuideIntentReq req = new GuideIntentReq();
        try {
            req = IoUtils.parseJson(jsonStr, GuideIntentReq.class);
            log.info("load success");
            log.info("request input " + IoUtils.toJsonString(req));
        } catch (Exception e) {
            log.error("error param. content={}.", jsonStr);
            return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(null));
        }
        String query = req.getQuery();
        log.info("query: " + query);

        IntentRslt intentRslt = guideService.getIntentRes(query);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(intentRslt));
    }

    /**
     * item 信息
     *
     * @param jsonStr
     * @return response
     */
    @PostMapping("get_item_info.do")
    public ResponseEntity<BaseResp> itemInfo(@RequestParam(value = "content") String jsonStr) {
        log.info("item input " + jsonStr);
        RcmdInfoReq req = new RcmdInfoReq();
        try {
            req = IoUtils.parseJson(jsonStr, RcmdInfoReq.class);
            log.info("load success");
            log.info("request input " + IoUtils.toJsonString(req));
        } catch (Exception e) {
            log.error("error param. content={}.", jsonStr);
            return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(null));
        }
        String query = req.getItemId();
        log.info("query: " + query);

        RcmdItemInfo rcmdItemInfo = guideService.getItem(query);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(rcmdItemInfo));
    }
}
