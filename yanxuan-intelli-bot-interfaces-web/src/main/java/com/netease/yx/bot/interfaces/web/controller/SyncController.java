package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.service.sync.ItemAttributeSyncService;
import com.netease.yx.bot.biz.service.sync.KnowledgeSyncService;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/sync")
public class SyncController {
    @Autowired
    private KnowledgeSyncService knowledgeSyncService;

    @Autowired
    private ItemAttributeSyncService itemAttributeSyncService;

    // 同步FAQ知识
    @RequestMapping("knowledge.do")
    public ResponseEntity<BaseResp> knowledge(@RequestParam long knowledgeId) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(knowledgeSyncService.sync(knowledgeId)));
    }

    // 同步属性数据
    @RequestMapping("attr.do")
    public ResponseEntity<BaseResp> attr(@RequestParam long itemId, @RequestParam long attrId) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(itemAttributeSyncService.sync(itemId, attrId)));
    }
}
