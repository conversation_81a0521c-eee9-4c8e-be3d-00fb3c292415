package com.netease.yx.bot.interfaces.web.controller;


import com.netease.yx.bot.biz.manager.RGAssistManager;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.rgAssist.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人工辅助
 */
@RestController
@RequestMapping("/yanxuan-intelli-bot")
@Slf4j
public class RGAssistController {
    @Autowired
    private RGAssistManager rgAssistManager;

    @PostMapping("rg_assist_guide.do")
    public ResponseEntity<BaseResp> guide(@RequestBody @Validated RGAssistGuideReq RGAssistGuideReq) {
        log.info("rg_guide request: {}", RGAssistGuideReq);
        RGAssistGuideResp resp = rgAssistManager.guidePipeline(RGAssistGuideReq);
        log.info("rg_guide response: {}", resp);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(resp));
    }

    @PostMapping("rg_assist_guide_scan.do")
    public ResponseEntity<BaseResp> guide(@RequestBody @Validated RGAssistGuideTrackReq RGAssistGuideTrackReq) {
        log.info("rg_guide_scan request: {}", RGAssistGuideTrackReq);
        RGAssistGuideTrackResp resp = rgAssistManager.guideTrackPipeline(RGAssistGuideTrackReq);
        log.info("rg_guide_scan response: {}", resp);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(resp));
    }

    // 推荐理由
    @PostMapping("rg_assist_guide_rcmd_reason.do")
    public ResponseEntity<BaseResp> rcmd(@RequestBody @Validated RGAssistRcmdReasonReq rgAssistRcmdReasonReq) {
        log.info("rg_assist_guide_rcmd_reason request: {}", rgAssistRcmdReasonReq);
        String resp = rgAssistManager.genRcmdReason(rgAssistRcmdReasonReq);
        RGAssistRcmdReasonResp rgAssistRcmdReasonResp = new RGAssistRcmdReasonResp(resp);
        log.info("rg_assist_guide_rcmd_reason response: {}", rgAssistRcmdReasonResp);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(rgAssistRcmdReasonResp));
    }
}
