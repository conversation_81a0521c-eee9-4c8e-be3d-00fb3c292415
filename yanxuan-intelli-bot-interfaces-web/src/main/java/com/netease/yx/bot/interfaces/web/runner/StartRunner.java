package com.netease.yx.bot.interfaces.web.runner;

import com.netease.yx.bot.biz.manager.ChannelQaManager;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class StartRunner implements ApplicationRunner {
    @Autowired
    private ChannelQaManager channelQaManager;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 主动触发一下请求，因为服务开启头几个请求，很容易请求超时，导致出现问题
        log.info("这个是测试ApplicationRunner接口");
        ChannelQaReq channelQaReq = new ChannelQaReq("tb", 6801386, "111", "123", "abc", "xxx", 111, "测试启动", 5, true, 1, 1, null, 0, 0, false, 3);
        log.info(channelQaReq.toString());
        // {"channelId":6801386,"sessionId":20232970 ,"rawMsg":"物流轨迹","platformMessageId":"21278","platformId": "tb","messageSource":1,"messageType":1}
        channelQaManager.pipeline(channelQaReq);
    }
}

