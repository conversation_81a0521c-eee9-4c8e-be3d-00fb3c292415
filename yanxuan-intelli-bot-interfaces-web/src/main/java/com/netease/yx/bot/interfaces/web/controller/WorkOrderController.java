package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.service.unuse.WorkOrderClassifyService;
import com.netease.yx.bot.core.model.entity.workbench.SessionResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 七鱼客服工作台相关
 */
@RestController
@RequestMapping("workbench")
@Slf4j
public class WorkOrderController {
    @Autowired
    private WorkOrderClassifyService workOrderClassifyService;

    // 会话分类实时推荐
    // 接口功能不再维护，但是不能删除
    @RequestMapping("classify.do")
    public SessionResp doPost(@RequestParam(value = "content") String content) {
        return workOrderClassifyService.classify(content);
    }
}
