package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.common.es8.Es8Service;
import com.netease.yx.bot.biz.common.es8.SimpleBulkRequest;
import com.netease.yx.bot.biz.common.es8.SimpleKnnRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@RequestMapping("es/")
public class ElasticController {
    @Autowired
    private Es8Service es8Service;

    @RequestMapping("/index/delete")
    public ResponseEntity delete(@RequestBody SimpleBulkRequest simpleBulkRequest) throws IOException {
        return new ResponseEntity(es8Service.delete(simpleBulkRequest.getIndex()), HttpStatus.OK);
    }

    @RequestMapping("/bulk/insert")
    public ResponseEntity main(@RequestBody SimpleBulkRequest simpleBulkRequest) throws IOException {
        return new ResponseEntity(es8Service.bulk(simpleBulkRequest), HttpStatus.OK);
    }

    @RequestMapping("/search/matchAll")
    public ResponseEntity matchAll(@RequestBody SimpleKnnRequest simpleKnnRequest) throws IOException {
        return new ResponseEntity(es8Service.matchAll(simpleKnnRequest.getIndex()), HttpStatus.OK);
    }

    @RequestMapping("/search/knn")
    public ResponseEntity knn(@RequestBody SimpleKnnRequest simpleKnnRequest) throws IOException {
        return new ResponseEntity(es8Service.knnSearch(simpleKnnRequest), HttpStatus.OK);
    }

    @RequestMapping("/search/search")
    public ResponseEntity search(@RequestBody SimpleKnnRequest simpleKnnRequest) throws IOException {
        return new ResponseEntity(es8Service.search(simpleKnnRequest), HttpStatus.OK);
    }
}
