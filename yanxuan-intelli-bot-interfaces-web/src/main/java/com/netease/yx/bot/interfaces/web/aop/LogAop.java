/**
 * @(#)ResponseAop.java, 2020/4/9.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.interfaces.web.aop;

import com.netease.yx.bot.common.util.IoUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> @ corp.netease.com)
 */
@Aspect
@Component
@Slf4j
public class LogAop {

    /**
     * 以 controller 包下定义的所有请求为切入点
     */
    @Pointcut("execution(public * com.netease.yx.bot.interfaces.web.controller..*.*(..))")
    public void logAop() {
    }

    /**
     * 在切点之前织入
     *
     * @param joinPoint
     * @throws Throwable
     */
    @Before("logAop()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
        // 打印请求入参
        log.info("request : {} {}", joinPoint.toShortString(), IoUtils.toJsonString(joinPoint.getArgs(), false));
    }


    @Around("logAop()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        // 执行中间的所有的结果
        Object result = proceedingJoinPoint.proceed();
        log.info("response : {} ; time is {} ", IoUtils.toJsonString(result, false), System.currentTimeMillis() - startTime);
        return result;
    }
}