package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.manager.BatchChannelQaManager;
import com.netease.yx.bot.biz.manager.ChannelQaManager;
import com.netease.yx.bot.biz.manager.ChannelSearchManager;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.channel.BatchChannelQaReq;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaAddiReq;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaReq;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaResp;
import com.netease.yx.bot.core.model.entity.search.SearchReq;
import com.netease.yx.bot.core.model.entity.search.SearchResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 全渠道商品搜索，实时问答
 */
@RestController
@RequestMapping("/yanxuan-intelli-bot")
@Slf4j
public class ChannelController {
    @Autowired
    private ChannelQaManager channelQaManager;

    @Autowired
    private ChannelSearchManager channelSearchManager;

    @Autowired
    private BatchChannelQaManager batchChannelQaManager;

    // 跨渠道商品搜索
    @RequestMapping("multi_channel_search.do")
    public ResponseEntity<BaseResp> check(@RequestBody SearchReq searchReq) {
        SearchResp searchResp = channelSearchManager.process(searchReq);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(searchResp));
    }

    // 跨渠道问答
    @PostMapping("multi_channel_qa.do")
    public ResponseEntity<BaseResp> multiChannelQa(@RequestBody @Validated ChannelQaReq channelQaReq) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(channelQaManager.pipeline(channelQaReq)));
    }

    // 跨渠道问答， 批量输入，用于生成训练任务
    @PostMapping("batch_multi_channel_qa.do")
    public ResponseEntity<BaseResp> batchMultiChannelQaForTrain(@RequestBody @Validated BatchChannelQaReq batchChannelQaReq) {
        List<ChannelQaResp> respList = batchChannelQaManager.process(batchChannelQaReq);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(respList));
    }

    // 跨渠道问答辅助输入， 比如客服搜索选择了某个商品后，该商品会作为该会话的实时问答辅助输入，确定会话的itemId
    @PostMapping("multi_channel_qa_addi.do")
    public ResponseEntity<BaseResp> multiChannelQaAddi(@RequestBody @Validated ChannelQaAddiReq channelQaReq) {
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(channelQaManager.processAddi(channelQaReq)));
    }
}
