package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.service.ChatService;
import com.netease.yx.bot.biz.service.IntentProductService;
import com.netease.yx.bot.common.util.IoUtils;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import com.netease.yx.bot.core.model.entity.CoreIntent;
import com.netease.yx.bot.core.model.entity.chat.ChatClassifyReq;
import com.netease.yx.bot.core.model.entity.chat.ChatType;
import com.netease.yx.bot.core.model.entity.intentProduct.ClassifyHierarchyTFResp;
import com.netease.yx.bot.core.model.entity.intentProduct.FewShotTFResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/yanxuan-intelli-bot")
@Slf4j
public class IntentProductController {

    @Autowired
    private IntentProductService intentProductService;

    @Autowired
    private ChatService chatService;

    /**
     * 售后意图分类
     *
     * @param query
     * @return response
     */
    @PostMapping("intent_product.do")
    public ResponseEntity<BaseResp> intentProduct(@RequestParam(value = "query") String query) {
        List<CoreIntent> intentProductResponse = intentProductService.getIntentProduct(query);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(intentProductResponse));
    }

    /**
     * 售后意图分类AB版本
     *
     * @param query
     * @return response
     */
    @PostMapping("intent_product_ver.do")
    public ResponseEntity<BaseResp> intentProductVer(@RequestParam(value = "query") String query) {
        List<CoreIntent> intentProductResponse = intentProductService.getCoreIntentByVersion(query);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(intentProductResponse));
    }

    /**
     * 闲聊分类
     *
     * @param req
     * @return response
     */
    @PostMapping("chat_classify.do")
    public ResponseEntity<BaseResp> chatClassify(@RequestBody @Validated ChatClassifyReq req) {
        ChatType chatType = chatService.getChitChatSmartHostRes(req);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(chatType));
    }


    /**
     * 售后分类服务
     *
     * @param query
     * @return response
     */
    @PostMapping("intent_product_cls.do")
    public ResponseEntity<BaseResp> intentProductCls(@RequestParam(value = "query") String query) {
        String smartworkInput = intentProductService.genRequestInput(query);
        ClassifyHierarchyTFResp output = intentProductService.getClassifyTFRes(smartworkInput);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(IoUtils.toJsonString(output)));
    }

    /**
     * 售后fewshot服务
     *
     * @param query
     * @return response
     */
    @PostMapping("intent_product_fs.do")
    public ResponseEntity<BaseResp> intentProductFs(@RequestParam(value = "query") String query) {
        String smartworkInput = intentProductService.genRequestInput(query);
        FewShotTFResp output = intentProductService.getFewShotTFRes(smartworkInput);

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(IoUtils.toJsonString(output)));
    }
}
