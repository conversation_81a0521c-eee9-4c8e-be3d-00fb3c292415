package com.netease.yx.bot.interfaces.web;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Consul健康检查服务
 * 由于历史问题，两个health接口都需要保留
 *
 * <AUTHOR>
 * @date 2018/5/3.
 */
@RestController
public class HealthCheckController {

    /**
     * 健康检查接口
     *
     * @return HttpStatus.OK 表示成功
     */
    @RequestMapping("/i/health")
    public ResponseEntity checkHealth() {
        return new ResponseEntity(HttpStatus.OK);
    }


    @RequestMapping("/yanxuan-intelli-bot/health.do")
    public Map<String, Integer> doPost() {
        Map<String, Integer> ret = new HashMap<>();
        ret.put("code", 200);
        return ret;
    }
}
