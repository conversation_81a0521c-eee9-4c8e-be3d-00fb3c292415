/**
 * @(#)QualityInspection.java, 2021/12/10.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.interfaces.web.controller;

import com.netease.yx.bot.biz.service.unuse.assistant.QualityInsepctionManager;
import com.netease.yx.bot.core.model.entity.qualityInspection.QualityInspectionRequest;
import com.netease.yx.bot.core.model.entity.qualityInspection.QualityInspectionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 人工质检
 * 接口保留，但不处理具体逻辑
 */

@RestController
@RequestMapping("/yanxuan-intelli-bot")
@Slf4j
public class QualityInspectionController {

    @Autowired
    private QualityInsepctionManager qualityInsepctionManager;


    @PostMapping("quality_inspection.do")
    @ResponseBody
    public QualityInspectionResponse qualityInspection(@RequestBody QualityInspectionRequest request) {
        //sample1
        return new QualityInspectionResponse();
    }
}
