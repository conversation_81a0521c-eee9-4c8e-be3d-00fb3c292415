package com.netease.yx.bot.interfaces.web.controller;

import com.alibaba.fastjson.JSON;
import com.netease.yx.bot.biz.common.mps.MpsDispatcher;
import com.netease.yx.bot.biz.common.mps.MpsReceiveMessageBean;
import com.netease.yx.bot.biz.common.mps.MpsSender;
import com.netease.yx.bot.biz.service.mps.handler.SmartSessionCloseMsgHandler;
import com.netease.yx.bot.biz.service.mps.handler.TrainChangeMsgHandler;
import com.netease.yx.bot.common.util.web.BaseResp;
import com.netease.yx.bot.common.util.web.SucResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * mps消息处理相关
 */
@RestController
@Slf4j
@RequestMapping("/api/mps/")
public class MpsMessageApiController {
    @Autowired
    private MpsDispatcher mpsDispatcher;

    @Autowired
    private MpsSender mpsSender;

    @Autowired
    private SmartSessionCloseMsgHandler smartSessionCloseMsgHandler;

    @Autowired
    private TrainChangeMsgHandler trainChangeMsgHandler;

    /**
     * 接收MPS消息
     *
     * @param messages 消息
     */
    @PostMapping(value = "receive")
    public SucResp receive(@RequestBody List<MpsReceiveMessageBean> messages) {
        log.info("[op:receive] messages={}", JSON.toJSONString(messages));
        mpsDispatcher.onMessage(messages);
        return new SucResp(null);
    }

    @PostMapping("mpsSend.do")
    public ResponseEntity<BaseResp> mpsSend(@RequestBody MpsReceiveMessageBean mpsReceiveMessageBean) {

        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(mpsSender.sendMps(mpsReceiveMessageBean.topic, mpsReceiveMessageBean.payload)));
    }

    @PostMapping("smartSessionClose.do")
    public ResponseEntity<BaseResp> smartSessionClose(@RequestBody MpsReceiveMessageBean mpsMessage) {
        smartSessionCloseMsgHandler.process(mpsMessage);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(1));
    }

    @PostMapping("trainChange.do")
    public ResponseEntity<BaseResp> trainChange(@RequestBody MpsReceiveMessageBean mpsMessage) {
        trainChangeMsgHandler.process(mpsMessage);
        return ResponseEntity.status(HttpStatus.OK).body(new SucResp<>(1));
    }
}
