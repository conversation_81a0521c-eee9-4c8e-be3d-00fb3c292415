#### 细粒度意图

细粒度意图主要用于售后场景的意图识别

##### 字段

输入

```json
{
    "query": string, 
    "version": string
}

```

输出

```json
{
  "code":200,
  "data":[
    {
      "idx":int,                # 排序
      "intentCh":string,        # 意图中文名
      "prob":float,             # 概率
      "intent":string,          # 意图
      "confidence":string,      # 置信度
      "knowledgeType":string,
      "knowledgeId":int,
      "strongConfidence":bool,  
      "sop":bool                # 是否sop意图
      }
  ]
}
```

##### 调用方式

一般情况嵌入PipelineService中使用，单独调用请使用：

```shell
# release
curl -d 'query=为什么还没送到' -d 'version=B' -X POST 'http://**************:8083/yanxuan-intelli-bot/intent_product_ver.do'
```

```shell
# output
{"code":200,"data":[{"idx":1,"intentCh":"催派送","prob":0.9900000095367432,"intent":"express_urgeDispatch","confidence":"strong","knowledgeType":"","knowledgeId":-1,"strongConfidence":true,"sop":false}]}```
```