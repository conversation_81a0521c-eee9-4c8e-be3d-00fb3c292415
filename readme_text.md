#### 智能客服机器人文本预处理

为了保证智能客服对文本的处理的粒度一致，统一提供一个对外的接口给知识同步工程和Faq工程调用

##### 字段

URL参数：

```java
String content
```

输出：

```java
public class TextBasicData {
    /**
     * 原始文本
     */
    private String rawText;
    /**
     * 经过文本格式化、文本规范化后的文本
     */
    private String cleanedText;
    /**
     * 分词
     */
    private List<String> words = new ArrayList<String>();
    /**
     * 词性标注
     */
    private List<String> tags = new ArrayList<String>();

    /**
     * ner识别的属性名
     */
    private List<String> attrName = new ArrayList<String>();
    /**
     * ner识别的属性值
     */
    private List<String> attrValue = new ArrayList<String>();
    /**
     * ner识别的实体词
     */
    private List<String> entity = new ArrayList<String>();
    /**
     * 完整的ner
     */
    private NerRslt nerRslt;
}
```

##### 调用方式

一般情况嵌入PipelineService中使用，单独调用请使用：

```shell
# release
curl  -d 'content=“这件连衣裙，我想退货”' "http://**************:8083/yanxuan-intelli-bot/text.do?"
```

```shell
# output
{"code":200,"data":{"rawText":"“这件连衣裙，我想退货”","cleanedText":" 这件连衣裙 我想退货 ","words":["这","件","连衣裙","我","想","退货"],"tags":["r","q","n","n","v","v"],"attrName":null,"attrValue":null,"entity":null,"nerRslt":{"attrName":[],"attrValue":[],"entity":["连衣裙"],"state":null}}}```
```

> ner 相关的字段目前还没有使用
