package com.netease.yx.bot.core.model.entity.qualityInspection;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = ComplaintTokens.class)
@TestPropertySource("classpath:server-config-test.properties")
class ComplaintTokensTest {
    @Autowired
    ComplaintTokens complaintTokens;

    @Test
    void getWords() {
        System.out.println((Arrays.toString(complaintTokens.getWords())));
        assertThat(Arrays.equals(complaintTokens.getWords(), new String[]{"315", "消费者协会"})).isTrue();

    }

    @Test
    void hasComplainttest() {
        System.out.println((Arrays.toString(complaintTokens.getWords())));
        boolean res = false;
        String singleRound = "绯闻绯闻3 15绯闻废弃物";
        for (String singleWord : new String[]{"315", ""}) {
            if (singleWord.length() == 0) continue;
            if (singleRound.contains(singleWord)) {
                res = true;
                break;
            }
        }
        assertEquals(false, res);

    }
}