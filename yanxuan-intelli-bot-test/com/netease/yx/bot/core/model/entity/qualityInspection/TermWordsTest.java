package com.netease.yx.bot.core.model.entity.qualityInspection;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = TermWords.class)
@TestPropertySource("classpath:server-config-test.properties")
class TermWordsTest {
    @Autowired
    TermWords termWords;

    @Test
    void getTermwords() {
        String inputText = "工Pop单";
        boolean res = false;
        //特殊专业词汇
        for (String item : termWords.termwords) {
            if (item.length() == 0) continue;
            if (inputText.toLowerCase().contains(item.toLowerCase())) {
                res = true;
            }
        }
        assertEquals(false, res);

    }


}