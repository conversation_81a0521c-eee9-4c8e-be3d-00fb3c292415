package com.netease.yx.bot.core.model.entity.qualityInspection;

import org.checkerframework.checker.units.qual.A;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */

@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = PolarAnalysisSetting.class)
@TestPropertySource("classpath:server-config-test.properties")
class PolarAnalysisSettingTest {
    @Autowired
    PolarAnalysisSetting polarAnalysisSetting;

    @Test
    void getUrl() {
        assertEquals(polarAnalysisSetting.getUrl(), "http://smart-infer.hz.infra.mail:31938/v1/models/nlp-diting-comment-polar:predict");
        assertEquals(polarAnalysisSetting.getHost(), "nlp-diting-comment-polar.yx-serving.svc");
    }

}