package com.netease.yx.bot.core.model.entity.qualityInspection;

import com.alibaba.fastjson.JSONValidator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = NegativeQaThreshold.class)
@TestPropertySource("classpath:server-config-test.properties")
class NegativeQaThresholdTest {

    @Autowired
    NegativeQaThreshold negativeQaThreshold;

    @Test
    void getThreshold() {
        List<String> staffDialog = new ArrayList<>();
        //staffDialog.add("的午饭晚饭访问");
        //staffDialog.add("饿疯惹人个");

        JSONValidator validator = JSONValidator.from("{\"cmd\":65,\"content\":\"[可怜]\"}");
        System.out.println(validator.validate());

        String[] strlist = staffDialog.toArray(new String[0]);
        System.out.println(strlist.length);
        System.out.println(strlist[0]);
        System.out.println(strlist[1]);
        System.out.println(((negativeQaThreshold.getThreshold())));
        assertEquals(negativeQaThreshold.getThreshold(), 2.0);
    }

    @Test
    void teststr() {


        String inputText = "dev.test";
        Pattern p = Pattern.compile("^dev.*$");
        Matcher m = p.matcher(inputText);
        assertEquals(true, m.find());

    }
}