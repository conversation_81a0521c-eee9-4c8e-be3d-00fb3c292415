package com.netease.yx.bot.core.model.entity.qualityInspection;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */
/*@ExtendWith(SpringExtension.class)
//@EnableConfigurationProperties(value = CorrectionSetting.class)
//@TestPropertySource("classpath:src/main/resources/application.yml")*/
@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = CorrectionSetting.class)
@TestPropertySource("classpath:server-config-test.properties")
class CorrectionSettingTest {
    @Autowired
    public CorrectionSetting correctionSetting;

    @Test
    void getUrl() {
        System.out.println(correctionSetting);
        //System.out.println(correctionSetting.getUrl());
        //assertEquals("http://smart-infer.hz.infra.mail:31938/v1/models/mac-bert-text-correction:predict",(correctionSetting.getUrl()));
    }
}