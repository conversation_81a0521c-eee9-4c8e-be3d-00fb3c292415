package com.netease.yx.bot.core.model.entity.qualityInspection;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = AppeasingTokens.class)
@TestPropertySource("classpath:server-config-test.properties")
class AppeasingTokensTest {
    @Autowired
    public AppeasingTokens appeasingTokens;

    @Test
    void getSamples() {

        System.out.println((Arrays.toString(appeasingTokens.getSamples())));
        assertThat(Arrays.equals(appeasingTokens.getSamples(), new String[]{"非常抱歉给您带来了不愉快的购物体验", "真的很庆幸，能遇到您这么豁达的客人。"})).isTrue();
    }
}