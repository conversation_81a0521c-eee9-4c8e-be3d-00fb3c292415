package com.netease.yx.bot.core.model.entity.qualityInspection;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = SpecialTokens.class)
@TestPropertySource("classpath:server-config-test.properties")
class SpecialTokensTest {
    @Autowired
    SpecialTokens specialTokens;

    @Test
    void getRegexString() {
        assertEquals(specialTokens.getRegexString(), "?{3,}|(.{3,})");
    }


}