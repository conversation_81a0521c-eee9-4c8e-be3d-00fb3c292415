package com.netease.yx.bot.biz.service;

import com.alibaba.fastjson.JSON;
import com.netease.yx.bot.core.model.entity.qualityInspection.CorrectionSetting;
import com.netease.yx.bot.core.model.entity.qualityInspection.PolarAnalysisResponse;
import com.netease.yx.bot.core.model.entity.qualityInspection.PolarAnalysisSetting;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */

@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = PolarAnalysisSetting.class)
@TestPropertySource("classpath:server-config-test.properties")
class QualityInspectionPloarAnalysisManagerTest {

    @Autowired
    PolarAnalysisSetting polarAnalysisSetting;

    QualityInspectionPloarAnalysisManager qualityInspectionPloarAnalysisManager = new QualityInspectionPloarAnalysisManager();

    @Test
    void getPloarScore() {
        //异常
        System.out.println(polarAnalysisSetting);
        System.out.println(qualityInspectionPloarAnalysisManager);
        PolarAnalysisResponse polarAnalysisResponse = qualityInspectionPloarAnalysisManager.getPloarScore("我很好呢！", "123");
        System.out.println("polarAnalysisResponse" + polarAnalysisResponse);
        if (polarAnalysisResponse == null) {
            assertEquals(true, true);
        } else {
            assertEquals(true, polarAnalysisResponse.getGoodScore() < 0.5);
        }
    }

    @Test
    void getPloarScore_v2() {
        //正常回复
        System.out.println(polarAnalysisSetting);
        System.out.println(qualityInspectionPloarAnalysisManager);

        String outputs = "{\"uniqueId\": \"1234\", \"type\": \"xx\", \"content\": \"\\u771f\\u7684\\u5f88\\u5dee\\uff0c\\u592a\\u5dee\\u4e86\", \"goodScore\": 2.3992921342141926e-05, \"badScore\": 0.9999760389328003, \"others\": {\"CommentPolarVersion\": \"0.1.0\"}}";

        PolarAnalysisResponse polarAnalysisResponse = JSON.parseObject(outputs, PolarAnalysisResponse.class);
        System.out.println("polarAnalysisResponse" + polarAnalysisResponse);
        if (polarAnalysisResponse == null) {
            assertEquals(true, true);
        } else {
            assertEquals(false, polarAnalysisResponse.getGoodScore() > 0.5);
        }
    }
}