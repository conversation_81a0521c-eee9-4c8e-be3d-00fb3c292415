package com.netease.yx.bot.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.netease.yx.bot.core.model.entity.qualityInspection.AppeasingTokens;
import com.netease.yx.bot.core.model.entity.qualityInspection.CorrectionResponse;
import com.netease.yx.bot.core.model.entity.qualityInspection.CorrectionSetting;
import com.netease.yx.bot.core.model.entity.qualityInspection.SpecialTokens;
import com.sun.tools.javac.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */

@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = CorrectionSetting.class)
@TestPropertySource("classpath:server-config-test.properties")
class QualityInspectionCorrectionManagerTest {
    @Autowired
    public CorrectionSetting correctionSetting;

    public SpecialTokens specialTokens = new SpecialTokens();
    QualityInspectionCorrectionManager qualityInspectionCorrectionManager = new QualityInspectionCorrectionManager();

    @Test
    void correctionRst() {
        System.out.println(correctionSetting);
        System.out.println(qualityInspectionCorrectionManager);
        assertEquals(false, qualityInspectionCorrectionManager.correctionRst(new String[]{"我很好"}));
    }

    @Test
    void resparsing() {
        CorrectionResponse correctionResponse = null;
        String outputJson = "{'predictions': [['你找到你最喜欢的工作，我也很高兴。', []], ['便携鞋子收纳袋', []]]}";
        //outputJson ="{'predictions': [['你找到你最喜欢的工作，我也很高兴。', [['心', '兴', 15, 16]]], ['便携鞋子收纳袋', []]]}";
        correctionResponse = JSON.parseObject(outputJson, CorrectionResponse.class);
        System.out.println("correctionResponse" + correctionResponse.toString());
        System.out.println("correctionResponse" + correctionResponse.getPredictions());
        boolean res = false;
        for (Object obj : correctionResponse.getPredictions()) {
            JSONArray strarray = ((JSONArray) (qualityInspectionCorrectionManager.convertObjectToList(obj).get(1)));
            System.out.println(strarray);
            if (strarray.size() > 0) {
                res = true;
                break;
            }
        }
        assertEquals(false, res);
    }


    @Test
    void hasSpecialToken() {

        System.out.println("inputText.matches(regx):" + specialTokens.getRegexString());


        String inputText = "访问12342??法国突然好发热绯闻....";
        Pattern p = Pattern.compile(specialTokens.getRegexString());
        Matcher m = p.matcher(inputText);
        assertEquals(true, m.find());

        inputText = "访问12342??法国突然好发热绯闻...";
        p = Pattern.compile(specialTokens.getRegexString());
        m = p.matcher(inputText);
        assertEquals(true, m.find());

        inputText = "访问12342??法国突然好发热绯闻..";
        p = Pattern.compile(specialTokens.getRegexString());
        m = p.matcher(inputText);
        assertEquals(false, m.find());

        inputText = "访问12342???法国突然好发热绯闻..";
        p = Pattern.compile(specialTokens.getRegexString());
        m = p.matcher(inputText);
        assertEquals(true, m.find());

        inputText = "访问12342????法国突然好发热绯闻..";
        p = Pattern.compile(specialTokens.getRegexString());
        m = p.matcher(inputText);
        assertEquals(true, m.find());


    }
}