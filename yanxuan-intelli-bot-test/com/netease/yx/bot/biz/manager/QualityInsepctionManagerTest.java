package com.netease.yx.bot.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.hankcs.hanlp.classification.tokenizers.HanLPTokenizer;
import com.netease.yx.bot.core.model.entity.qualityInspection.NegativeQaThreshold;
import com.netease.yx.bot.core.model.entity.qualityInspection.QualityInspectionRequest;
import com.netease.yx.bot.core.model.entity.qualityInspection.TermWords;
import com.netease.yx.bot.core.model.entity.qualityInspection.TextRoundInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(value = NegativeQaThreshold.class)
@TestPropertySource("classpath:server-config-test.properties")
class QualityInsepctionManagerTest {

    private final HanLPTokenizer hanlpTokenizer = new HanLPTokenizer();

    TermWords termWords;
    @Autowired
    NegativeQaThreshold negativeQaThreshold;

    @Test
    void termMatching() {
        // 文本分词
        String inputText = "你这个工单我不行呀";
        String[] termArray = {"你这", "工", "风控"};
        List<String> termList = Arrays.asList(termArray);
        //特殊专业词汇
        boolean res = false;
        for (String item : termWords.termwords) {
            if (termList.contains(item.toLowerCase())) {
                res = true;
                break;
            }
        }
        assertEquals(true, res);

    }

    List<TextRoundInfo> createinput() {
        List<TextRoundInfo> inputs = new ArrayList<TextRoundInfo>();
        String[] inputtextlist = new String[]{"说的是啥", "范围广范围广", "合肥绯闻"};
        int[] texttypelist = new int[]{0, 0, 0};
        int[] staffId = new int[]{4355, 4355, 4355};
        int[] msgStatus = new int[]{1, 1, 1};
        int[] isAutoReply = new int[]{0, 0, 0};
        int[] messageCreateTs = new int[]{1423423, 532535, 46324};
        for (int i = 0; i < 3; i++) {
            TextRoundInfo textRoundInfo = new TextRoundInfo();
            textRoundInfo.setInputText(inputtextlist[i]);
            textRoundInfo.setIsAutoReply((long) isAutoReply[i]);
            textRoundInfo.setMessageCreateTs((long) messageCreateTs[i]);
            textRoundInfo.setMsgStatus((long) msgStatus[i]);
            textRoundInfo.setStaffId((long) staffId[i]);
            textRoundInfo.setTextType((long) texttypelist[i]);

            inputs.add(textRoundInfo);
        }
        return inputs;
    }

    @Test
    void preprocesstest() {
        QualityInspectionRequest qualityInspectionRequest = new QualityInspectionRequest();


        qualityInspectionRequest.setSid(12003);
        qualityInspectionRequest.setSessionInfo(createinput());
        qualityInspectionRequest.getSessionInfo().sort(Comparator.comparingLong(TextRoundInfo::getMessageCreateTs));
        System.out.println(qualityInspectionRequest.getSessionInfo());


    }


    // 一问一答判定
    @Test
    void isNegativeQatest() {
        QualityInspectionRequest qualityInspectionRequest = new QualityInspectionRequest();


        qualityInspectionRequest.setSid(12003);
        qualityInspectionRequest.setSessionInfo(createinput());
        int userInputCount = 0;
        int staffInputCount = 0;
        for (TextRoundInfo singleRound : qualityInspectionRequest.getSessionInfo()) {
            // 用户
            if (singleRound.getTextType() == 0) {
                if ((singleRound.getMsgStatus() == 1)) userInputCount++;
            } else {//客服
                if ((singleRound.getStaffId() != -1) &
                        (singleRound.getMsgStatus() == 1) & (singleRound.getIsAutoReply() == 0)) staffInputCount++;
            }
        }
        System.out.println((float) (staffInputCount / userInputCount));
        assertEquals(true, (float) (staffInputCount / userInputCount) < negativeQaThreshold.getThreshold());
    }

    @Test
    void isContainCardTest() {
        String singleRound = "{\"cardInfo\":{\"name\":\"小选为您推荐\",\"images\":[{\"name\":\"防晒防紫外线偏光宝宝儿童太阳镜\",\"desc\":\"优质TPEE材质，柔软度好，耐折不易变形，幼童也不用担心伤害。\",\"picture\":\"https://yanxuan-item.nosdn.127.net/546caf781c55d9b6667065bac23bf010.png\",\"price\":99,\"url\":\"yanxuan://commoditydetails?commodityid=4007337\",\"kefuUrl\":\"https://you.163.com/item/detail?id=4007337\",\"afterSaleDesc\":\"好评率100%\"}],\"desc\":\"\\\"炫酷时尚 安全健康~偏光太阳镜片，有效阻隔有害光线，更好保护眼睛。；优质TPEE材质，柔软度好，耐折不易变形，不怕伤害；佩戴舒适，不易掉色，大脸不夹，小脸不掉\\\"\"},\"cmd\":802,\"sessionid\":\"19236529053\",\"auto\":0}";
        boolean res = false;
        try {
            JSONObject singRoundObject = JSON.parseObject(singleRound);
            System.out.println(singRoundObject);
            if (singRoundObject.containsKey("cardInfo")) {
                JSONObject innerDict = (JSONObject) singRoundObject.get("cardInfo");
                System.out.println(innerDict);
                System.out.println(innerDict.get("name"));
                if (innerDict.containsKey("name") & (innerDict.get("name").equals("小选为您推荐"))) {
                    res = true;
                }

            }
        } catch (JSONException e) {
            e.printStackTrace();
            res = false;
        }

        assertEquals(true, res);
    }

}