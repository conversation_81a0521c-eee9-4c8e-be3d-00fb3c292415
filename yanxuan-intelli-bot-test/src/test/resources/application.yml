negativeqa:
  threshold: 1.5
smartworkfunction:
  polaranalysis:
    # url
    url: "http://smart-infer.hz.infra.mail:31938/v1/models/nlp-diting-comment-polar:predict"
    host: "nlp-diting-comment-polar.yx-serving.svc"
  correction:
    # url
    url: "http://smart-infer.hz.infra.mail:31938/v1/models/mac-bert-text-correction:predict"
    host: "mac-bert-text-correction.yx-serving.svc"


specialtoken:
  regexstring: "?{3,}|(.{3,})"

complaint:
  words:
    - "315"
    - "消费者协会"
    - "工商"
    - "政府部门"
    - "微博"
    - "曝光"
    - "高管"
    - "丁磊"
    - "投诉"
    - "领导回电"
    - "上级回电"
    - "主管回电"
    - "公司地址"
    - "公司名称"
    - "营业执照"
    - "虚假宣传"
    - "霸王条款"
    - "欺诈"
    - "欺骗"
appeasing:
  samples:
    - "非常抱歉给您带来了不愉快的购物体验"
    - "真的很庆幸，能遇到您这么豁达的客人。"
    - "您很有同理心，能换位思考，这样的用户不多见了。"
    - "跟您协商"
    - "小选很乐意为您处理的哦"
    - "因为咨询量较大，回复没你们及时，辛苦耐心等待一下哈"
    - "小选明白的呢~您一定急需这款商品才会进线催促的，每个人都有非常着急的时候，小选理解您的心情的呢~"
    - "小选第一时间帮您处理"
    - "消消气"
    - "有您的反馈才有严选的进步哦，您的意见小选都如实记录好了呢~啾咪~"
    - "请您不要放心上。"
    - "针对您的情况，我们特意申请了小补偿，还希望您见谅。"
    - "很抱歉给您造成了这些困扰，在这件事情上我们确实犯了一个错误，对此我们向您道歉。"
    - "听您说了这么多 小选这边也是和您一起难受的"
    - "小选非常乐意为您解决网易严选产品相关的售前售后问题"
    - "如果我们能帮您的一定会尽力，不能帮到您的地方也请您谅解"
    - "实在不好意思亲给您带来的不便"
    - "出现这个情况小选也是很内疚"
    - "小选会去帮您处理的，包您满意"
    - "您再给小选一次机会嘛~ 小选会把这个问题处理好的呢~"
    - "您已经做出很大让步了。"
    - "我能感受到您的失望，我可以帮助您的是"
    - "小选在网购遇到这个情况肯定跟您的心情是一样的，大家都是消费者，所以非常理解您的心情。"
    - "很抱歉，刚刚小选没描述清楚，让您误解了"
    - "望您谅解"
    - "不客气的呢，能为您服务也是小选的荣幸哟~"
    - "十分抱歉，也是给您致歉了"
    - "请您不要着急,,我们一定会竭尽全力为您解决的"
    - "您这次问题解决后尽快放心使用"
    - "这个是小选帮您尽最大努力争取到的"
term:
  termwords:
    - "工单"
    - "风控"
    - "黑名单"
    - "切仓"
    - "调仓"
    - "砍单"
    - "拆包"
    - "同人"
    - "在途"
    - "SKU"
    - "异常卡单"
    - "刷单"
    - "代销"
    - "出库"
    - "渠道"
    - "仓配"
    - "popo"
    - "青龙系统"
    - "便洁宝"
    - "QA"
    - "工作台"
    - "客服系统"
    - "七鱼"
    - "销退"
    - "内仓"
    - "外仓"
    - "追单"
    - "信誉等级"
    - "首响"
    - "平响"