{"swagger": "2.0", "info": {"description": "The api document is built by JDK-1.8.0_301 at: 2025-07-23 15:31:45 +0800", "version": "0", "title": "接口文档"}, "basePath": "/", "tags": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "报警接口"}, {"name": "ChannelController", "description": "全渠道商品搜索，实时问答"}, {"name": "CheckController", "description": "各种检查 相似问的有效性检查 知识重复的检查"}, {"name": "DMController", "description": "主站APP的机器人问答，接口很老"}, {"name": "ElasticController"}, {"name": "EmbeddingController"}, {"name": "GuideV2Controller"}, {"name": "HealthCheckController", "description": "Consul健康检查服务 由于历史问题，两个health接口都需要保留"}, {"name": "IntentProductController"}, {"name": "MpsMessageApiController"}, {"name": "QualityInspectionController", "description": "人工质检 接口保留，但不处理具体逻辑"}, {"name": "RGAssistController", "description": "人工辅助"}, {"name": "SingleModuleController"}, {"name": "SyncController"}, {"name": "TestController", "description": "报警接口"}, {"name": "WorkOrderController", "description": "七鱼客服工作台相关"}], "paths": {"/api/mps/mpsSend.do": {"post": {"tags": ["MpsMessageApiController"], "operationId": "MpsMessageApiController.mpsSend", "parameters": [{"in": "body", "name": "mpsReceiveMessageBean", "required": true, "schema": {"$ref": "#/definitions/MpsReceiveMessageBean"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/api/mps/receive": {"post": {"tags": ["MpsMessageApiController"], "summary": "接收MPS消息", "description": "接收MPS消息", "operationId": "MpsMessageApiController.receive", "parameters": [{"in": "body", "name": "messages", "description": "消息\n", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/MpsReceiveMessageBean"}}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/SucResp%3CObject%3E"}}}}}, "/api/mps/smartSessionClose.do": {"post": {"tags": ["MpsMessageApiController"], "operationId": "MpsMessageApiController.smartSessionClose", "parameters": [{"in": "body", "name": "mpsMessage", "required": true, "schema": {"$ref": "#/definitions/MpsReceiveMessageBean"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/api/mps/trainChange.do": {"post": {"tags": ["MpsMessageApiController"], "operationId": "MpsMessageApiController.trainChange", "parameters": [{"in": "body", "name": "mpsMessage", "required": true, "schema": {"$ref": "#/definitions/MpsReceiveMessageBean"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/emb/bert_embedding.do": {"post": {"tags": ["EmbeddingController"], "operationId": "EmbeddingController.embedding", "parameters": [{"in": "body", "name": "sen", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/emb/bert_embedding_v2.do": {"post": {"tags": ["EmbeddingController"], "operationId": "EmbeddingController.embeddingV2", "parameters": [{"in": "body", "name": "bertEmbReq", "required": true, "schema": {"$ref": "#/definitions/BertEmbReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/es/bulk/insert": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.main", "parameters": [{"in": "body", "name": "simpleBulkRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleBulkRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/es/index/delete": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.delete", "parameters": [{"in": "body", "name": "simpleBulkRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleBulkRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/es/search/knn": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.knn", "parameters": [{"in": "body", "name": "simpleKnnRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleKnnRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/es/search/matchAll": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.matchAll", "parameters": [{"in": "body", "name": "simpleKnnRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleKnnRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/es/search/search": {"post": {"tags": ["ElasticController"], "operationId": "ElasticController.search", "parameters": [{"in": "body", "name": "simpleKnnRequest", "required": true, "schema": {"$ref": "#/definitions/SimpleKnnRequest"}}], "responses": {"200": {"description": "successful operation"}}}}, "/i/health": {"post": {"tags": ["HealthCheckController"], "summary": "健康检查接口", "description": "健康检查接口", "operationId": "HealthCheckController.checkHealth", "parameters": [], "responses": {"200": {"description": "successful operation"}}}}, "/sync/attr.do": {"post": {"tags": ["SyncController"], "operationId": "SyncController.attr", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "attrId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/sync/knowledge.do": {"post": {"tags": ["SyncController"], "operationId": "SyncController.knowledge", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/workbench/classify.do": {"post": {"tags": ["WorkOrderController"], "operationId": "WorkOrderController.doPost", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "content", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/SessionResp"}}}}}, "/yanxuan-intelli-bot/batch_multi_channel_qa.do": {"post": {"tags": ["ChannelController"], "operationId": "ChannelController.batchMultiChannelQaForTrain", "parameters": [{"in": "body", "name": "batchChannelQaReq", "required": true, "schema": {"$ref": "#/definitions/BatchChannelQaReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/chat_classify.do": {"post": {"tags": ["IntentProductController"], "summary": "闲聊分类", "description": "闲聊分类", "operationId": "IntentProductController.chatClassify", "parameters": [{"in": "body", "name": "req", "description": "", "required": true, "schema": {"$ref": "#/definitions/ChatClassifyReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/check_manual.do": {"post": {"tags": ["CheckController"], "summary": "检查是否是人工", "description": "检查是否是人工", "operationId": "CheckController.checkManual", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/check_similar_knowledge.do": {"post": {"tags": ["CheckController"], "operationId": "CheckController.check", "parameters": [{"in": "body", "name": "checkSimilarKnowledgeReq", "required": true, "schema": {"$ref": "#/definitions/CheckSimilarKnowledgeReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/check_similar_question.do": {"post": {"tags": ["CheckController"], "operationId": "CheckController.check_1", "parameters": [{"in": "body", "name": "checkSimilarReq", "required": true, "schema": {"$ref": "#/definitions/CheckSimilarQuestionReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/check_transfer_rg.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.checkTransferRg", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "sessionId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/checkBotKnowledgeES": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "AlertController.checkBotKnowledgeES", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/checkBotSmartworkBertContextVec": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "AlertController.checkBotSmartworkBertContextVec", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/checkBotSmartworkBertVec": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "AlertController.checkBotSmartworkBertVec", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/get_item_info.do": {"post": {"tags": ["GuideV2Controller"], "summary": "item 信息", "description": "item 信息", "operationId": "GuideV2Controller.itemInfo", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/guide.do": {"post": {"tags": ["GuideV2Controller"], "summary": "导购主接口", "description": "导购主接口", "operationId": "GuideV2Controller.guide", "parameters": [{"in": "body", "name": "req", "description": "", "required": true, "schema": {"$ref": "#/definitions/GuideReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/guide_active.do": {"post": {"tags": ["GuideV2Controller"], "summary": "导购主动推荐", "description": "导购主动推荐", "operationId": "GuideV2Controller.guideActive", "parameters": [{"in": "body", "name": "req", "description": "", "required": true, "schema": {"$ref": "#/definitions/GuideReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/guide_intent.do": {"post": {"tags": ["GuideV2Controller"], "summary": "意图接口调用", "description": "意图接口调用", "operationId": "GuideV2Controller.intent", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/health.do": {"post": {"tags": ["HealthCheckController"], "operationId": "HealthCheckController.doPost", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "properties": {"MAP.KEY": {"type": "string"}}}}}}}, "/yanxuan-intelli-bot/input_associate.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.inputAssociate", "parameters": [{"in": "body", "name": "inputAssociateReq", "required": true, "schema": {"$ref": "#/definitions/InputAssociateReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_first.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.intentFirst", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_product.do": {"post": {"tags": ["IntentProductController"], "summary": "售后意图分类", "description": "售后意图分类", "operationId": "IntentProductController.intentProduct", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_product_cls.do": {"post": {"tags": ["IntentProductController"], "summary": "售后分类服务", "description": "售后分类服务", "operationId": "IntentProductController.intentProductCls", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_product_fs.do": {"post": {"tags": ["IntentProductController"], "summary": "售后fewshot服务", "description": "售后fewshot服务", "operationId": "IntentProductController.intentProductFs", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/intent_product_ver.do": {"post": {"tags": ["IntentProductController"], "summary": "售后意图分类AB版本", "description": "售后意图分类AB版本", "operationId": "IntentProductController.intentProductVer", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/item_base_info.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.getSrcId", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/item_label_task_insert.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.insert", "parameters": [{"in": "body", "name": "itemLabelTask", "required": true, "schema": {"$ref": "#/definitions/ItemLabelTask"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/item_label_task_select.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.select", "parameters": [{"in": "body", "name": "itemLabelTaskSelectReq", "required": true, "schema": {"$ref": "#/definitions/ItemLabelTaskSelectReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/item_label_task_update.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.select_1", "parameters": [{"in": "body", "name": "itemLabelTask", "required": true, "schema": {"$ref": "#/definitions/ItemLabelTask"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/multi_channel_qa.do": {"post": {"tags": ["ChannelController"], "operationId": "ChannelController.multiChannelQa", "parameters": [{"in": "body", "name": "channelQaReq", "required": true, "schema": {"$ref": "#/definitions/ChannelQaReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/multi_channel_qa_addi.do": {"post": {"tags": ["ChannelController"], "operationId": "ChannelController.multiChannelQaAddi", "parameters": [{"in": "body", "name": "channelQaReq", "required": true, "schema": {"$ref": "#/definitions/ChannelQaAddiReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/multi_channel_search.do": {"post": {"tags": ["ChannelController"], "operationId": "ChannelController.check", "parameters": [{"in": "body", "name": "searchReq", "required": true, "schema": {"$ref": "#/definitions/SearchReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/ngram.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.ngramPreprocess", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/prophet.do": {"post": {"tags": ["SingleModuleController"], "summary": "服务先知接口", "description": "服务先知接口", "operationId": "SingleModuleController.prophet", "parameters": [{"in": "body", "name": "prophetReq", "description": "", "required": true, "schema": {"$ref": "#/definitions/ProphetReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/qp.do": {"post": {"tags": ["GuideV2Controller"], "summary": "搜索QP接口", "description": "搜索QP接口", "operationId": "GuideV2Controller.qp", "parameters": [{"in": "body", "name": "req", "description": "", "required": true, "schema": {"$ref": "#/definitions/GuideReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/quality_inspection.do": {"post": {"tags": ["QualityInspectionController"], "operationId": "QualityInspectionController.qualityInspection", "parameters": [{"in": "body", "name": "request", "required": true, "schema": {"$ref": "#/definitions/QualityInspectionRequest"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/QualityInspectionResponse"}}}}}, "/yanxuan-intelli-bot/rcmd.do": {"post": {"tags": ["GuideV2Controller"], "summary": "召回接口调用", "description": "召回接口调用", "operationId": "GuideV2Controller.rcmd", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/rg_assist_guide.do": {"post": {"tags": ["RGAssistController"], "operationId": "RGAssistController.guide", "parameters": [{"in": "body", "name": "RGAssistGuideReq", "required": true, "schema": {"$ref": "#/definitions/RGAssistGuideReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/rg_assist_guide_rcmd_reason.do": {"post": {"tags": ["RGAssistController"], "operationId": "RGAssistController.rcmd", "parameters": [{"in": "body", "name": "rgAssistRcmdReasonReq", "required": true, "schema": {"$ref": "#/definitions/RGAssistRcmdReasonReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/rg_assist_guide_scan.do": {"post": {"tags": ["RGAssistController"], "operationId": "RGAssistController.guide_1", "parameters": [{"in": "body", "name": "RGAssistGuideTrackReq", "required": true, "schema": {"$ref": "#/definitions/RGAssistGuideTrackReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/search.do": {"post": {"tags": ["GuideV2Controller"], "summary": "搜搜接口调用", "description": "搜搜接口调用", "operationId": "GuideV2Controller.search", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "description": "", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/shortcut_rcmd.do": {"post": {"tags": ["SingleModuleController"], "summary": "快捷短语气泡词预测接口", "description": "快捷短语气泡词预测接口", "operationId": "SingleModuleController.prophet_1", "parameters": [{"in": "body", "name": "shortcutRcmdReq", "description": "", "required": true, "schema": {"$ref": "#/definitions/ShortcutRcmdReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/attr.do": {"post": {"tags": ["TestController"], "operationId": "TestController.attr", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "attrId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/chat.do": {"post": {"tags": ["TestController"], "operationId": "TestController.chat", "parameters": [{"in": "body", "name": "req", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/ReqInstance"}}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/getAnswerId.do": {"post": {"tags": ["TestController"], "operationId": "TestController.getAnswerId", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "channelId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "platform", "in": "formData", "required": true, "type": "string"}, {"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/getFeature.do": {"post": {"tags": ["TestController"], "operationId": "TestController.getFeature", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "domain", "in": "formData", "required": true, "type": "string"}, {"name": "featureNames", "in": "formData", "required": true, "type": "string"}, {"name": "ids", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/intent.do": {"post": {"tags": ["TestController"], "operationId": "TestController.intent", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "sen", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/knowledge.do": {"post": {"tags": ["TestController"], "operationId": "TestController.knowledge", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/knowledgeId2outAnswer.do": {"post": {"tags": ["TestController"], "operationId": "TestController.knowledgeId2outAnswer", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/knowledgeSolution.do": {"post": {"tags": ["TestController"], "operationId": "TestController.knowledgeSolution", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "knowledgeId", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/processWithSimpleModel.do": {"post": {"tags": ["TestController"], "operationId": "TestController.processWithSimpleModel", "parameters": [{"in": "body", "name": "botContext", "required": true, "schema": {"$ref": "#/definitions/BotContext"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/recallWithEs6.do": {"post": {"tags": ["TestController"], "operationId": "TestController.chat_1", "parameters": [{"in": "body", "name": "botContext", "required": true, "schema": {"$ref": "#/definitions/BotContext"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/redis.do": {"post": {"tags": ["TestController"], "operationId": "TestController.redisGet", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "key", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/redisDelete.do": {"post": {"tags": ["TestController"], "operationId": "TestController.redisDelete", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "key", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/secure.do": {"post": {"tags": ["TestController"], "operationId": "TestController.secure", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "sen", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/test/similarity.do": {"post": {"tags": ["TestController"], "operationId": "TestController.similarity", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "input", "in": "formData", "required": true, "type": "string"}, {"name": "itemId", "in": "formData", "required": true, "type": "integer", "format": "int64"}, {"name": "threshold", "in": "formData", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/text.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.pipeline", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "query", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/usercenter-prophet.do": {"post": {"tags": ["SingleModuleController"], "operationId": "SingleModuleController.userCenterProphet", "parameters": [{"in": "body", "name": "prophetReq", "description": "", "required": true, "schema": {"$ref": "#/definitions/ProphetReq"}}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BaseResp"}}}}}, "/yanxuan-intelli-bot/yx_bot_kb.do": {"post": {"tags": ["DMController"], "operationId": "DMController.process", "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"name": "jsonStr", "in": "formData", "required": true, "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/BotResp"}}}}}}, "definitions": {"AlgoAnswer": {"properties": {"answerContent": {"type": "string"}, "answerId": {"type": "integer", "format": "int64"}, "answerType": {"type": "integer", "format": "int32"}, "answerUse": {"type": "integer", "format": "int32"}, "channel": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "createTime": {"type": "integer", "format": "int64"}, "deleteFlag": {"type": "integer", "format": "int32"}, "editTime": {"type": "integer", "format": "int64"}, "effectiveTime": {"type": "integer", "format": "int64"}, "expiryTime": {"type": "integer", "format": "int64"}, "itemCateLabel": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "itemCateLabelStr": {"type": "string"}, "itemInfoLabel": {"type": "string"}, "otherLabel": {"type": "array", "items": {"type": "string"}}, "platform": {"type": "array", "items": {"type": "string"}}, "relevanceKnowledge": {"type": "string"}, "relevanceShortCut": {"type": "string"}, "updateTime": {"type": "integer", "format": "int64"}}}, "AlgoKeyword": {"properties": {"cleanKeyword": {"type": "string"}, "keyword": {"type": "string"}, "status": {"type": "integer", "format": "int32"}}}, "AlgoKnowledge": {"properties": {"answerNum": {"type": "integer", "format": "int32"}, "answers": {"type": "array", "items": {"$ref": "#/definitions/AlgoAnswer"}}, "busCate": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "cleanStdQuestion": {"type": "string"}, "cleanStdQuestionTerms": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "editTime": {"type": "integer", "format": "int64"}, "effectiveTime": {"type": "integer", "format": "int64"}, "effectiveType": {"type": "integer", "format": "int32"}, "expiryTime": {"type": "integer", "format": "int64"}, "keywordGroups": {"type": "array", "items": {"$ref": "#/definitions/AlgoKeyword"}}, "kmCate": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "knowledgeId": {"type": "integer", "format": "int64"}, "knowledgeType": {"type": "integer", "format": "int32"}, "knowledgeUse": {"type": "integer", "format": "int32"}, "similarQuestions": {"type": "array", "items": {"$ref": "#/definitions/AlgoSimilarQuestion"}}, "status": {"type": "integer", "format": "int32"}, "stdQuestion": {"type": "string"}, "stqQuestionVector": {"type": "array", "items": {"type": "number", "format": "float"}}, "unSimilarQuestions": {"type": "array", "items": {"$ref": "#/definitions/AlgoSimilarQuestion"}}, "updateTime": {"type": "integer", "format": "int64"}}}, "AlgoSimilarQuestion": {"properties": {"cleanContent": {"type": "string"}, "cleanContentTerms": {"type": "string"}, "content": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "status": {"type": "integer", "format": "int32"}}}, "BaseResp": {"properties": {"code": {"type": "integer", "format": "int32"}}}, "BatchChannelQaReq": {"properties": {"qaReqList": {"type": "array", "items": {"$ref": "#/definitions/ChannelQaReq"}}}}, "BertEmbReq": {"properties": {"headerHost": {"type": "string"}, "maxLength": {"type": "integer", "format": "int32"}, "sen": {"type": "array", "items": {"type": "string"}}, "url": {"type": "string"}}}, "BotContext": {"properties": {"activeRcmdCount": {"type": "integer", "format": "int32", "description": "主动推荐次数\n"}, "cardItemIds": {"type": "array", "description": "会话过程中累计多次发送的商品卡片 如果是外渠，1对多的商品，也放在这。\n", "items": {"type": "integer", "format": "int64"}}, "cardItems": {"type": "array", "description": "会话过程中累计多次发送的商品详情\n", "items": {"$ref": "#/definitions/Item"}}, "channel": {"type": "integer", "format": "int64", "description": "渠道id\n"}, "consultTime": {"type": "integer", "format": "int64"}, "deviceType": {"type": "string", "description": "设备类型 老版本APP会话使用\n", "enum": ["NULL", "ANDROID", "IOS", "OTHER"]}, "hstTexts": {"type": "array", "description": "用户直接输入的历史语句记录\n", "items": {"type": "string"}}, "inputs": {"type": "array", "description": "当前轮的文本数据\n", "items": {"$ref": "#/definitions/TextBasicData"}}, "invokeSource": {"type": "string", "description": "调用来源，用于判断人工或机器人来源\n\n----\n枚举类型: **InvokeSource**   \n\n*枚举值*\n> * *ROBOT* : 0 - 机器人来源\n> * *HUMAN* : 1 - 人工来源\n> * *LIVE* : 2 - 直播\n\n", "enum": ["ROBOT", "HUMAN", "LIVE"]}, "item": {"description": "对应的Item对象,如果是商详页过来，会放在这里\n", "$ref": "#/definitions/Item"}, "itemId": {"type": "integer", "format": "int64", "description": "携带商品ID，主站ID，为数字\n"}, "itemIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "itemPhyCategoryIdStr": {"type": "string"}, "multiChannelOrderId": {"type": "string", "description": "平台订单ID\n"}, "multiChannelPipelineLogs": {"type": "array", "items": {"$ref": "#/definitions/MultiChannelPipelineLog"}}, "multiChannelSessionId": {"type": "string", "description": "会话ID\n"}, "newUser": {"type": "boolean", "description": "是新用户，默认是false\n"}, "orderId": {"type": "integer", "format": "int64", "description": "APP携带订单ID 老版本APP会话使用\n"}, "orderItemIds": {"type": "array", "description": "从订单页过来的话，因为无法确定单个商品，所以是需要取多个商品\n", "items": {"type": "integer", "format": "int64"}}, "orderItems": {"type": "array", "description": "从订单页过来的话，因为无法确定单个商品，所以是需要取多个商品\n", "items": {"$ref": "#/definitions/Item"}}, "pipelineLogs": {"type": "array", "description": "历史处理链路的日志，一般就保留前两轮即可\n", "items": {"$ref": "#/definitions/MainPipelineLog"}}, "platform": {"type": "string"}, "platformItemId": {"type": "string"}, "platformRawItemCardId": {"type": "string"}, "platformType": {"type": "string", "description": "来源平台，APP/WECHAT/WEB 老版本APP会话使用\n", "enum": ["NULL", "APP", "WECHAT", "WEB"]}, "preSale": {"type": "boolean", "description": "售前标识 老版本APP会话使用\n"}, "sessionId": {"type": "integer", "format": "int64", "description": "会话ID，老版本APP会话使用 将逐渐废弃\n"}, "sessionInteraction": {"type": "integer", "format": "int32"}, "sourceType": {"type": "string", "description": "source标识 老版本APP会话使用\n\n----\n枚举类型: **SourceType**   \n\n*枚举值*\n> * *NULL* : NULL\n> * *ALL* : ALL， 这里是给主动预测配置使用\n> * *ORDER_DETAIL* : APP 订单入口\n> * *AFTER_SALE_PROCESS* : APP 售后进度页入口\n> * *AFTER_SALE_SERVICE* : APP 售后入口\n> * *USER_CENTER* : APP 个人中心入口\n> * *ITEM_DETAIL* : APP 商品详情页入口\n> * *MESSAGE_CENTER* : APP 消息中心入口\n> * *CUSTOMER_REVIEWS* : APP 商品评论页\n> * *WECHAT* : 微信\n> * *WEB* : WEB\n> * *BALANCE* : 入口余额页\n> * *PRO_MEMBERSHIP* : pro会员权益页\n> * *CROWDFUNDING_ITEM* : 众筹商品页\n\n", "enum": ["NULL", "ALL", "ORDER_DETAIL", "AFTER_SALE_PROCESS", "AFTER_SALE_SERVICE", "USER_CENTER", "ITEM_DETAIL", "MESSAGE_CENTER", "CUSTOMER_REVIEWS", "WECHAT", "WEB", "BALANCE", "PRO_MEMBERSHIP", "CROWDFUNDING_ITEM"]}, "testMode": {"type": "boolean"}, "ticketStatus": {"type": "integer", "format": "int32", "description": "工单状态\n"}, "turnCount": {"type": "integer", "format": "int32", "description": "记录交互轮次\n"}, "userId": {"type": "integer", "format": "int64", "description": "用户ID\n"}, "userOrder": {"description": "用户最可能关心的一笔订单详情，为复杂规则产生的订单，不一定是订单页过来的订单 这里的订单指的是针对某个SPU的订单详情\n", "$ref": "#/definitions/UserOrder"}, "userR": {"type": "integer", "format": "int32", "description": "用户R等级\n"}, "userRGCount24H": {"type": "integer", "format": "int32", "description": "24H访问人工次数\n"}, "userRGCount48H": {"type": "integer", "format": "int32", "description": "48H访问人工次数\n"}, "userV": {"type": "integer", "format": "int32", "description": "用户v等级\n"}}}, "BotReq": {"properties": {"data": {"type": "string"}, "faqIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "isOrderQues": {"type": "boolean"}, "itemId": {"type": "integer", "format": "int64"}, "kfGroupId": {"type": "integer", "format": "int64"}, "messageId": {"type": "string"}, "orderId": {"type": "integer", "format": "int64"}, "packageId": {"type": "integer", "format": "int64"}, "sessionId": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}}}, "BotResp": {"properties": {"chatIntent": {"type": "string", "description": "闲聊类别\n"}, "faqRslt": {"$ref": "#/definitions/FaqIdRslt"}, "guideRcmdReason": {"type": "string", "description": "导购新增 (a) guideRslt, GuideRslt， 导购内容 (c) guideRcmdReason, string , 统一推荐语，用于多list的商品推荐\n"}, "guideRslt": {"type": "array", "items": {"$ref": "#/definitions/GuideRslt"}}, "intentRslt": {"$ref": "#/definitions/IntentRslt"}, "isRGVisible": {"type": "boolean", "description": "智能转人工按钮（判断是否出人工按钮）\n"}, "noAnswer": {"type": "boolean"}, "statType": {"type": "integer", "format": "int32"}, "state": {"type": "string", "enum": ["SUCCESS", "PARAM_ERROR", "PARAM_NOT_ENOUGH", "INTERNAL_ERROR"]}, "text": {"type": "string", "description": "参数说明： (a) state, 请求返回状态字段 (b) text, String, 展示文案、话术、文本 (c) faqRslt, FaqIdResult, faq结果 (d) statType, int, 统计组统计时需要区分结果类型\n"}}}, "CateResult": {"properties": {"cateName": {"type": "string", "description": "类目的叶子类目名称\n"}, "cateSummaryResult": {"description": "预留的摘要字段，考虑可能总结会和预测的类目联动\n", "$ref": "#/definitions/SummaryResult"}, "leafCateId": {"type": "integer", "format": "int64", "description": "类目的叶子类目ID\n"}, "score": {"type": "number", "format": "double", "description": "类目的分数，为0到1之间\n"}}}, "ChannelQaAddiReq": {"properties": {"channelId": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "itemIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "itemPhyCategoryIdStr": {"type": "string"}, "orderId": {"type": "string"}, "platformItemId": {"type": "string"}, "platformRawItemCardId": {"type": "string"}, "serviceId": {"type": "string"}, "sessionId": {"type": "string"}}}, "ChannelQaReq": {"properties": {"batch": {"type": "boolean"}, "channelId": {"type": "integer", "format": "int64"}, "consultTime": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int32"}, "messageContent": {"$ref": "#/definitions/MessageContent"}, "messageId": {"type": "string"}, "messageSource": {"type": "integer", "format": "int32", "description": "customer = 1, // 用户 staff = 2, // 客服 system = 3, // 系统 robot = 4 // 机器人\n"}, "messageType": {"type": "integer", "format": "int32", "description": "other = 0, // 其他 text = 1, // 文本 image = 2, // 图片 orderCard = 3, // 订单卡片 goodCard = 4, // 商品卡片 afterSaleCard = 5, // 售后卡片类型\n\n\nsystem = 6, // 系统消息\nemoji = 7, // 表情\nfile = 8, // 文件\nlink = 9, // 链接\naudio = 10, // 音频\nvideo = 11, // 视频\n"}, "platform": {"type": "string"}, "rawMsg": {"type": "string"}, "serviceId": {"type": "string"}, "sessionId": {"type": "string"}, "sessionInteraction": {"type": "integer", "format": "int32"}, "testMode": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}, "topK": {"type": "integer", "format": "int32"}, "userId": {"type": "string"}}}, "ChannelQaResp": {"properties": {"channelQaAddiReq": {"$ref": "#/definitions/ChannelQaAddiReq"}, "coreIntents": {"type": "array", "items": {"$ref": "#/definitions/CoreIntent"}}, "faqList": {"type": "array", "items": {"$ref": "#/definitions/ChannelQaRslt"}}, "intentTypeV2": {"type": "string", "description": "枚举类型: **IntentTypeV2**   \n\n*枚举值*\n> * *CHITCHAT* : 闲聊\n> * *KBQA* : 商品知识问答\n> * *FAQ* : FAQ\n> * *SPECIAL* : 特殊意图\n> * *ITEM_CARD* : 商品卡片\n> * *ORDER_CARD* : 订单卡片\n> * *GUIDE* : 导购\n> * *UNKNOWN*\n\n", "enum": ["CHITCHAT", "KBQA", "FAQ", "SPECIAL", "ITEM_CARD", "ORDER_CARD", "GUIDE", "UNKNOWN"]}, "scoreType": {"type": "integer", "format": "int32"}}}, "ChannelQaRslt": {"properties": {"answerId": {"type": "integer", "format": "int64"}, "attrSource": {"type": "integer", "format": "int32"}, "cateId": {"type": "integer", "format": "int64"}, "corpusId": {"type": "string"}, "intentId": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "itemPhyCategoryIdStr": {"type": "string"}, "knowledgeId": {"type": "integer", "format": "int64"}, "knowledgeSource": {"type": "integer", "format": "int32"}, "knowledgeType": {"type": "integer", "format": "int32"}, "platformItemId": {"type": "string"}, "platformRawItemCardId": {"type": "string"}, "question": {"type": "string"}, "score": {"type": "number", "format": "double"}, "showAnswer": {"type": "string"}, "showQuestion": {"type": "string"}}}, "ChatClassifyReq": {"properties": {"sen": {"type": "string"}}}, "CheckSimilarKnowledgeReq": {"properties": {"extra": {"type": "object", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}, "needCheckKnowledgeMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/NeedCheckKnowledge"}, "properties": {"MAP.KEY": {"type": "integer", "format": "int64"}}}, "topK": {"type": "integer", "format": "int32"}}}, "CheckSimilarQuestionReq": {"properties": {"checkTypes": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "knowledges": {"type": "array", "items": {"$ref": "#/definitions/AlgoKnowledge"}}}}, "CoreIntent": {"properties": {"confidence": {"type": "string"}, "idx": {"type": "integer", "format": "int32"}, "intent": {"type": "string"}, "intentCh": {"type": "string"}, "knowledgeId": {"type": "integer", "format": "int64"}, "knowledgeType": {"type": "string"}, "prob": {"type": "number", "format": "double"}}}, "FaqIdRslt": {"properties": {"faqIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "faqQues": {"type": "array", "items": {"type": "string"}}, "scores": {"type": "array", "items": {"type": "number", "format": "double"}}, "state": {"type": "string", "description": "成员变量： 1. state ： 请求返回状态字段 2. type ： 返回结果类型 3. faqIds：匹配到的faq答案\n", "enum": ["SUCCESS", "PARAM_ERROR", "PARAM_NOT_ENOUGH", "INTERNAL_ERROR"]}, "type": {"type": "integer", "format": "int32"}}}, "FilterTerm": {"properties": {"func": {"type": "string", "enum": ["MATCH", "TERM"]}, "key": {"type": "string"}, "nested": {"type": "boolean"}, "nestedPath": {"type": "string"}, "value": {"type": "string"}}}, "GuideReq": {"properties": {"itemId": {"type": "string"}, "query": {"type": "string"}, "sessionId": {"type": "string"}, "uid": {"type": "string"}}}, "GuideRslt": {"properties": {"desc": {"type": "string"}, "itemId": {"type": "integer", "format": "int64"}, "itemNickName": {"type": "string"}, "note": {"type": "string"}, "picture": {"type": "string"}, "rcmdReason": {"type": "array", "items": {"type": "string"}}, "rcmdTitle": {"type": "array", "items": {"type": "string"}}, "title": {"type": "string"}, "url": {"type": "string"}}}, "InputAssociateReq": {"properties": {"query": {"type": "string"}, "sessionId": {"type": "integer", "format": "int64"}}}, "IntentRslt": {"properties": {"coreIntents": {"type": "array", "items": {"$ref": "#/definitions/CoreIntent"}}, "firstIntent": {"type": "string", "description": "枚举类型: **Level1IntentType**   \n\n*枚举值*\n> * *KBQA* : 商品知识问答\n> * *GUIDE* : 导购\n> * *CHITCHAT* : 闲聊\n> * *FAQ* : FAQ\n> * *SPECIAL* : 特殊意图\n> * *ITEM_CARD*\n> * *ORDER_CARD* : 订单卡片\n> * *UNKNOWN*\n\n", "enum": ["KBQA", "GUIDE", "CHITCHAT", "FAQ", "SPECIAL", "ITEM_CARD", "ORDER_CARD", "UNKNOWN"]}, "secondIntent": {"type": "string", "description": "枚举类型: **Level2IntentType**   \n\n*枚举值*\n> * *SPECIAL_MANUAL* : 找人工\n> * *SPECIAL_COMPLAIN* : 投诉\n\n", "enum": ["SPECIAL_MANUAL", "SPECIAL_COMPLAIN"]}}}, "Item": {"properties": {"itemId": {"type": "integer", "format": "int64"}, "itemName": {"type": "string"}, "nickName": {"type": "string"}, "phyCategory1Id": {"type": "integer", "format": "int64"}, "phyCategory2Id": {"type": "integer", "format": "int64"}, "phyCategory3Id": {"type": "integer", "format": "int64"}, "phyCategory4Id": {"type": "integer", "format": "int64"}, "picUrl": {"type": "string"}, "soldOut": {"type": "boolean"}}}, "ItemCard": {"properties": {"itemId": {"type": "string"}, "skuId": {"type": "string"}, "url": {"type": "string"}}}, "ItemLabelTask": {"properties": {"id": {"type": "integer", "format": "int64"}, "insertTime": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "itemPreviousLabelTask": {"type": "object", "description": "this is a `java.lang.Object`"}, "itemPreviousLabelTaskStr": {"type": "string"}, "status": {"type": "integer", "format": "int32"}}}, "ItemLabelTaskSelectReq": {"properties": {"endTime": {"type": "integer", "format": "int64"}, "startTime": {"type": "integer", "format": "int64"}}}, "JSONObject": {"properties": {"map": {"type": "object", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}}}, "KbqaResp": {"properties": {"kbqaType": {"type": "string", "description": "枚举类型: **KbqaType**   \n\n*枚举值*\n> * *SIZE_PIC* : 尺码图\n> * *GOODS_PROPERTY* : 尺码图\n\n", "enum": ["SIZE_PIC", "GOODS_PROPERTY"]}, "score": {"type": "number", "format": "double"}, "showText": {"type": "string"}}}, "KnowledgeMatchResp": {"properties": {"matchRslts": {"type": "array", "items": {"$ref": "#/definitions/KnowledgeMatchRslt"}}, "matchType": {"type": "integer", "format": "int32"}}}, "KnowledgeMatchRslt": {"properties": {"answerId": {"type": "integer", "format": "int64"}, "answerUse": {"type": "integer", "format": "int32"}, "cateId": {"type": "integer", "format": "int64"}, "corpusId": {"type": "string"}, "itemId": {"type": "integer", "format": "int64"}, "knowledgeId": {"type": "integer", "format": "int64"}, "question": {"type": "string"}, "score": {"type": "number", "format": "double"}}}, "MainPipelineLog": {"properties": {"context": {"type": "object", "description": "The circular reference: `BotContext`\n上下文 BotContext\n", "x-circular-ref": "BotContext"}, "faqCombine": {"description": "意图和匹配综合得到的FAQ结果 FaqIdRslt\n", "$ref": "#/definitions/FaqIdRslt"}, "hstTextBasicData": {"type": "array", "description": "上轮基本文本解析 BotReq\n", "items": {"$ref": "#/definitions/TextBasicData"}}, "input": {"description": "输入 BotReq\n", "$ref": "#/definitions/BotReq"}, "intent": {"description": "意图综合结果,包含一级和售后等意图 IntentResp\n", "$ref": "#/definitions/IntentRslt"}, "kbqa": {"description": "商品问答结果，如尺码、商品属性等 KbqaResp\n", "$ref": "#/definitions/KbqaResp"}, "match": {"description": "匹配结果 KnowledgeMatchResp\n", "$ref": "#/definitions/KnowledgeMatchResp"}, "other": {"type": "object", "description": "其他需要被记录的数据 Map\n", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}, "output": {"description": "输出 BotResp\n", "$ref": "#/definitions/BotResp"}}}, "MessageContent": {"properties": {"itemList": {"type": "array", "items": {"$ref": "#/definitions/ItemCard"}}, "orderList": {"type": "array", "items": {"$ref": "#/definitions/OrderCard"}}, "text": {"type": "string"}, "url": {"type": "string"}}}, "MpsReceiveMessageBean": {"properties": {"messageId": {"type": "string", "description": "消息ID（同一个topic下唯一：用于消费者做幂等）\n"}, "payload": {"type": "string", "description": "请求JSON String\n"}, "product": {"type": "string", "description": "产品号\n"}, "topic": {"type": "string", "description": "消息主题\n"}}}, "MultiChannelPipelineLog": {"properties": {"channelQaAddiReq": {"description": "基于input 加工出来的额外信息，如商品卡片，订单卡片等\n", "$ref": "#/definitions/ChannelQaAddiReq"}, "context": {"type": "object", "description": "The circular reference: `BotContext`\n上下文 BotContext\n", "x-circular-ref": "BotContext"}, "hstTextBasicData": {"type": "array", "description": "上轮基本文本解析 BotReq\n", "items": {"$ref": "#/definitions/TextBasicData"}}, "input": {"description": "输入 BotReq\n", "$ref": "#/definitions/ChannelQaReq"}, "other": {"type": "object", "description": "其他需要被记录的数据 Map\n", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}, "output": {"description": "输出 BotResp\n", "$ref": "#/definitions/ChannelQaResp"}}}, "NeedCheckKnowledge": {"properties": {"busCate": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "itemCateLabel": {"type": "string"}, "itemInfoLabel": {"type": "string"}, "kmCate": {"type": "string"}, "knowledgeType": {"type": "integer", "format": "int32"}, "knowledgeUse": {"type": "integer", "format": "int32"}, "similarQuestions": {"type": "array", "items": {"type": "string"}}, "stdQuestion": {"type": "string"}}}, "OrderCard": {"properties": {"orderId": {"type": "string"}, "packageList": {"type": "array", "items": {"type": "object", "description": "this is a `java.lang.Object`"}}, "url": {"type": "string"}}}, "ProphetReq": {"properties": {"device": {"type": "string", "description": "设备类型，IOS，ANDROID, OTHER\n"}, "itemId": {"type": "integer", "format": "int64", "description": "APP携带商品ID\n"}, "newUser": {"type": "boolean", "description": "是否是新用户\n"}, "orderId": {"type": "integer", "format": "int64", "description": "APP携带订单ID\n"}, "platform": {"type": "string", "description": "来源平台，APP/WECHAT/WEB\n"}, "presale": {"type": "boolean", "description": "售前标识\n"}, "returnFaqNum": {"type": "integer", "format": "int32", "description": "返回的FAQ数量\n"}, "sessionId": {"type": "integer", "format": "int64", "description": "会话ID\n"}, "source": {"type": "string", "description": "source标识\n"}, "userId": {"type": "integer", "format": "int64", "description": "用户ID\n"}, "userR": {"type": "integer", "format": "int32", "description": "用户R等级\n"}, "userRGCount24H": {"type": "integer", "format": "int32", "description": "24H访问人工次数\n"}, "userRGCount48H": {"type": "integer", "format": "int32", "description": "48H访问人工次数\n"}, "userV": {"type": "integer", "format": "int32", "description": "用户v等级\n"}}}, "QualityInspectionRequest": {"properties": {"sessionInfo": {"type": "array", "items": {"$ref": "#/definitions/TextRoundInfo"}}, "sid": {"type": "integer", "format": "int64"}}}, "QualityInspectionResponse": {"properties": {"problemIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "ReqInstance": {"properties": {"candidates": {"type": "array", "items": {"type": "string"}}, "extra": {"type": "object", "additionalProperties": {"type": "object", "description": "this is a `java.lang.Object`"}, "properties": {"MAP.KEY": {"type": "string"}}}, "hst": {"type": "string"}, "id": {"type": "string"}, "sen": {"type": "string"}}}, "RGAssistGuideReq": {"properties": {"itemId": {"type": "string"}, "userId": {"type": "string"}}}, "RGAssistGuideTrackReq": {"properties": {"userId": {"type": "integer", "format": "int64"}}}, "RGAssistRcmdReasonReq": {"properties": {"channelId": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}}}, "SearchReq": {"properties": {"channelId": {"type": "integer", "format": "int64"}, "query": {"type": "string"}, "topK": {"type": "integer", "format": "int32"}}}, "SessionResp": {"properties": {"cateResults": {"type": "array", "description": "类目结果列表，默认5个，按下标从0到4，分数依次降低\n", "items": {"$ref": "#/definitions/CateResult"}}, "rcmdType": {"type": "string", "description": "推荐结果产生的类型\n\n----\n枚举类型: **RcmdType**   \n\n*枚举值*\n> * *TOP_DEFAULT* : 默认的一些热门结果\n> * *MODEL* : 模型产生的结果\n\n", "enum": ["TOP_DEFAULT", "MODEL"]}, "sessionLength": {"type": "integer", "format": "int32", "description": "会话的长度\n"}, "state": {"type": "string", "description": "返回状态\n", "enum": ["SUCCESS", "PARAM_ERROR", "PARAM_NOT_ENOUGH", "INTERNAL_ERROR"]}, "summaryResult": {"description": "需要推荐的总结摘要\n", "$ref": "#/definitions/SummaryResult"}}}, "ShortcutRcmdReq": {"properties": {"historyKnowledgeIds": {"type": "array", "items": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "rcmdNum": {"type": "integer", "format": "int32"}, "sessionId": {"type": "integer", "format": "int64", "description": "会话id\n"}, "yxUserId": {"type": "integer", "format": "int64"}}}, "SimpleBulkRequest": {"properties": {"ids": {"type": "array", "items": {"type": "string"}}, "index": {"type": "string"}, "objects": {"type": "array", "items": {"$ref": "#/definitions/JSONObject"}}}}, "SimpleKnnRequest": {"properties": {"candidates": {"type": "integer", "format": "int32"}, "fields": {"type": "array", "items": {"type": "string"}}, "filterTerms": {"type": "array", "items": {"$ref": "#/definitions/FilterTerm"}}, "index": {"type": "string"}, "knnField": {"type": "string"}, "topK": {"type": "integer", "format": "int32"}, "vector": {"type": "array", "items": {"type": "number", "format": "float"}}}}, "SucResp<Object>": {"properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "description": "this is a `java.lang.Object`", "x-generic-type": "T"}}, "x-generic-types": ["T"]}, "SummaryResult": {"properties": {"summary": {"type": "string"}, "summaryConfidence": {"type": "string", "description": "枚举类型: **SummaryConfidence**   \n\n*枚举值*\n> * *EXCELLENT* : 非常好\n> * *GOOD* : 比较好\n> * *NORMAL* : 一般\n> * *BAD* : 差\n\n", "enum": ["EXCELLENT", "GOOD", "NORMAL", "BAD"]}}}, "TextBasicData": {"properties": {"cleanedText": {"type": "string", "description": "经过文本格式化、文本规范化后的文本\n"}, "rawText": {"type": "string", "description": "原始文本\n"}, "tags": {"type": "array", "description": "词性标注\n", "items": {"type": "string"}}, "words": {"type": "array", "description": "分词\n", "items": {"type": "string"}}}}, "TextRoundInfo": {"properties": {"inputText": {"type": "string"}, "isAutoReply": {"type": "integer", "format": "int64"}, "messageCreateTs": {"type": "integer", "format": "int64"}, "msgStatus": {"type": "integer", "format": "int64"}, "staffId": {"type": "integer", "format": "int64"}, "textType": {"type": "integer", "format": "int64"}}}, "UserOrder": {"properties": {"address": {"type": "string", "description": "具体街道地址\n"}, "applyId": {"type": "integer", "format": "int64", "description": "【申请单】Id\n"}, "applyStatus": {"type": "integer", "format": "int64", "description": "【申请单】申请单状态：\n\n\n换货：1-待审核，2-待用户寄回，3-待仓库收货质检，4-退货成功(已确认收货)，5-客服审核不通过，6-用户取消，7-系统取消，\n8-客服取消，9-等待客服确认，10-等待用户收货，11-客服拒绝，12-审核通过，直接退款，13-换新待发货，14-换新失败，待仓库收取新件，15-待拆卸商品，16-异常关闭，31-修改单号成功\n实际使用：1,2,3,5,6,7,8,9,10,11\n\n\n退货：1-待审核，2-待用户寄回，3-待仓库收货质检，4-退货成功(已确认收货)，5-客服审核不通过，6-用户取消，7-系统取消，\n8-客服取消，9-等待客服确认，10-等待用户收货，11-客服拒绝，12-审核通过，直接退款，13-换新待发货，14-换新失败，待仓库收取新件，15-待拆卸商品，16-异常关闭，31-修改单号成功\n实际使用：1,2,3,5,6,8,9,11,15\n\n\n价保：1-待审核，2-审核通过，3-审核驳回，4-价保完成，5-用户端申请中，6-用户端申请失败 7-未知\n实际使用：1,2,3,4,5,6,7\n\n\n维修：1-待客服审核，2-待用户寄回商品，3-客服审核不通过，4-系统取消，5-用户取消，6-客服取消，7-仓库收货待客服确认，\n8-待供应商收货并鉴定，9-换新待供应商安排发货，10-换新待客服安排发货，11-待用户收货，12-无法换新待客服确认，13-维修结束，\n14-无法换新已处理，15-客服拒绝，16-换新待严选出库，17-换新待严选回寄，18-供应商维修中，19-已寄送（待仓库收货）\n实际使用：1,2,3,4,5,8,11,13,14,18\n\n\n拒收： 1-待审核，2-待用户寄回，3-待仓库收货质检，4-退款完成，5-客服审核不通过，6-用户取消，7-系统取消，\n8-客服取消，9-等待客服确认，10-等待用户收货，11-客服拒绝，12-换货成功，13-换新待发货，14-换新失败，待仓库收取新件，15-待拆卸商品，16-异常关闭，31-修改单号成功\n实际使用：1,3,5,6\n"}, "applyTypeCode": {"type": "integer", "format": "int64", "description": "【申请单】申请单类型\n\n\n1 换货单，\n2 退货单，\n3 价保，\n4 维修，\n5 拒收\n"}, "carrier": {"type": "string", "description": "物流承运商\n"}, "confirmTime": {"type": "integer", "format": "int64", "description": "确认收货时间\n"}, "count": {"type": "integer", "format": "int64", "description": "sku数量\n"}, "createTime": {"type": "integer", "format": "int64", "description": "订单创建时间\n"}, "globalId": {"type": "integer", "format": "int64", "description": "【工单】全局id\n"}, "itemId": {"type": "integer", "format": "int64", "description": "商品id\n"}, "itemName": {"type": "string", "description": "商品名称\n"}, "mbrLevel": {"type": "integer", "format": "int32", "description": "普通会员等级 0,1,2,3,4,5,6 对应v0,v1,v2,v3,v4,v5,v6\n"}, "orderId": {"type": "integer", "format": "int64", "description": "订单id\n"}, "orderStatus": {"type": "integer", "format": "int32", "description": "订单状态\n\n\n0-未付款 1-已付款 2-系统取消 3-用户取消 4-客服取消 6-用户付款后取消\n实际使用：1,4,6\n"}, "outstoreNo": {"type": "string", "description": "出库批次号\n"}, "outstoreStatus": {"type": "string", "description": "出库状态 已取消,待仓库处理, 审核通过\n"}, "packageId": {"type": "string", "description": "包裹id\n"}, "payTime": {"type": "integer", "format": "int64", "description": "订单支付时间\n"}, "realPriceAmount": {"type": "number", "format": "double", "description": "订单+sku的实际支付金额\n"}, "receiverCity": {"type": "string", "description": "城市名称\n"}, "receiverDistrict": {"type": "string", "description": "区域名称\n"}, "receiverMobile": {"type": "string", "description": "收件人手机号\n"}, "receiverName": {"type": "string", "description": "收件人姓名\n"}, "receiverProvince": {"type": "string", "description": "省份名称\n"}, "skuId": {"type": "integer", "format": "int64", "description": "skuid\n"}, "spmcStatus": {"type": "integer", "format": "int32", "description": "超级会员状态\n\n\n0:未试用,\n1:试用中,\n2:试用结束未购买,\n3:已购买且在有效期内,\n4:购买后过期,\n11:亲友卡使用中,\n12:亲友卡使用过期\n\n\n主站超会对应status=3\n"}, "storehouseId": {"type": "integer", "format": "int64", "description": "仓库id\n"}, "storehouseName": {"type": "string", "description": "仓库名称\n"}, "ticketContentTitle": {"type": "string", "description": "【工单详情】主题\n"}, "ticketDescPlainText": {"type": "string", "description": "【工单详情】纯文本详细内容\n"}, "ticketStatus": {"type": "integer", "format": "int32", "description": "工单流转状态\n\n\n-1:删除状态,\n0:创建 (新建未提交),\n1:转交待处理（转交到工单池或到业务组或业务部门） ,\n2:待二次处理(一线撤回、提交到受理组被拒、转交到其他组撤回，转交到其他组被拒，转交到业务部门被拒),\n100:处理中,\n200:完结\n"}, "trackingNum": {"type": "string", "description": "运单号\n"}, "trackingStatus": {"type": "integer", "format": "int64", "description": "物流状态\n\n\n7:等待发货\n8:已发货(等待收货)\n9:已收货(等待评价)\n10:已评价\n12:系统取消\n13:用户取消\n14:客服取消\n15:用户付款后取消\n"}, "userCreditLevel": {"type": "string", "description": "用户信誉等级\n\n\nR1,R2,R3,R4,R5,审核通过\n"}, "userId": {"type": "integer", "format": "int64", "description": "用户账号id\n"}}}}, "x-project-version": "1.0-SNAPSHOT", "x-swagger-plugin-version": "2.0.2"}