curl -X POST 'http://************:8080/yanxuan-intelli-bot/multi_channel_qa.do' -d '{"channelId":1990361,"sessionId":
19226239339231,"rawMsg":"https://detail.tmall.com/item.htm?id=657144248384","platformMessageId":111}' -H "Content-Type:
application/json"

curl -X POST 'http://************:8080/yanxuan-intelli-bot/multi_channel_qa.do' -d '{"channelId":1990361,"sessionId":
124523,"rawMsg":"有商品id+多条知识","platformMessageId":"abcdf","platformId": "tb"}' -H "Content-Type: application/json"

curl -X POST 'http://************:8080/yanxuan-intelli-bot/multi_channel_qa.do' -d '{"channelId":1990361,"sessionId":
20232457121 ,"rawMsg":"无答案1","platformMessageId":"abc","platformId": "tb"}' -H "Content-Type: application/json"

- sessionId：20232457
- platform:tb
- kefuMessageId:17159
- platformMessageId:1923038961646.PNM

# redis

curl -d 'knowledgeId=20142542' 'http://************:8083/sync/knowledge.do'

curl -d 'key=msgId2pipelinelog:
19147564953842.PNM' 'http://127.0.0.1:8550/proxy/test.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/test/redis.do'

curl -curl -X POST 'http://127.0.0.1:8550/proxy/test.yanxuan-kfkm.service.mailsaas/api/qatrain/getQaTrainTask' -d '
taskId=QA_TRAIN_tb_17159' -H "X-YX-COLOR:feature2"

curl -curl -X POST 'http://127.0.0.1:8550/proxy/test.yanxuan-kfkm.service.mailsaas/api/qatrain/getQaConetextByTaskId'
-d 'taskId=QA_TRAIN_tb_17159' -H "X-YX-COLOR:feature2"

check_similar_knowledge.do

curl -X POST 'http://************:8080/yanxuan-intelli-bot/check_similar_knowledge.do' -d '{"needCheckKnowledgeMap":{"
1":{"id":123}}}' -H "Content-Type: application/json"

private String platformMessageId;
// 后端生成
private Long kefuMessageId;

curl -X
POST 'http://127.0.0.1:8550/proxy/test.yanxuan-intelli-bot.service.mailsaas/yanxuan-intelli-bot/batch_multi_channel_qa.do'
-d '{"qaReqList":[{"channelId":"1","messageSource":2,"messageType":1,"platform":"qy","platformMessageId":"70859054052","
platformSessionId":"19213508897","rawMsg":"{\"text\":\"hi，您好，机器人客服小选来啦~
选择或输入想要了解的问题，小选会尽力为您解答哦~\"}","serviceId":"System","sessionInteraction":1,"testMode":0,"timestamp":
1704769673252,"userId":"System"},{"channelId":"1","messageSource":1,"messageType":4,"platform":"qy","
platformMessageId":"70859054054","platformSessionId":"19213508897","rawMsg":"
{\"itemList\":[{\"itemId\":\"470196502\"}]}","serviceId":"System","sessionInteraction":1,"testMode":0,"timestamp":
1704769681697,"userId":"System"},{"channelId":"1","messageSource":1,"messageType":1,"platform":"qy","
platformMessageId":"70859054055","platformSessionId":"19213508897","rawMsg":"{\"text\":\"你好\"}","serviceId":"System","
sessionInteraction":1,"testMode":0,"timestamp":1704769684894,"userId":"System"},{"channelId":"1","messageSource":1,"
messageType":1,"platform":"qy","platformMessageId":"70859054057","platformSessionId":"19213508897","rawMsg":"{\"text\":
\"退换货\"}","serviceId":"System","sessionInteraction":1,"testMode":0,"timestamp":1704769688319,"userId":"System"},{"
channelId":"1","messageSource":1,"messageType":3,"platform":"qy","platformMessageId":"70859054059","platformSessionId":"
19213508897","rawMsg":"{\"orderList\":[{\"orderId\":\"529331544\"}]}","serviceId":"System","sessionInteraction":1,"
testMode":0,"timestamp":1704769697805,"userId":"System"},{"channelId":"1","messageSource":1,"messageType":3,"platform":"
qy","platformMessageId":"70859054061","platformSessionId":"19213508897","rawMsg":"
{\"orderList\":[{\"orderId\":\"529331544\"}],\"itemList\":[{\"itemId\":\"1318002\"}]}","serviceId":"System","
sessionInteraction":1,"testMode":0,"timestamp":1704769702167,"userId":"System"},{"channelId":"1","messageSource":1,"
messageType":4,"platform":"qy","platformMessageId":"70859054063","platformSessionId":"19213508897","rawMsg":"
{\"itemList\":[{\"itemId\":\"470188637\"}]}","serviceId":"System","sessionInteraction":1,"testMode":0,"timestamp":
1704769705074,"userId":"System"},{"channelId":"1","messageSource":1,"messageType":4,"platform":"qy","
platformMessageId":"70859054065","platformSessionId":"19213508897","rawMsg":"
{\"itemList\":[{\"itemId\":\"470186676\"}]}","serviceId":"System","sessionInteraction":1,"testMode":0,"timestamp":
1704769709273,"userId":"System"},{"channelId":"1","messageSource":1,"messageType":1,"platform":"qy","
platformMessageId":"70859054067","platformSessionId":"19213508897","rawMsg":"{\"text\":\"无答案1\"}","serviceId":"
System","sessionInteraction":1,"testMode":0,"timestamp":1704769717631,"userId":"System"}]}' -H "Content-Type:
application/json"
