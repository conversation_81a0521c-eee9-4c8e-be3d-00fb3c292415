#### 智能客服机器人导购

机器人端的导购功能说明

场景：被动触发导购推荐卡片

##### 字段

输入：

```java
public class GuideReq {
    /**
     * 导购输入
     * (a) query 查询语句
     * (b) uid  用户id
     */
    private String query;
    private String uid;
}
```

输出：

```java
public class GuideRslt {
    /**
     * 导购结果
     * (a) itemId, Long， 推荐的商品id
     * (b) itemNickName, String, 推荐的商品中文名称
     * (c) rcmdReason, List<String></String>, 商品推荐语 (可选)
     */

    private static final String ITEM_NICK_NAME = "None";
    private static final String RCMD_REASON = "";
    private static final List<String> RCMD_REASON_LIST = new ArrayList<>();
    private static final String BOT_DEFAULT_TEXT = "小选为您诚心推荐~";

    private Long itemId;
    private String itemNickName = ITEM_NICK_NAME;
    private List<String> rcmdReason = RCMD_REASON_LIST;


    public GuideRslt(Long itemId){

        this.itemId = itemId;
    }
}
```

##### 调用方式

一般情况嵌入PipelineService中使用，单独调用请使用：

```shell
# release
curl -X POST 'http://**************:8083/yanxuan-intelli-bot/guide.do' -d '{    "query": "热水袋",    "uuid": "69970872"}' -H "Content-Type: application/json"
```

```shell
# output
{"code":200,"data":{"itemId":3826035,"itemNickName":"日式电热暖水袋","rcmdReason":["甄选性价比超高的【日式电热暖水袋】分享给您，希望您能满意~"]}}
```

##### 实现方案

参考[导购](https://kttfkmbfmy.feishu.cn/docs/doccnaNb6igBiqi00X4eujPmIMb#) 中的导购一期实现方案

