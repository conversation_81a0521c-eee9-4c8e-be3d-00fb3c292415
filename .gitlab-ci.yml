variables:
  SERVICE_CODE: 'yanxuan-intelli-bot'

stages:
  - inspection
  - unitTest
  - package
  - upload
  - upload-bee

#inspection
inspection-job:
  stage: inspection
  #线上分支执行代码检查
  only:
    - /^(master|release|hotfix).*$/
  script:
    - ci_tools inspection $SERVICE_CODE $CI_COMMIT_REF_NAME $CI_PIPELINE_ID
  tags:
    - ci-backend

unitTest-job:
  stage: unitTest
  #dev和feature分支执行单测
  only:
    - /^(dev|feature).*$/
  script:
    - mvn clean org.jacoco:jacoco-maven-plugin:0.8.2:prepare-agent test org.jacoco:jacoco-maven-plugin:0.8.2:report -Dmaven.test.failure.ignore=true
    - ci_tools report_unit_test_result $TARGET_FILE_PATH/site/jacoco/jacoco.xml $SERVICE_CODE $CI_COMMIT_REF_NAME $CI_PIPELINE_ID
  tags:
    - ci-backend

# 前置检查
include:
  - project: tech/ci-operator
    file: /submodels/precheck.gitlab-ci.yml
    ref: master


################### 测试环境镜像构建 ###################
test_package:
  stage: package
  script:
    - mvn clean package -U -am -Ptest -Dmaven.test.skip=true
    - mv build/yanxuan-intelli-bot.jar ${SERVICE_CODE}.jar
    - mv deploy/setenv-test.sh  -t ./
    - mv setenv-test.sh setenv.sh
    - zip -r ${CI_PROJECT_NAME}.zip ${SERVICE_CODE}.jar setenv.sh* conf/
  tags:
    - ci-backend
  artifacts:
    paths:
      - ${CI_PROJECT_NAME}.zip
      - swagger/
    expire_in: 1h
  only:
    - /^(feature|release|hotfix).*$/

# 测试环境普通制品上传及自动发布
test_upload:
  stage: upload
  script:
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=test --artifactPath=$CI_PROJECT_NAME.zip  --artifactVersion=$ARTIFACT_VERSION --autoDepoly=false --clusterId=2
  tags:
    - ci-backend
  only:
    - dev
    - /^feature.*$/
    - /^release.*$/
  dependencies:
    - test_package

# 测试环境swagger文件上传
test_upload_bee:
  stage: upload-bee
  script:
    - cd swagger
    - beeUpload $SERVICE_CODE $CI_BUILD_REF_NAME $GITLAB_USER_NAME $GITLAB_USER_EMAIL
  tags:
    - ci-backend
  only:
    - dev
    - /^feature.*$/
    - /^release.*$/
  dependencies:
    - test_package

################### 预发环境镜像构建 ###################
release_package:
  stage: package
  script:
    - mvn clean package -U -am -Prelease -Dmaven.test.skip=true
    - mv build/yanxuan-intelli-bot.jar ${SERVICE_CODE}.jar
    - mv deploy/setenv-release.sh  -t ./
    - mv setenv-release.sh setenv.sh
    - zip -r ${CI_PROJECT_NAME}.zip ${SERVICE_CODE}.jar setenv.sh* conf/
  tags:
    - ci-backend
  artifacts:
    paths:
      - ${CI_PROJECT_NAME}.zip
      - swagger/
    expire_in: 1h
  only:
    - /^(feature|release|hotfix).*$/


# 预发环境swagger文件上传
release_upload_bee:
  stage: upload-bee
  script:
    - cd swagger
    - beeUpload $SERVICE_CODE $CI_BUILD_REF_NAME $GITLAB_USER_NAME $GITLAB_USER_EMAIL
  tags:
    - ci-backend
  only:
    - dev
    - /^feature.*$/
    - /^release.*$/
  dependencies:
    - release_package


################### 线上环境制品构建（拉取前端代码）、上传###################
online_package:
  stage: package
  script:
    - mvn clean package -U -am -Ponline -Dmaven.test.skip=true
    - mv build/yanxuan-intelli-bot.jar ${SERVICE_CODE}.jar
    - mv deploy/setenv-online.sh  -t ./
    - mv setenv-online.sh setenv.sh
    - zip -r ${CI_PROJECT_NAME}.zip ${SERVICE_CODE}.jar setenv.sh* conf/
  tags:
    - ci-backend
  artifacts:
    paths:
      - ${CI_PROJECT_NAME}.zip
    expire_in: 1h
  only:
    - /^(release|hotfix).*$/

################### 预发环境制品上传 ###################
release_upload:
  stage: upload
  script:
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=release --artifactPath=${CI_PROJECT_NAME}.zip --artifactVersion=$ARTIFACT_VERSION --module=default  --autoDepoly=true --clusterId=5
  tags:
    - ci-backend
  dependencies:
    - release_package
  only:
    - /^(feature|release|hotfix).*$/


################### 线上环境制品上传 ###################
online_upload:
  stage: upload
  script:
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=online --artifactPath=${CI_PROJECT_NAME}.zip --artifactVersion=$ARTIFACT_VERSION --module=default
  environment: online
  tags:
    - ci-backend
  dependencies:
    - online_package
  only:
    - /^(release|hotfix).*$/
