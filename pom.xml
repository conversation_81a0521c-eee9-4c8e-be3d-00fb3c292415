<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.netease.yx</groupId>
    <artifactId>yanxuan-intelli-bot</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <name>yanxuan-intelli-bot</name>
    <modules>
        <module>yanxuan-intelli-bot-common-dal</module>
        <module>yanxuan-intelli-bot-common-util</module>
        <module>yanxuan-intelli-bot-common-service-integration</module>
        <module>yanxuan-intelli-bot-core-model</module>
        <module>yanxuan-intelli-bot-core-service</module>
        <module>yanxuan-intelli-bot-biz</module>
        <module>yanxuan-intelli-bot-biz-service-impl</module>
        <module>yanxuan-intelli-bot-interfaces-web</module>
        <module>yanxuan-intelli-bot-interfaces-facade</module>
        <module>yanxuan-intelli-bot-assembly</module>
        <module>yanxuan-intelli-bot-test</module>
    </modules>
    <!--properties统一管理版本号-->
    <properties>
        <!--定义整个工程的版本-->
        <spring.boot.version>2.3.5.RELEASE</spring.boot.version>
        <!--apolloY版本-->
        <apolloY.client.version>1.0.2-RELEASE</apolloY.client.version>
        <!-- dynamic logger level版本  -->
        <dll.version>1.0-SNAPSHOT</dll.version>
    </properties>
    <!--父pom不做具体的jar包依赖-->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.apolloY</groupId>
                <artifactId>apolloY-client</artifactId>
                <version>${apolloY.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.netease.yanxuan</groupId>
                <artifactId>dll</artifactId>
                <version>${dll.version}</version>
                <type>jar</type>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.16</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.6</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.45</version>
            </dependency>
            <dependency>
                <groupId>commons-dbcp</groupId>
                <artifactId>commons-dbcp</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.xmemcached</groupId>
                <artifactId>xmemcached</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.hankcs</groupId>
                <artifactId>hanlp</artifactId>
                <version>portable-1.6.6</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <repositories>
        <repository>
            <id>hzntes-mail</id>
            <name>NTES-MAIL-HZ Maven Repository</name>
            <url>http://repo.mail.netease.com/artifactory/repo</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.5.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.6</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>utf8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>generate-service-docs</id>
                        <phase>compile</phase>
                        <configuration>
                            <doclet>com.netease.yanxuan.swagger.plugin.ServiceDoclet</doclet>
                            <docletArtifact>
                                <groupId>com.netease.yanxuan</groupId>
                                <artifactId>swagger-plugin</artifactId>
                                <version>1.1-SNAPSHOT</version>
                            </docletArtifact>
                            <!--failOnError设置为false，在javadoc插件出错的情况下不会阻止编译和打包流程-->
                            <failOnError>false</failOnError>
                            <reportOutputDirectory>${project.build.outputDirectory}</reportOutputDirectory>
                            <useStandardDocletOptions>false</useStandardDocletOptions>
                            <additionalOptions>
                                <additionalOption>-docBasePath ${project.basedir}/swagger</additionalOption>
                                <additionalOption>-projectVersion ${project.version}</additionalOption>
                            </additionalOptions>
                        </configuration>
                        <goals>
                            <goal>aggregate-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
            </properties>
        </profile>
        <profile>
            <id>pressure</id>
            <properties>
                <env>pressure</env>
            </properties>
        </profile>
        <profile>
            <id>release</id>
            <properties>
                <env>release</env>
            </properties>
        </profile>
        <profile>
            <id>online</id>
            <properties>
                <env>online</env>
            </properties>
        </profile>
    </profiles>
</project>
