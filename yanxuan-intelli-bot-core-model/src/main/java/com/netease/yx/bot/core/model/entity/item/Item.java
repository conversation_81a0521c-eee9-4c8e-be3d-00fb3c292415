/**
 * @(#)Item.java, 2020/9/27.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.item;

/**
 * <AUTHOR> @ corp.netease.com)
 */

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Item implements Serializable {
    /**
     * 服装一级类目id
     */
    public static final Long CATE_CLOTH = 1000993L;
    /**
     * 鞋靴一级类目id
     */
    public static final Long CATE_SHOE = 1003161L;
    /**
     * 母婴类目
     */
    public static final Long CATE_BABY = 1000181L;

    public static final String KEY_ITEM_ID = "item_id";
    public static final String KEY_ITEM_NAME = "item_name";
    public static final String KEY_ITEM_NICK_NAME = "nick_name";
    public static final String KEY_ITEM_PIC_URL = "list_pic_url";
    public static final String KEY_PHY_CATEGORY1_ID = "phy_category1_id";
    public static final String KEY_PHY_CATEGORY2_ID = "phy_category2_id";
    public static final String KEY_PHY_CATEGORY3_ID = "phy_category3_id";
    public static final String KEY_PHY_CATEGORY4_ID = "phy_category4_id";

    private Long itemId;
    private String itemName;
    private String nickName;
    private String picUrl;
    private Long phyCategory1Id;
    private Long phyCategory2Id;
    private Long phyCategory3Id;
    private Long phyCategory4Id;
    private boolean soldOut;

    public static void main(String[] args) {
        System.out.println(StringUtils.joinWith(",", "A", "B", "C"));
    }

    public boolean isNeedSizeCate() {
        return CATE_CLOTH.equals(phyCategory1Id) || CATE_SHOE.equals(phyCategory1Id) || CATE_BABY.equals(phyCategory1Id);
    }

    public String joinPhyCateGoryId() {
        return StringUtils.joinWith(",", phyCategory1Id, phyCategory2Id, phyCategory3Id, phyCategory4Id);
    }
}