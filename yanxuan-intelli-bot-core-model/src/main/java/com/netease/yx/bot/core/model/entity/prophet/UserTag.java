package com.netease.yx.bot.core.model.entity.prophet;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum UserTag implements EnumIntegerInterface<UserTag> {
    NULL(0, "NULL"),
    V1(1, "V1"),
    V2(2, "V2"),
    V3(3, "V3"),
    V4(4, "V4"),
    V5(5, "V5"),
    V6(6, "V6"),

    NEW_USER(10, "新用户");

    private final int value;

    private final String desc;

    @Override
    public UserTag genEnumByIntValue(int intValue) {
        for (UserTag userTag : UserTag.values()) {
            if (userTag.getValue() == intValue) {
                return userTag;
            }
        }
        return UserTag.NULL;
    }
}
