package com.netease.yx.bot.core.model.entity.intentProduct;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Data
@AllArgsConstructor
public class FewShotTFResp {
    private final String STRONG = "strong";
    private FewShotTFDataResp DEFAULT_FS_RESP = new FewShotTFDataResp("其它", "other", "strong", 1f);
    private List<FewShotTFDataResp> predictions;

    public FewShotTFResp() {
        this.predictions = new ArrayList<>(Arrays.asList(DEFAULT_FS_RESP));
    }

    public void sort() {
        this.predictions.sort(new Comparator<FewShotTFDataResp>() {
            @Override
            public int compare(FewShotTFDataResp o1, FewShotTFDataResp o2) {
                if (o1.getConfidence() == o2.getConfidence()) {
                    return 0;
                }
                return STRONG.equals(o1.getConfidence()) ? -1 : 1;
            }
        });
    }

    public Integer getRecallNums() {
        if (predictions == null) {
            return 0;
        }

        return predictions.size();
    }

    public List<String> getIntentList() {
        List<String> output = new ArrayList<>();
        for (FewShotTFDataResp fewShotTFDataResp : predictions) {
            output.add(fewShotTFDataResp.getIntentList());
        }
        return output;
    }

    public List<String> getIntentCHList() {
        List<String> output = new ArrayList<>();
        for (FewShotTFDataResp fewShotTFDataResp : predictions) {
            output.add(fewShotTFDataResp.getIntentCHList());
        }
        return output;
    }

    public List<String> getConfidenceList() {
        List<String> output = new ArrayList<>();
        for (FewShotTFDataResp fewShotTFDataResp : predictions) {
            output.add(fewShotTFDataResp.getConfidence());
        }
        return output;
    }

    public List<Float> getScoreList() {
        List<Float> output = new ArrayList<>();
        for (FewShotTFDataResp fewShotTFDataResp : predictions) {
            output.add(fewShotTFDataResp.getScore());
        }
        return output;
    }

    public void resetConf() {
        if (getRecallNums() > 1) {
            for (FewShotTFDataResp resp : this.predictions) {
                if (STRONG.equals(resp.getConfidence())) {
                    resp.resetConf();
                }
            }
        }
    }

    public List<String> getStrongIntent() {
        List<String> res = new ArrayList<>();
        for (int i = 0; i < getRecallNums(); i++) {
            if (STRONG.equals(getConfidenceList().get(i))) {
                res.add(getIntentList().get(i));
            }
        }
        return res;
    }
}
