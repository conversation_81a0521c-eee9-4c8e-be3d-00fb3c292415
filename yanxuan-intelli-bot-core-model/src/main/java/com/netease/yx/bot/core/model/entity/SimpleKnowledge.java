/**
 * @(#)SimpleKnowledge.java, 2020/8/4.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleKnowledge implements Comparable<SimpleKnowledge> {
    private int index;
    private long knowledgeId;
    private String content;
    private Double score;

    public SimpleKnowledge(long knowledgeId, String content, Double score) {
        this.knowledgeId = knowledgeId;
        this.content = content;
        this.score = score;
    }

    /**
     * 从大到小
     *
     * @param o
     * @return
     */
    @Override
    public int compareTo(SimpleKnowledge o) {
        return -Double.compare(score, o.score);
    }
}