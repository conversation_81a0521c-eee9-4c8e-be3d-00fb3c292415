package com.netease.yx.bot.core.model.entity.check;

import lombok.Data;

import java.util.List;

@Data
public class NeedCheckKnowledge {
    // 知识编号，如果是excel批量上传，就是第一列知识编号，如果是单个编辑，就填0
    private long id;
    // 知识类型, 1. FAQ, 2. 内部知识
    // 必选
    private int knowledgeType;
    // 标准问
    private String stdQuestion;
    // 客服业务分类，存id，逗号隔开，FAQ使用
    // 可为空
    private String busCate;
    // 内部知识库分类，存id，逗号隔开，内部知识使用
    // 可为空
    private String kmCate;
    // 商品标签，商品id与关键字
    // 可为空
    private String itemInfoLabel;
    // 商品标签，物理类目
    // 可为空
    private String itemCateLabel;
    // 问题用途: 1.对内，2.对外
    // 不为空
    private int knowledgeUse;
    // 相似问
    // 可为空
    private List<String> similarQuestions;
}
