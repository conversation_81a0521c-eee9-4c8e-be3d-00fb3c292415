/**
 * @(#)Platform.java, 2020/9/3.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.constant;

/**
 * <AUTHOR> @ corp.netease.com)
 */
public enum PlatformType {
    /**
     *
     */
    NULL,
    /**
     *
     */
    APP,
    /**
     *
     */
    WECHAT,
    /**
     *
     */
    WEB;

    public static PlatformType get(String typeStr) {
        PlatformType platformType = APP;
        try {
            if (typeStr != null) {
                platformType = PlatformType.valueOf(typeStr);
            }
        } catch (Exception e) {
            platformType = APP;
        }
        return platformType;
    }
}