package com.netease.yx.bot.core.model.entity.intentProduct;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IntentProductDataResp {
    private static final Integer INIT_IDX = 1;
    private static final Float INIT_SCORE = 1f;
    private static final String REQUEST_OBJ_KEY = "instances";
    private static final String CONFIDENCE_STRONG = "strong";
    private static final String CONFIDENCE_MEDIAN = "median";
    private static final String INTENT_OTHER = "other";


    private Integer idx;
    private String intentCh;
    private String intent;
    private String knowledgeType;
    private Float prob;
    private String confidence;

    public static IntentProductDataResp buildDefault() {
        return new IntentProductDataResp(INIT_IDX, "其它", INTENT_OTHER, "", INIT_SCORE, CONFIDENCE_STRONG);
    }
}
