/**
 * @(#)ItemRcmdReq.java, 2020/12/20.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.rcmd;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ItemRcmdReq {
    public static final String VALUE_ITEMID_RCMD_VER = "1.0.0";
    public static final String VALUE_ITEMID_SCENE = "relatedItemRecall";
    public static final String VALUE_GUESS_RCMD_VER = "3.0.1.15.2";
    public static final String VALUE_GUESS_SCENE = "home_guess_like";
    public static final String VALUE_PRODUCT = "yanxuan-intelli-bot";

    private String userId;
    private boolean newUser;
    private String rcmdVer;
    private String scene;
    private String recallParam;
    private String product;

    public ItemRcmdReq(String userId, boolean newUser, String recallParam) {
        this.userId = userId;
        this.newUser = newUser;
        this.recallParam = recallParam;
        this.rcmdVer = VALUE_ITEMID_RCMD_VER;
        this.scene = VALUE_ITEMID_SCENE;
        this.product = VALUE_PRODUCT;
    }

    public ItemRcmdReq(String userId, boolean newUser) {
        this.userId = userId;
        this.newUser = newUser;
        this.recallParam = recallParam;
        this.rcmdVer = VALUE_GUESS_RCMD_VER;
        this.scene = VALUE_GUESS_SCENE;
    }
}