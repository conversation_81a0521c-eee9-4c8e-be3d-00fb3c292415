package com.netease.yx.bot.core.model.entity.search;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchItemInfoFromAppIndex {
    @JsonAlias("item_id")
    private long itemId;

    @JsonAlias("item_name")
    private String itemName;

    @JsonAlias("item_nickname_single")
    private String itemNickNameSingle;

    @JsonAlias("center_word")
    private String centerWord;

    private double score;
}
