package com.netease.yx.bot.core.model.entity.kfkm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedSimilarQuestion {
    public static final int UN_SIMILAR_FLAG = 0;
    public static final int SIMILAR_FLAG = 1;

    /**
     * "content": "问题内容\n",
     * "id": 8,
     * "itemId": 42,
     * "knowledgeId": 1,
     * "operator": "操作人\n",
     * "similarFlag": 0,
     * "source": 1,
     * "taskId": "最新的内容是基于哪个训练任务id操作的，可以为空\n"
     */
    private String content;
    private long id;
    private long itemId;
    private long knowledgeId;
    private int similarFlag;
    private int source;
    private String taskId;
}
