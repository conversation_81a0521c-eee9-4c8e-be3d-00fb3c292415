/**
 * @(#)DeviceType.java, 2020/12/30.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.constant;

/**
 * <AUTHOR> @ corp.netease.com)
 */
public enum DeviceType {
    /**
     *
     */
    NULL,
    /**
     *
     */
    ANDROID,
    /**
     *
     */
    IOS,
    /**
     *
     */
    OTHER;

    public static DeviceType get(String typeStr) {
        DeviceType deviceType = OTHER;
        try {
            if (typeStr != null) {
                deviceType = DeviceType.valueOf(typeStr.toUpperCase());
            }
        } catch (Exception e) {
            deviceType = OTHER;
        }
        return deviceType;
    }
}