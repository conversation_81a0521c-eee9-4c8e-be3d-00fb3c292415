package com.netease.yx.bot.core.model.entity.channel;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelQaAddiReq implements Serializable {
    private long channelId;
    @JsonAlias("platformSessionId")
    private String sessionId;
    private String serviceId;
    // 主站APP的商品id
    private long itemId;
    // 消息粒度的平台原始的订单id
    private String orderId;
    // 主站APP的商品id，因为可能是1对多的关系，所以存在这里
    private List<Long> itemIds;
    // 消息粒度的平台原始商品卡片抽取出来的id，可能是item，也可能是sku
    private String platformRawItemCardId;
    // 消息粒度的平台原始商品卡片抽取出来的id，转换成itemId
    private String platformItemId;
    // 商品的物理类目层次
    private String itemPhyCategoryIdStr;
}
