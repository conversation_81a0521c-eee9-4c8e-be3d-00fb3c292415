package com.netease.yx.bot.core.model.entity.guide;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QPReq {
    private static final String VERSION = "1.0";
    private static final String UUID = "uuid";
    private static final boolean NEED_REWRITE = false;

    private String query;
    private String uuid;
    private boolean needRewrite = NEED_REWRITE;
    private String version = VERSION;

    public QPReq(String query, String uuid) {
        this.query = query;
        this.uuid = uuid;
    }
}
