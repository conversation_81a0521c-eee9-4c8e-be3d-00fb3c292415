package com.netease.yx.bot.core.model.entity.attr;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttrResp {
    private long attrId;
    private String attrName;
    private List<Long> valueIds;
    private List<String> attrValues;
    private int source;
    private float score;
    // 0-类目属性， 1-商品属性
    private int type;

    // 暂时兼容老的，后面要废弃
    private List<String> similarAttrNames;

    private double recallScoreFromText;
    private double recallScoreFromEmbedding;
    private double rankScoreFromSimilar;
    private double rankScoreFromFaq;
}
