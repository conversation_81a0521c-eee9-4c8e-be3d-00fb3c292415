package com.netease.yx.bot.core.model.nlpserver.classification;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 非层次分类标准返回
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassificationResp {
    // 多个样本的每个类的分数
    private List<List<Double>> scores;
    // 多个样本的类目下标，第一层List是表示多个样本，第二层List是表示可能的多标签
    private List<List<Integer>> labels;
    // 类目体系的定义
    private Map<Integer, String> cateIndex2NameMap;
}
