package com.netease.yx.bot.core.model.entity.kfkm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlgoSimilarQuestion {
    // id
    private long id;
    // 内容
    private String content;
    // 清理后内容
    private String cleanContent;
    // 清理后内容分词
    private String cleanContentTerms;
    // 状态: 1.正常，2.已删除
    private int status = 1;
}
