package com.netease.yx.bot.core.model.nlpserver.chat.openai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatMessage {
    private static final String ROLE_SYSTEM = "system";
    private static final String ROLE_USER = "user";
    private static final String ROLE_ASSISTANT = "assistant";
    // 角色
    private String role;
    // 内容
    private String content;

    public static ChatMessage buildSystemMessage(String content) {
        return new ChatMessage(ROLE_SYSTEM, content);
    }

    public static ChatMessage buildUserMessage(String content) {
        return new ChatMessage(ROLE_USER, content);
    }

    public static ChatMessage buildAssistantMessage(String content) {
        return new ChatMessage(ROLE_ASSISTANT, content);
    }

    public static boolean checkRole(String role) {
        return StringUtils.equals(role, ROLE_SYSTEM) || StringUtils.equals(role, ROLE_USER) || StringUtils.equals(role, ROLE_ASSISTANT);
    }

    public static String chooseRoleByIndex(int index) {
        if (index % 2 == 0) {
            return ROLE_USER;
        } else {
            return ROLE_ASSISTANT;
        }
    }
}
