package com.netease.yx.bot.core.model.entity.kfkm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlgoKnowledgeWithId {
    private long knowledgeId;
    private AlgoKnowledge knowledge;

    private List<AlgoSimilarQuestion> similarQuestions;
    // 关键词组
    private List<AlgoKeyword> keywords;
    // 答案对象
    private List<AlgoAnswer> answers;
}
