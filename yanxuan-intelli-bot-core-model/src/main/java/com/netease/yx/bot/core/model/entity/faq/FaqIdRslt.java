package com.netease.yx.bot.core.model.entity.faq;

import com.netease.yx.bot.core.model.constant.ReplyState;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/20.
 */

@Data
@AllArgsConstructor
public class FaqIdRslt implements Serializable {

    public static final int TYPE_NO_ANSWER = 0;
    public static final int TYPE_FAQ_PRECISE = 1;
    public static final int TYPE_FAQ_FUZZY = 2;
    public static final Float PRECISION_SCORE = 0.8f;
    public static final Long EMPTY_KID = 0L;
    private static final String MULTI_ANSWER_QUESTION_KID_PREFIX = "400";
    /**
     * 成员变量：
     * 1. state ： 请求返回状态字段
     * 2. type ： 返回结果类型
     * 3. faqIds：匹配到的faq答案
     */

    private ReplyState state = ReplyState.SUCCESS;
    private int type;
    private List<Long> faqIds;
    private List<String> faqQues;
    private List<Double> scores;

    public FaqIdRslt() {
        this.type = TYPE_NO_ANSWER;
        this.faqIds = new ArrayList<>();
        this.faqQues = new ArrayList<>();
        this.scores = new ArrayList<>();
    }

    public FaqIdRslt(List<Long> faqIds) {
        this.faqIds = faqIds;
        this.type = TYPE_FAQ_FUZZY;
    }

    public boolean isNotNull() {
        return type != TYPE_NO_ANSWER && faqIds != null && faqIds.size() > 0;
    }
}
