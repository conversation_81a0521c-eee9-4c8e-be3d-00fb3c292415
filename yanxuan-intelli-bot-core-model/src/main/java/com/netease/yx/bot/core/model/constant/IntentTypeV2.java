package com.netease.yx.bot.core.model.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IntentTypeV2 {
    /**
     * 0-闲聊，1-商品属性相关，2-日常业务相关，3-人工和投诉相关，4-商品卡片， 5-订单卡片，-1-未知
     */
    /**
     * 闲聊
     */
    CHITCHAT(0),
    /**
     * 商品知识问答
     */
    KBQA(1),
    /**
     * FAQ
     */
    FAQ(2),
    /**
     * 特殊意图
     */
    SPECIAL(3),
    /**
     * 商品卡片
     */
    ITEM_CARD(4),
    /**
     * 订单卡片
     */
    ORDER_CARD(5),
    /**
     * 导购
     */
    GUIDE(6),

    UNKNOWN(-1);

    private final int code;

    public boolean isCard() {
        return code == IntentTypeV2.ITEM_CARD.code || code == IntentTypeV2.ORDER_CARD.code;
    }

    public boolean isUseful() {
        return code == IntentTypeV2.KBQA.code || code == IntentTypeV2.FAQ.code || code == IntentTypeV2.GUIDE.code;
    }
}
