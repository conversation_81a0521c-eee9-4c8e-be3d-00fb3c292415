/**
 * @(#)ProphetReq.java, 2020/9/3.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

import lombok.Data;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class ProphetReq {
    /**
     * 用户ID
     */
    private long userId;
    /**
     * 会话ID
     */
    private long sessionId;
    /**
     * 来源平台，APP/WECHAT/WEB
     */
    private String platform;
    /**
     * source标识
     */
    private String source;
    /**
     * 售前标识
     */
    private boolean presale;
    /**
     * APP携带订单ID
     */
    private long orderId;
    /**
     * APP携带商品ID
     */
    private long itemId;
    /**
     * 设备类型，IOS，ANDROID, OTHER
     */
    private String device;
    /**
     * 返回的FAQ数量
     */
    private int returnFaqNum = 5;
    /**
     * 用户R等级
     */
    private int userR;
    /**
     * 用户v等级
     */
    private int userV;
    /**
     * 24H访问人工次数
     */
    private int userRGCount24H;
    /**
     * 48H访问人工次数
     */
    private int userRGCount48H;
    /**
     * 是否是新用户
     */
    private boolean newUser;
}