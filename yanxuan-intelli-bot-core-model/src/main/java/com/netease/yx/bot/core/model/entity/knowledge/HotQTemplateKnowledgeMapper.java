/**
 * @(#)HotQTemplateKnowledgeMapper.java, 2020/9/17.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.knowledge;

import lombok.Data;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class HotQTemplateKnowledgeMapper implements Comparable<HotQTemplateKnowledgeMapper> {
    private long id;
    private long templateId;
    private long knowledgeId;
    private int orderNum;

    /**
     * 小的排序靠前
     *
     * @param o
     * @return
     */
    @Override
    public int compareTo(HotQTemplateKnowledgeMapper o) {
        return Double.compare(orderNum, o.orderNum);
    }
}