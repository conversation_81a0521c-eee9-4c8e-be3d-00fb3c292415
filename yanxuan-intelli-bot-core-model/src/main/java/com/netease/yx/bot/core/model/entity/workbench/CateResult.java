/**
 * @(#)CateIdResult.java, 2019/11/20.
 * <p/>
 * Copyright 2019 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.workbench;

import lombok.Data;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class CateResult {
    /**
     * 类目的叶子类目ID
     */
    private Long leafCateId;
    /**
     * 类目的叶子类目名称
     */
    private String cateName;
    /**
     * 类目的分数，为0到1之间
     */
    private Double score;
    /**
     * 预留的摘要字段，考虑可能总结会和预测的类目联动
     */
    private SummaryResult cateSummaryResult;

    public CateResult(Long leafCateId, String cateName, Double score) {
        this.leafCateId = leafCateId;
        this.cateName = cateName;
        this.score = score;
        this.cateSummaryResult = SummaryResult.buildDefaultResult();
    }
}
