/**
 * @(#)Cate.java, 2019/11/23.
 * <p/>
 * Copyright 2019 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.workbench;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 * 类目基本信息
 */
@Data
public class Cate {
    /**
     * faq类目及相应频繁问题的数据结构
     * 相关表：YX_FAQ_CATE / YX_CATE_FREQ_FAQ
     */
    private long cateId;
    private long superId;
    private int level;
    private String cateName;
    /**
     * 排序好的
     */
    private List<Long> freqFaqIds;

    public Cate(long cateId, String cateName) {
        this.cateId = cateId;
        this.cateName = cateName;
    }

    public Cate(long cateId) {
        this.cateId = cateId;
        this.cateName = StringUtils.EMPTY;
    }

}