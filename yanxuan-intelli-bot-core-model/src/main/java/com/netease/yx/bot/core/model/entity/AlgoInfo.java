/**
 * @(#)AlgoInfo.java, 2020/5/29.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @ corp.netease.com)
 * 对应 MYSQL yx_bot.TB_YX_KFKM_QUESTION_ALGO_INFO
 * 客服配置的知识库经过转化后的数据，共算法直接使用
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlgoInfo {
    public static final int QUESTION_TYPE_STANDARD = 1;
    public static final int QUESTION_TYPE_SIMILAR = 2;
    public static final int QUESTION_TYPE_EXTEND = 3;
    public static final int QUESTION_TYPE_MINING = 4;

    public static final int STATUS_VALID = 1;
    public static final int STATUS_UNVALID = 0;
    public static final int STATUS_INTERNAL = 2;
    public static final int STATUS_FAQ = 1;

    public static final String CORPUS_ID_PREFFIX_STANDARD = "STANDARD-";
    public static final String CORPUS_ID_PREFFIX_SIMILAR = "SIMILAR-";

    public static final int CORPUS_TYPE_STANDARD = 1;
    public static final int CORPUS_TYPE_SIMILAR = 2;
    public static final String ITEM_ID_DEFAULT = "-1";
    public static final String CATE_ID_DEFAULT = "-1";

    /**
     * 自增id
     */
    private long id;
    /**
     * 语料id， 一般是由语料类型标志 加 唯一id构成，如 standard-1111
     */
    private String corpusId;
    /**
     * 语料类型, 1. 标准问题, 2. 相似问题 3. 知识扩展 4.挖掘补充
     */
    private int corpusType;
    /**
     * 知识id
     */
    private long knowledgeId;
    /**
     * 生效状态，1-生效，0-失效
     */
    private int status;
    /**
     * 商品ID，当前问题是绑定到商品时，有正值，默认-1
     */
    private String itemId = "-1";
    /**
     * 物理类目ID，当前问题是绑定到类目时，有正值，默认-1
     */
    private String cateId = "-1";
    /**
     * 物理类目的层级，当前问题是绑定到类目时，有正值，默认-1
     */
    private String cateLevel = "-1";
    /**
     * 问题原文
     */
    private String question;
    /**
     * 清洗后的问题文本
     */
    private String cleanQuestion;
    /**
     * 分词结果，用空格分割
     */
    private String terms;
    /**
     * 问题表征向量，目前是128维
     */
    private String vector;
    /**
     * 完整的term对象json
     */
    private String termsJson;
    /**
     * 创建时间
     */
    private long createTime;
    /**
     * 更新时间
     */
    private long updateTime;
}