package com.netease.yx.bot.core.model.train;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TrainContext {
    private String platform;
    private Long channelId;
    private Long sessionId;
    // kefuMessageId;
    private Long messageId;
    private List<RecommendKnowledge> recommendKnowledge;
    private Long itemId;
    private String platformItemId;
    private String platformRawItemCardId;
    private String itemPhyCategoryIdStr;
    private int intentType;
    private String messageContent;

    private long intentId;

    // 0-人工、1-机器人
    private int sessionInteraction;

    private boolean noRcmdKnowledge;
}
