package com.netease.yx.bot.core.model.entity.prophet;

import com.alibaba.fastjson.JSON;
import com.netease.yx.bot.core.model.entity.UserOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProphetPipelineLog {
    public static final String KEY_OTHER_NEW_USER = "new_user";
    private static final String KEY_INPUT = "input";
    private static final String KEY_CENTER_USER_ORDER = "centerUserOrder";
    private static final String KEY_USER_ORDERS = "userOrders";
    private static final String KEY_CONFIG = "config";
    private static final String KEY_MODEL = "model";
    private static final String KEY_OTHER = "other";
    private static final String KEY_OUTPUT = "output";
    private static final String EMPTY_MAP_STR = "{}";
    private static final String EMPTY_LIST_STR = "[]";
    private ProphetReq input;
    private UserOrder centerUserOrder;
    private List<UserOrder> userOrders;
    private List<Long> config;
    private List<Long> model;
    private Map<String, Object> other;
    private ProphetResp output;

    public static Map<String, String> buildMap(ProphetPipelineLog prophetPipelineLog) {
        Map<String, String> map = new HashMap<>(10);
        map.put(KEY_INPUT, EMPTY_MAP_STR);
        map.put(KEY_CENTER_USER_ORDER, EMPTY_MAP_STR);
        map.put(KEY_USER_ORDERS, EMPTY_LIST_STR);
        map.put(KEY_CONFIG, EMPTY_LIST_STR);
        map.put(KEY_MODEL, EMPTY_LIST_STR);
        map.put(KEY_OTHER, EMPTY_MAP_STR);
        map.put(KEY_OUTPUT, EMPTY_MAP_STR);

        if (prophetPipelineLog != null) {
            if (prophetPipelineLog.getInput() != null) {
                map.put(KEY_INPUT, JSON.toJSONString(prophetPipelineLog.getInput()));
            }
            if (prophetPipelineLog.getCenterUserOrder() != null) {
                map.put(KEY_CENTER_USER_ORDER, JSON.toJSONString(prophetPipelineLog.getCenterUserOrder()));
            }
            if (prophetPipelineLog.getUserOrders() != null) {
                map.put(KEY_USER_ORDERS, JSON.toJSONString(prophetPipelineLog.getUserOrders()));
            }
            if (prophetPipelineLog.getConfig() != null) {
                map.put(KEY_CONFIG, JSON.toJSONString(prophetPipelineLog.getConfig()));
            }
            if (prophetPipelineLog.getModel() != null) {
                map.put(KEY_MODEL, JSON.toJSONString(prophetPipelineLog.getModel()));
            }
            if (prophetPipelineLog.getOther() != null) {
                map.put(KEY_OTHER, JSON.toJSONString(prophetPipelineLog.getOther()));
            }
            if (prophetPipelineLog.getOutput() != null) {
                map.put(KEY_OUTPUT, JSON.toJSONString(prophetPipelineLog.getOutput()));
            }
        }
        return map;
    }
}
