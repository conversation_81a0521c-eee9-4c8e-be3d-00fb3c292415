/**
 * @(#)KbqaType.java, 2020/11/30.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Getter
@AllArgsConstructor
public enum KbqaType {
    /**
     * 尺码图
     */
    SIZE_PIC("尺码"),
    /**
     * 尺码图
     */
    GOODS_PROPERTY("属性");

    private String name;
}