package com.netease.yx.bot.core.model.constant;

public class StandardReply {
    /**
     * 默认标准回复
     */
    public static final String DEFAULT_REPLY = "小选暂时没有懂您的意思，还请您用 【关键词】描述一下您的问题呢";

    /**
     * 业务相关问题的默认标准回复
     */
    public static final String DEFAULT_WORK_RELATED_REPLY = "小选在听您说话呢，还请您用更准确的【关键词】描述一下您的问题呢";

    /**
     * 闲聊回复
     * 当生成的闲聊回复被易盾判为无效时，需要替换成默认闲聊回复
     */
    public static final String DEFAULT_CHITCHAT_REPLY = "您说，小选在呢~";
    public static final String DEFAULT_CHITCHAT_NOT_SAFE_REPLY = "抱歉，小选没有理解您的意思，请问有什么可以帮您的吗？";  //"不知如何回答才好，不如换个话题吧~";
    public static final String DEFAULT_CHITCHAT_NO_ANSWER_REPLY = "抱歉，您的问题有点复杂，请稍后再试";  //"不知如何回答才好，不如换个话题吧~";

    /**
     * 导购默认回复
     */
    public static final String DEFAULT_GUIDE_REPLY = "小选知道您要找商品，您可以先到严选App搜索框查询商品，以后小选帮您挑哈";

    /**
     * 算法流程线话术： 仅有多条流程线结果时
     */
    public static final String MULTI_BOT_REPLY = "请选择您需要的流程";
    public static final String BOT_ASK_MONEY_REPLY = "请选择您要查询的退款流程";

    /**
     * 算法流程线话术： 仅有多条faq结果时
     */
    public static final String MULTI_FAQ_REPLY = "请点击下方您想咨询的问题";

    /**
     * 算法流程线话术： 同时有多条faq及流程线时
     */
    public static final String MULTI_FAQ_BOT_REPLY = "小选猜您想问以下问题, 或者请选择底部流程";

    /**
     * 匹配唯一的faq回答时，需要提示一下底部流程可以选择
     */
    public static final String UNIQUE_FAQ_ADD_BOT_REPLY = "如果以上内容没能解决您的问题，请尝试底部流程。";
}
