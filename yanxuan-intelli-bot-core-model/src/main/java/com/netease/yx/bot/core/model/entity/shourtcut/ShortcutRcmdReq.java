/**
 * @(#)ShortcutRcmdReq.java, 2020/10/24.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.shourtcut;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class ShortcutRcmdReq {
    /**
     * 会话id
     */
    private long sessionId;
    private long yxUserId;


    // 推荐的数量，默认是5个，最多10个
    private int rcmdNum = 5;

    private List<List<Long>> historyKnowledgeIds;
}