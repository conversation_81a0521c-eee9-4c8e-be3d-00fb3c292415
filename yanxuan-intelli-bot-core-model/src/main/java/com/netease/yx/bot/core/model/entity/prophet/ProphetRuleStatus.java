/**
 * @(#)ProphetRuleStatus.java, 2020/12/1.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

import com.netease.yx.bot.core.model.entity.EnumIntegerInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@AllArgsConstructor
@Getter
public enum ProphetRuleStatus implements EnumIntegerInterface<ProphetRuleStatus> {

    NULL(0, "NULL"),
    EFFECTIVE(1, "启用"),
    INEFFECTIVE(2, "停用"),
    DELETED(3, "已删除");

    private final int value;

    private final String desc;

    @Override
    public ProphetRuleStatus genEnumByIntValue(int intValue) {
        for (ProphetRuleStatus prophetRuleStatus : ProphetRuleStatus.values()) {
            if (prophetRuleStatus.getValue() == intValue) {
                return prophetRuleStatus;
            }
        }
        return ProphetRuleStatus.NULL;
    }
}