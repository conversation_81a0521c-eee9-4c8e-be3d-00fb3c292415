/**
 * @(#)PolarAnalysisResponse.java, 2021/12/15.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.qualityInspection;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class PolarAnalysisResponse {
    private String uniqueId;
    private String type;
    private String content;
    private double goodScore;
    private double badScore;
    private Map<String, String> others;

}