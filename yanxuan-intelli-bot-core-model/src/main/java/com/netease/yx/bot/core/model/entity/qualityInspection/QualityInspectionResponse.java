/**
 * @(#)QualityInspectionResponse.java, 2021/12/10.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.qualityInspection;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
/*
*    private boolean isContainTerm; //包含专有名词
1001
   private boolean isContainTypo; // 包含错别字
1002
    private boolean isContainSpecialChar; //包含特殊字符，如“？？？”
1003
    private boolean isNegativeQa;// 消极问答（一问一答）
1004
    private boolean isImpatience;//包含客服不耐烦情绪
1005
    private boolean isNegativeEmotion;// 包含用户负面情绪
1006
    private boolean notContainAppeasing; // 客服没有对用户进行安抚
1007
     private boolean isComplaint;//用户投诉
1008
    private boolean notRelatedRecommend; //无货场景下没有关联推荐
1009

* */
@Data
public class QualityInspectionResponse {
    private List<Integer> problemIds = new ArrayList<>();
}

