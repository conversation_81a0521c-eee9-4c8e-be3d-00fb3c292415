/**
 * @(#)CardType.java, 2020/9/5.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.constant;

/**
 * <AUTHOR> @ corp.netease.com)
 */
public enum CardType {
    /**
     * 催发货
     */
    URGE_EXPRESS(1),
    /**
     * 申请退货
     */
    APPLY_RETURN_GOODS(2),
    /**
     * 申请换货
     */
    APPLY_CHANGE_GOODS(3),
    /**
     * 申请维修
     */
    APPLY_FIX_ITEM(4),
    /**
     * 修改订单
     */
    APPPY_CHANGE_ORDER(5),
    /**
     * 发票服务
     */
    APPLY_RECEIPT(6),
    /**
     * 价格保护
     */
    APPLY_PRICE_PROTECT(7),
    /**
     * 意见反馈
     */
    FEEDBACK(8);

    private long id;

    CardType(long id) {
        this.id = id;
    }

    public long getId() {
        return id;
    }
}