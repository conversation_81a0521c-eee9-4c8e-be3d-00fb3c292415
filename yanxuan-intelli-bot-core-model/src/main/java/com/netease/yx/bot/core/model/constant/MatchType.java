/**
 * @(#)FaqRsltType.java, 2020/5/7.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.constant;

/**
 * <AUTHOR> @ corp.netease.com)
 */
public enum MatchType {
    /**
     * 没有匹配到结果，或者没有答案
     */
    NO_ANSWER,
    /**
     * 精确匹配到了结果
     */
    PRECISE_MATCH,
    /**
     * 模糊匹配到了结果
     */
    FUZZY_MATCH
}