/**
 * @(#)RcmdResult.java, 2019/11/23.
 * <p/>
 * Copyright 2019 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.workbench;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 * 推荐结果
 */
@Data
@AllArgsConstructor
public class RcmdResult {
    /**
     * 推荐结果产生的类型
     */
    private RcmdType rcmdType;
    /**
     * 需要推荐的类目信息
     */
    private List<CateResult> cateResults;
    /**
     * 需要推荐的总结摘要
     */
    private SummaryResult summaryResult;
    /**
     * 会话的长度
     */
    private Integer sessionLength;
}