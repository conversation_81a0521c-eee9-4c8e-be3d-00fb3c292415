package com.netease.yx.bot.core.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeAnswerV2 {
    public static final String KEY_ID = "id";
    public static final String KEY_KNOWLEDGE_ID = "knwoledge_id";
    public static final String KEY_IS_DEFAULT = "is_default";
    public static final String KEY_ITEM_ID = "item_id";
    public static final String KEY_CATEGORY_ID = "category_id";
    public static final String KEY_ANSWER = "answer";
    public static final String KEY_ANSWER_TYPE = "answer_type";
    public static final String KEY_ANSWER_STATUS = "answer_status";
    public static final String KEY_ANSWER_USE = "answer_use";
    public static final String KEY_CREATE_TIME = "create_time";
    public static final String KEY_UPDATE_TIME = "update_time";

    private long id;
    private long knowledgeId;
    private int isDefault;
    private long itemId;
    private long categoryId;
    private String answer;
    private int answerType;
    private String answerUse;
    private int answerStatus;
    private String createTime;
    private String updateTime;
}
