package com.netease.yx.bot.core.model.train;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecommendKnowledge {
    /**
     * {
     * "knowledgeSource":"知识类型，1：FAQ；2：商品属性库"；3：虚拟知识,
     * "knowledgeId":"知识id，faq知识id，或商品属性id",
     * "answerId":"答案id，faq答案id，或商品属性值id",
     * "itemId":"主站商品id",
     * "itemPhyCategoryIdStr":"从一级到末级的商品中心物理类目id拼接，以,为分隔符"
     * }
     */
    private Integer knowledgeSource;
    private Long knowledgeId;
    private Long answerId;
    private Long itemId;
    private String itemPhyCategoryIdStr;
}
