package com.netease.yx.bot.core.model.entity.guide;


import lombok.Data;

@Data
public class QPResp {
    /**
     * 转写结果
     */
    private Object rewriteRslt;
    /**
     * 用于语义召回使用的query文本，主要是做了清洗操作
     */
    private String semanticRecallQuery;
    /**
     * 搜索意图，是搜商品，还是搜利益点什么的，目前只有搜商品
     */
    private Object intentType;
    /**
     * 默认的分词结果
     */
    private Object defaultWords;
    /**
     * 索引词结果序列
     */
    private Object indexSeqs;
    /**
     * 类目意图结果
     */
    private Object cateMap;
    /**
     * 标签，比如性别标签
     */
    private Object tags;
    /**
     * 其他额外的结果
     */
    private Object extra;
    /**
     * ner结果
     */
    private QPNerRslt nerRslt;
}