package com.netease.yx.bot.core.model.entity.channel;

import com.netease.yx.bot.core.model.constant.IntentTypeV2;
import com.netease.yx.bot.core.model.entity.CoreIntent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelQaResp implements Serializable {
    private List<ChannelQaRslt> faqList = new ArrayList<>();
    // 粗粒度的意图
    private IntentTypeV2 intentTypeV2;
    // 细粒度的语义意图
    private List<CoreIntent> coreIntents;

    private ChannelQaAddiReq channelQaAddiReq;
    // 	0-模糊，1-精确
    private int scoreType;

    public ChannelQaResp(List<ChannelQaRslt> faqList, IntentTypeV2 intentTypeV2, ChannelQaAddiReq channelQaAddiReq) {
        this.faqList = faqList;
        this.intentTypeV2 = intentTypeV2;
        this.channelQaAddiReq = channelQaAddiReq;

        if (CollectionUtils.size(faqList) == 1) {
            scoreType = 1;
        }
    }
}
