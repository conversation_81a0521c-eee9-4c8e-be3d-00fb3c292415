/**
 * @(#)ShortcutRe.java, 2020/9/15.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.mapper.knowledge;

import com.netease.yx.bot.core.model.entity.knowledge.Shortcut;

import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
public interface ShortcutMapper {
    /**
     * 查询所有结果
     *
     * @return
     */
    List<Shortcut> selectAll();
}