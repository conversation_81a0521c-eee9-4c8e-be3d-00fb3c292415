/**
 * @(#)MatchProcessData.java, 2020/6/1.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.faq;


import com.netease.yx.bot.core.model.entity.text.Term;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecallMatchProcessData implements Comparable<RecallMatchProcessData>, Serializable {
    private static final String DEFAULT_ID = "-1";

    /**
     * knowledge id
     */
    private String id;
    private String question;
    private double score;
    private List<Term> terms;
    private String itemId;
    private String cateId;
    // 将废弃
    private String corpusId;
    private String questionHst;
    private String answerId;

    public RecallMatchProcessData(String id, String question, String corpusId, String itemId, String answerId, double score) {
        this.id = id;
        this.question = question;
        this.score = score;
        this.answerId = answerId;
        this.itemId = itemId;
        this.corpusId = corpusId;
    }

    public RecallMatchProcessData(String id, String question, String corpusId, String itemId, String cateId, String answerId, double score) {
        this.id = id;
        this.question = question;
        this.score = score;
        this.answerId = answerId;
        this.itemId = itemId;
        this.corpusId = corpusId;
        this.cateId = cateId;
    }

    public RecallMatchProcessData(String id, String question, double score, List<Term> terms) {
        this.id = id;
        this.question = question;
        this.score = score;
        this.terms = terms;
    }

    public RecallMatchProcessData(String id, String question, double score, List<Term> terms, String itemId, String cateId, String corpusId) {
        this.id = id;
        this.question = question;
        this.score = score;
        this.terms = terms;
        this.itemId = itemId;
        this.cateId = cateId;
        this.corpusId = corpusId;
    }

    public RecallMatchProcessData(String id, String question, double score, String corpusId) {
        this.id = id;
        this.question = question;
        this.score = score;
        this.corpusId = corpusId;
    }


    public static RecallMatchProcessData buildFromTextBasicData(TextBasicData basicData) {
        List<Term> terms = basicData.buildTerms();
        return new RecallMatchProcessData(DEFAULT_ID, basicData.getCleanedText(), 0, terms);
    }

    /**
     * 降序，从大到小
     *
     * @param o
     * @return
     */
    @Override
    public int compareTo(RecallMatchProcessData o) {
        return -Double.compare(score, o.score);
    }

    public String buildId() {
        if (StringUtils.isNotEmpty(corpusId)) {
            return corpusId;
        }
        return StringUtils.join(id + question);
    }
}