package com.netease.yx.bot.core.model.mapper.algo;

import com.netease.yx.bot.core.model.entity.label.ItemLabelTask;
import com.netease.yx.bot.core.model.entity.label.ItemLabelTaskSelectReq;

import java.util.List;

public interface ItemLabelTaskMapper {

    List<ItemLabelTask> selectAll();

    List<ItemLabelTask> selectByTime(ItemLabelTaskSelectReq itemLabelTaskSelectReq);

    int insert(ItemLabelTask itemLabelTask);

    int updateStatus(ItemLabelTask itemLabelTask);
}
