package com.netease.yx.bot.core.model.entity.rgAssist;

import com.netease.yx.bot.core.model.constant.ReplyState;
import com.netease.yx.bot.core.model.entity.faq.FaqIdRslt;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
public class RGAsssistFaqResp {
    /**
     * 参数说明：
     * (a) state, 请求返回状态字段
     * (b) text, String, 展示文案、话术、文本
     * (c) faqRslt, FaqIdResult, faq结果
     */

    private String text;
    private FaqIdRslt faqRslt;
    private ReplyState state = ReplyState.SUCCESS;
    private boolean noAnswer = false;

    public static RGAsssistFaqResp buildTextAnswer(String text) {
        return RGAsssistFaqResp.builder().state(ReplyState.SUCCESS).text(text).build();
    }

    public static RGAsssistFaqResp buildFaqAnswer(FaqIdRslt faqIdRslt) {
        return RGAsssistFaqResp.builder().state(ReplyState.SUCCESS).faqRslt(faqIdRslt).build();
    }

    public static RGAsssistFaqResp buildNULLAnswer() {
        return RGAsssistFaqResp.builder().state(ReplyState.SUCCESS).noAnswer(true).build();
    }


}
