package com.netease.yx.bot.core.model.entity.chat;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatClassifyTFDataResp {
    @JsonAlias("output_0")
    private String chatCH;
    @JsonAlias("output_1")
    private Float score;
    @JsonAlias("output_2")
    private String confidence;
}
