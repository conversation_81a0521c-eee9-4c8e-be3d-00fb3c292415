/**
 * @(#)IntentType.java, 2020/5/6.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.constant;

/**
 * <AUTHOR> @ corp.netease.com)
 */
public enum Level1IntentType {
    /**
     * 商品知识问答
     */
    KBQA,
    /**
     * 导购
     */
    GUIDE,
    /**
     * 闲聊
     */
    CHITCHAT,
    /**
     * FAQ
     */
    FAQ,
    /**
     * 特殊意图
     */
    SPECIAL,
    ITEM_CARD,
    /**
     * 订单卡片
     */
    ORDER_CARD,
    UNKNOWN;


    public static Level1IntentType getFromName(String name) {
        for (Level1IntentType level1IntentType : Level1IntentType.values()) {
            if (level1IntentType.name().equals(name.toUpperCase())) {
                return level1IntentType;
            }
        }
        return UNKNOWN;
    }

    // 是正常的意图
    public static boolean checkCommonIntent(Level1IntentType level1IntentType) {
        return Level1IntentType.FAQ.equals(level1IntentType) || Level1IntentType.KBQA.equals(level1IntentType) || Level1IntentType.CHITCHAT.equals(level1IntentType) || Level1IntentType.GUIDE.equals(level1IntentType);
    }
}