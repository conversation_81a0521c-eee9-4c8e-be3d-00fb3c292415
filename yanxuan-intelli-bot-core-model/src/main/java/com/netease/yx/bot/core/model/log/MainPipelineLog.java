/**
 * @(#)PipelineLog.java, 2020/11/20.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.log;

import com.alibaba.fastjson.JSON;
import com.netease.yx.bot.core.model.entity.*;
import com.netease.yx.bot.core.model.entity.faq.FaqIdRslt;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MainPipelineLog implements Serializable {
    private static final String KEY_INPUT = "input";
    private static final String KEY_CONTEXT = "context";
    private static final String KEY_INTENT = "intent";
    private static final String KEY_MATCH = "match";
    private static final String KEY_KBQA = "kbqa";
    private static final String KEY_FAQ_COMBINE = "faqCombine";
    private static final String KEY_OTHER = "other";
    private static final String KEY_OUTPUT = "output";

    private static final String EMPTY_MAP_STR = "{}";

    /**
     * 输入
     * BotReq
     */
    private BotReq input;

    /**
     * 上下文
     * BotContext
     */
    private BotContext context;
    /**
     * 意图综合结果,包含一级和售后等意图
     * IntentResp
     */
    private IntentRslt intent;
    /**
     * 匹配结果
     * KnowledgeMatchResp
     */
    private KnowledgeMatchResp match;
    /**
     * 商品问答结果，如尺码、商品属性等
     * KbqaResp
     */
    private KbqaResp kbqa;
    /**
     * 意图和匹配综合得到的FAQ结果
     * FaqIdRslt
     */
    private FaqIdRslt faqCombine;
    /**
     * 其他需要被记录的数据
     * Map<String, Object>
     */
    private Map<String, Object> other = new HashMap<>();
    /**
     * 输出
     * BotResp
     */
    private BotResp output;


    /**
     * 上轮基本文本解析
     * BotReq
     */
    private List<TextBasicData> hstTextBasicData = new ArrayList<>();

    public static Map<String, String> buildMap(MainPipelineLog mainPipelineLog) {
        Map<String, String> map = new HashMap<>(10);
        map.put(KEY_INPUT, EMPTY_MAP_STR);
        map.put(KEY_CONTEXT, EMPTY_MAP_STR);
        map.put(KEY_INTENT, EMPTY_MAP_STR);
        map.put(KEY_MATCH, EMPTY_MAP_STR);
        map.put(KEY_KBQA, EMPTY_MAP_STR);
        map.put(KEY_FAQ_COMBINE, EMPTY_MAP_STR);
        map.put(KEY_OTHER, EMPTY_MAP_STR);
        map.put(KEY_OUTPUT, EMPTY_MAP_STR);
        if (mainPipelineLog != null) {
            if (mainPipelineLog.getInput() != null) {
                map.put(KEY_INPUT, JSON.toJSONString(mainPipelineLog.getInput()));
            }
            if (mainPipelineLog.getContext() != null) {
                map.put(KEY_CONTEXT, JSON.toJSONString(mainPipelineLog.getContext()));
            }
            if (mainPipelineLog.getIntent() != null) {
                map.put(KEY_INTENT, JSON.toJSONString(mainPipelineLog.getIntent()));
            }
            if (mainPipelineLog.getMatch() != null) {
                map.put(KEY_MATCH, JSON.toJSONString(mainPipelineLog.getMatch()));
            }
            if (mainPipelineLog.getKbqa() != null) {
                map.put(KEY_KBQA, JSON.toJSONString(mainPipelineLog.getKbqa()));
            }
            if (mainPipelineLog.getFaqCombine() != null) {
                map.put(KEY_FAQ_COMBINE, JSON.toJSONString(mainPipelineLog.getFaqCombine()));
            }
            if (mainPipelineLog.getOther() != null) {
                map.put(KEY_OTHER, JSON.toJSONString(mainPipelineLog.getOther()));
            }
            if (mainPipelineLog.getOutput() != null) {
                map.put(KEY_OUTPUT, JSON.toJSONString(mainPipelineLog.getOutput()));
            }
        }
        return map;
    }

    public void genHstTextBasicData() {
        if (this.getContext() != null && this.getContext().getInputs() != null && this.getContext().getInputs().size() > 0) {
            hstTextBasicData.add(0, this.getContext().getInputs().get(0));
//            this.hstTextBasicData = ;
        }
    }
}