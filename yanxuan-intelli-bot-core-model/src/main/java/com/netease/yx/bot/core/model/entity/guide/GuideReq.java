package com.netease.yx.bot.core.model.entity.guide;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GuideReq {
    private String sessionId;
    private String query;
    private String uid;
    private String itemId;

    public GuideReq(String query, String uid) {
        this.query = query;
        this.uid = uid;
    }

    public GuideReq(String sessionId, String query, String uid) {
        this.sessionId = sessionId;
        this.query = query;
        this.uid = uid;
    }

}
