package com.netease.yx.bot.core.model.entity.rgAssist;

import lombok.AllArgsConstructor;
import lombok.Data;


@AllArgsConstructor
@Data
public class ItemViewInfo implements Comparable<ItemViewInfo> {

    private long itemId;
    private String rcmdReason;
    private String action;
    private long views;

    public int compareTo(ItemViewInfo itemViewInfo) {
        return Long.compare(itemViewInfo.getViews(), this.views);
    }
}
