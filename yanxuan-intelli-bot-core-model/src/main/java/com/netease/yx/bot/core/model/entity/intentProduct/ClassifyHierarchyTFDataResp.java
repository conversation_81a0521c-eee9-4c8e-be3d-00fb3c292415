package com.netease.yx.bot.core.model.entity.intentProduct;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassifyHierarchyTFDataResp {
    @JsonAlias("output_0")
    private String intentCH;
    @JsonAlias("output_1")
    private String intent;
    @JsonAlias("output_2")
    private String confidence;
    @JsonAlias("output_3")
    private Float score;
}
