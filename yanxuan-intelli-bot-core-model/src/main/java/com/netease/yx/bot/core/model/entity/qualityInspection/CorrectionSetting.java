/**
 * @(#)CorrectionSetting.java, 2021/12/15.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.qualityInspection;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> @ corp.netease.com)
 */

//@Configuration
//@ConfigurationProperties("smartworkfunction.polaranalysis")
@Data
@Configuration
@ConfigurationProperties(prefix = "smartworkfunction.correction")
public class CorrectionSetting {

    //private String url= "http://smart-infer.hz.infra.mail:31938/v1/models/mac-bert-text-correction:predict";
    //private  String host = "mac-bert-text-correction.yx-serving.svc";
    private String url;
    private String host;

}