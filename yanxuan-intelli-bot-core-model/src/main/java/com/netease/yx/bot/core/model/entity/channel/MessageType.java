package com.netease.yx.bot.core.model.entity.channel;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MessageType {
    OTHER(0),
    TEXT(1),
    IMAGE(2),
    ORDER_CARD(3),
    GOOD_CARD(4);

    private final int code;

    public static boolean checkNeedProcess(int code){
        return MessageType.TEXT.code == code || MessageType.ORDER_CARD.code == code || MessageType.GOOD_CARD.code == code;
    }
}
