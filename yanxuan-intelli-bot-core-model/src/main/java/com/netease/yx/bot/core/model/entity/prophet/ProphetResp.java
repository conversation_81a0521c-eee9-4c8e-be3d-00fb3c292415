/**
 * @(#)ProphetResp.java, 2020/9/3.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

import com.netease.yx.bot.core.model.constant.CardType;
import com.netease.yx.bot.core.model.constant.ProphetType;
import com.netease.yx.bot.core.model.constant.SourceType;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@Builder
public class ProphetResp {
    /**
     * 展示FAQ列表，有序
     */
    private List<Long> knowledgeIds;
    /**
     * 展示卡片列表，有序
     */
    private List<Long> cardIds;
    /**
     * 快捷短语id列表，有序
     */
    private List<Long> shortcutIds;
    /**
     * source
     */
    private SourceType sourceType;
    /**
     * 输出结果的方式，DEFAULT / RULE / MODEL
     */
    private ProphetType prophetType;
    /**
     * 推荐商品的id
     */
    private Long rcmdItemId;
    /**
     * 推荐语
     */
    private String rcmdMsg;

    public static ProphetResp mockTestResp(ProphetReq req) {
        List<Long> knowledgeIds = new ArrayList<Long>() {{
            add(20142679L);
            add(20143134L);
            add(20143115L);
            add(20143112L);
            add(20143132L);
            add(20143103L);
            add(20142826L);
        }};

        List<Long> shortcutIds = new ArrayList<Long>() {{
            add(62L);
            add(63L);
            add(64L);
            add(38L);
            add(140L);
            add(183L);
            add(206L);
        }};

        List<Long> cardIds = new ArrayList<Long>() {{
            add(CardType.URGE_EXPRESS.getId());
            add(CardType.APPLY_RETURN_GOODS.getId());
            add(CardType.APPLY_CHANGE_GOODS.getId());
            add(CardType.APPLY_FIX_ITEM.getId());
            add(CardType.APPPY_CHANGE_ORDER.getId());
            add(CardType.APPLY_RECEIPT.getId());
            add(CardType.APPLY_PRICE_PROTECT.getId());
            add(CardType.FEEDBACK.getId());
        }};

        return ProphetResp.builder().knowledgeIds(knowledgeIds).shortcutIds(shortcutIds).cardIds(cardIds).prophetType(ProphetType.DEFAULT).sourceType(SourceType.USER_CENTER).build();
    }
}