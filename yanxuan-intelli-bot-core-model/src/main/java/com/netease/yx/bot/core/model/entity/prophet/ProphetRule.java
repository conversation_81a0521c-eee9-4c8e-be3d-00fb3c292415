/**
 * @(#)ProphetRule.java, 2020/12/1.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

import com.netease.yx.bot.core.model.constant.PlatformType;
import com.netease.yx.bot.core.model.constant.SourceType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProphetRule implements Comparable<ProphetRule> {
    /**
     * 规则编号
     */
    private String id;
    /**
     * 规则名称
     */
    private String name;
    /**
     * 状态：EFFECTIVE(1, "启用"),INEFFECTIVE(2, "停用"),DELETED(3, "已删除");
     */
    private ProphetRuleStatus status;
    /**
     * 顺序
     */
    private int order;
    /**
     * 修改人
     */
    private String kfUid;
    /**
     * 创建时间
     */
    private long createTime;
    /**
     * 修改时间
     */
    private long modifyTime;
    /**
     * 疲劳度：EVERYTIME(1, "每次进入都推送"),ONCE_A_DAY(2, "一天一次"),ONCE_PER_SESSION(3, "一个会话一次");
     */
    private FatigueType fatigueType;
    /**
     * 作用端及source来源。
     * <p>
     * Platform：APP(1, "APP"),WECHAT(2, "微信"),WEB(3, "WEB");
     * <p>
     * Source：ALL(1, "ALL"),ORDER_DETAIL(2, "订单入口"),AFTER_SALE_PROCESS(3, "售后进度页入口"),
     * AFTER_SALE_SERVICE(4, "售后入口"),USER_CENTER(5, "帮助与客服"),ITEM_DETAIL(6, "商品详情页入口"),
     * CUSTOMER_REVIEWS(7, "商品评论页"),ESSAGE_CENTER(8, "消息中心"), BALANCE(9, "入口余额页"),    // 新增入口余额页
     * PRO_MEMBERSHIP(10, "pro会员权益页");    // 新增pro会员权益页;
     */
    private Map<PlatformType, List<SourceType>> platformSourceMap;
    /**
     * 规则内容
     */
    private Map<String, List<Integer>> content;

    private List<UserTag> userTagsList; //用户标签
    // UserTag: V1(1, "V1"), V2(2, "V2"), V3(3, "V3"), V4(4, "V4"), V5(5, "V5"), NEW_CUSTOMER(6, "新用户");

    /**
     * 推荐问题
     */
    private List<Long> faqIdList;
    /**
     * 自助卡片组件：1-催发货,2-申请退货,3-申请换货,4-申请维修,5-修改订单,6-发票服务,7-价格保护,8-意见反馈
     */
    private List<Long> cardIdList;

    @Override
    public int compareTo(ProphetRule o) {
        return Integer.compare(this.order, o.order);
    }
}