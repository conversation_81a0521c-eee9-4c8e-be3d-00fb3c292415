package com.netease.yx.bot.core.model.entity.guide;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SearchResponse {

    private static final long serialVersionUID = -7857179120704987556L;
    public List<String> itemIdList; // required
    public List<String> semanticList; // required
    public long count; // required
    public byte hitStage; // required
    public Map<String, Long> categoryCount; // required
    public List<String> segTermList; // required
    public List<String> suggestTermList; // required
    public String code; // required
    public String originQuery; // required
    public List<String> actualQueryList; // required
    public Map<String, Long> labelsForSearchCount; // required
    public Map<String, Long> attributeCount; // required
    public Map<String, Long> mainCategoryCount; // required
    public Map<String, Long> makerCount; // required
    public Map<String, Long> shipAreaCount; // required
    public Map<String, Long> phyCategoryCount; // required
    public String recallCode; // required

}