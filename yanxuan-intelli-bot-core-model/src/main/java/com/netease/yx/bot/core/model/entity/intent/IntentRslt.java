/**
 * @(#)IntentRslt.java, 2020/5/11.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.intent;

import com.netease.yx.bot.core.model.constant.Level1IntentType;
import com.netease.yx.bot.core.model.constant.Level2IntentType;
import com.netease.yx.bot.core.model.entity.CoreIntent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IntentRslt implements Serializable {
    private Level1IntentType firstIntent;
    private Level2IntentType secondIntent;
    private List<CoreIntent> coreIntents;

    public IntentRslt(Level1IntentType firstIntent) {
        this.firstIntent = firstIntent;
        this.coreIntents = new ArrayList<>();
    }
}