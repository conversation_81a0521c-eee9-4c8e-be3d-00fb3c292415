/**
 * @(#)CoreIntent.java, 2020/10/22.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CoreIntent implements Serializable {
    private static final String KNOWLEDGE_TYPE_SOP = "SOP";
    private static final String KNOWLEDGE_TYPE_FAQ = "FAQ";
    private static final String CONFIDENCE_STRONG = "strong";
    private static final String CONFIDENCE_MEDIAN = "median";
    private static final String SOP_CHECK_INTENT = "express_urgeDelivery";

    private static final Integer INIT_IDX = 1;
    private static final double INIT_SCORE = 1f;
    private static final String REQUEST_OBJ_KEY = "instances";
    private static final String INTENT_OTHER = "other";

    private int idx;
    @JsonAlias("intent_ch")
    private String intentCh;
    private double prob;
    private String intent;
    private String confidence;
    @JsonAlias("knowledge_type")
    private String knowledgeType;
    private long knowledgeId;

    public static CoreIntent buildDefault() {
        return new CoreIntent(INIT_IDX, "其它", INIT_SCORE, INTENT_OTHER, CONFIDENCE_STRONG, "", -1);
    }

    public boolean isSop() {
        return StringUtils.equals(KNOWLEDGE_TYPE_SOP, knowledgeType);
    }

    public boolean isStrongConfidence() {
        return StringUtils.equals(CONFIDENCE_STRONG, confidence);
    }

    public void setMedian() {
        this.confidence = CONFIDENCE_MEDIAN;
    }

    public boolean needCheckSop() {
        return StringUtils.equals(SOP_CHECK_INTENT, intent);
    }
}
