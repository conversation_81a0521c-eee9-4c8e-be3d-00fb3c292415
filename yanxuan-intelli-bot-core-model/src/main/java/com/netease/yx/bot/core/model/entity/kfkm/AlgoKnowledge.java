package com.netease.yx.bot.core.model.entity.kfkm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlgoKnowledge {

    public static final String CORPUS_ID_PREFFIX_STANDARD = "STANDARD-";
    public static final String CORPUS_ID_PREFFIX_SIMILAR = "SIMILAR-";

    public static final String KNOWLEDGE_ID = "knowledgeId";
    public static final String KNOWLEDGE_TYPE = "knowledgeType";
    public static final String KNOWLEDGE_USE = "knowledgeUse";
    public static final String STATUS = "status";
    public static final String STD_QUESTION = "stdQuestion";
    public static final String CLEAN_STD_QUESTION = "cleanStdQuestion";
    public static final String CLEAN_STD_QUESTION_TERMS = "cleanStdQuestionTerms";
    public static final String EFFECTIVE_TIME = "effectiveTime";
    public static final String EFFECTIVE_TYPE = "effectiveType";
    public static final String EXPIRY_TIME = "expiryTime";
    public static final String ANSWER_NUM = "answerNum";

    public static final String ANSWERS = "answers";
    public static final String ANSWER_CHANNEL = "answers.channel";
    public static final String ANSWER_ITEM_INFO_LABEL = "answers.itemInfoLabel";
    public static final String ANSWER_ITEM_INFO_LABEL_KEYWORD = "answers.itemInfoLabel.keyword";
    public static final String ANSWER_PLATFORM = "answers.platform";
    public static final String ANSWER_ID = "answers.answerId";
    public static final String ANSWER_EXPIRY_TIME = "answers.expiryTime";
    public static final String ANSWER_EFFECTIVE_TIME = "answers.effectiveTime";
    public static final String ANSWER_ITEM_CATE_LABEL_STR_KEYWORD = "answers.itemCateLabelStr.keyword";
    public static final String ANSWER_TYPE = "answers.answerType";
    public static final String ANSWER_USE = "answers.answerUse";

    public static final String ANSWER_ITEM_CATE_LABEL = "answers.itemCateLabel";

    public static final String SIMILAR_QUESTIONS = "similarQuestions";
    public static final String SIMILAR_QUESTIONS_CLEAN_CONTENT = "similarQuestions.cleanContent";
    public static final String SIMILAR_QUESTIONS_CLEAN_CONTENT_TERMS = "similarQuestions.cleanContentTerms";

    public static final String UN_SIMILAR_QUESTIONS = "unSimilarQuestions";

    public static final String VALUE_EMPTY = "empty";
    public static final int STATUS_VALID = 4;

    public static final int USE_INTERNAL = 1;
    public static final int USE_OUT = 2;
    // 知识id
    private long knowledgeId;
    // 标准问，原文
    private String stdQuestion;
    // 清理规范化过的标准问，如大小写，繁体等
    private String cleanStdQuestion;
    // 使用自定义分词处理的结果
    private String cleanStdQuestionTerms;
    // 知识类型, 1. FAQ, 2. 内部知识
    private int knowledgeType;
    // 问题用途: 1.对内，2.对外
    private int knowledgeUse;
    // 客服业务分类，存id，逗号隔开，FAQ使用
    private List<Integer> busCate;
    // 内部知识库分类，存id，逗号隔开，内部知识使用
    private List<Integer> kmCate;
    // 生效类型
    private int effectiveType;
    // 生效时间
    private long effectiveTime;
    // 失效时间
    private long expiryTime;
    // 知识状态: 1.未审核，2.已审核，3.未生效，4.已生效，5.已删除
    private int status;
    // 答案的数量
    private int answerNum;
    private List<Float> stqQuestionVector;
    // 相似问对象
    private List<AlgoSimilarQuestion> similarQuestions;
    // 不相似问对象
    private List<AlgoSimilarQuestion> unSimilarQuestions = new ArrayList<>();
    // 关键词组
    private List<AlgoKeyword> keywordGroups;
    // 答案对象
    private List<AlgoAnswer> answers;
    // 很多是0, 毫秒，下同
    private long createTime;
    private long editTime;
    private long updateTime;
}
