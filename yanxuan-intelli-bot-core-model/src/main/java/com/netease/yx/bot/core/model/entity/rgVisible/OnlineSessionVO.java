package com.netease.yx.bot.core.model.entity.rgVisible;

import lombok.Data;

@Data
public class OnlineSessionVO {
    /**
     * 组id
     */
    private long groupId;
    /**
     * 组名
     */
    private String groupName;
    /**
     * 当前接待量
     */
    private int currentSession;
    /**
     * 最大接待量
     */
    private int maxSession;
    /**
     * 排队数
     */
    private int queueSize;
    /**
     * 在线（客服端）
     */
    private int onlineStatus;
    /**
     * 在线（管理端）
     */
    private int managerOnlineStatus;
    /**
     * 小休
     */
    private int restStatus;
    /**
     * 挂起
     */
    private int suspendStatus;
    /**
     * 离开
     */
    private int outStatus;
}
