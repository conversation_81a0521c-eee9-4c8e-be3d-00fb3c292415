package com.netease.yx.bot.core.model.entity.search;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchItemInfo {
    @JsonAlias("item_id")
    private long itemId;
    @JsonAlias("item_nick_name")
    private String itemName;
    @JsonAlias("out_item_id")
    private String outItemId;
    @JsonAlias("out_item_name")
    private String outItemName;

    @JsonAlias("out_sku_id")
    private String outSkuId;
    @JsonAlias("channel_tag")
    private String channelTag;

    @JsonAlias("org_channel_id")
    private String orgChannelId;
    @JsonAlias("channel_name")
    private String channelName;
    @JsonAlias("sku_id")
    private String skuId;
    @<PERSON>sonAlias("front_sku_spec_value")
    private String frontSkuSpecValue;

    private double score;

    public static SearchItemInfo transform(SearchItemInfoFromAppIndex searchItemInfoFromAppIndex) {
        SearchItemInfo searchItemInfo = new SearchItemInfo();
        searchItemInfo.setItemId(searchItemInfoFromAppIndex.getItemId());
        searchItemInfo.setItemName(searchItemInfoFromAppIndex.getItemNickNameSingle());
        searchItemInfo.setOutItemId(String.valueOf(searchItemInfoFromAppIndex.getItemId()));
        searchItemInfo.setOutItemName(searchItemInfoFromAppIndex.getItemNickNameSingle());
        return searchItemInfo;
    }
}
