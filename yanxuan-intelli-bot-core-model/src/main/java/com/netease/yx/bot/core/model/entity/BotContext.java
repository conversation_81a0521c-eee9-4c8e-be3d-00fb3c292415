/**
 * @(#)BotContext.java, 2020/9/5.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity;

import com.netease.yx.bot.core.model.constant.DeviceType;
import com.netease.yx.bot.core.model.constant.InvokeSource;
import com.netease.yx.bot.core.model.constant.PlatformType;
import com.netease.yx.bot.core.model.constant.SourceType;
import com.netease.yx.bot.core.model.entity.item.Item;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import com.netease.yx.bot.core.model.log.MainPipelineLog;
import com.netease.yx.bot.core.model.log.MultiChannelPipelineLog;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ corp.netease.com)
 * 在智能客服各子服务间传递的上下文数据
 * 部分数据需要从 redis中去取
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BotContext implements Serializable {
    public static final int SESSION_INTERACTION_HUMAN = 0;
    public static final int SESSION_INTERACTION_ROBOT = 1;
    public static final int SESSION_INTERACTION_CHECK = 2;

    private static final String COLON = ":";
    private static final String KEY_CONTEXT = "context";
    private static final String KEY_USER_ACESS = "acess";
    private static final String KEY_REQ_INTENT = "intent";
    private static final String KEY_REQ_RG = "rg";
    private static final String KEY_VISIBLE_RG = "visibleRG";
    private static final String KEY_ITEM_ID = "item";
    private static final String KEY_TURN_COUNT = "turn_count";
    private static final String KEY_USER_ORDER = "user_order";
    private static final String KEY_MSG = "messages";

    private static final String COMMA = ",";
    /**
     * 用户ID
     */
    private long userId;
    /**
     * 会话ID，老版本APP会话使用
     * 将逐渐废弃
     */
    private long sessionId;
    /**
     * 来源平台，APP/WECHAT/WEB
     * 老版本APP会话使用
     */
    private PlatformType platformType;
    /**
     * source标识
     * 老版本APP会话使用
     */
    private SourceType sourceType;
    /**
     * 调用来源，用于判断人工或机器人来源
     */
    private InvokeSource invokeSource;
    /**
     * 设备类型
     * 老版本APP会话使用
     */
    private DeviceType deviceType;
    /**
     * 售前标识
     * 老版本APP会话使用
     */
    private boolean preSale;
    /**
     * APP携带订单ID
     * 老版本APP会话使用
     */
    private long orderId;
    /**
     * 携带商品ID，主站ID，为数字
     */
    private long itemId;
    /**
     * 对应的Item对象,如果是商详页过来，会放在这里
     */
    private Item item;
    /**
     * 当前轮的文本数据
     */
    private List<TextBasicData> inputs;
    /**
     * 用户最可能关心的一笔订单详情，为复杂规则产生的订单，不一定是订单页过来的订单
     * 这里的订单指的是针对某个SPU的订单详情
     */
    private UserOrder userOrder;
    /**
     * 从订单页过来的话，因为无法确定单个商品，所以是需要取多个商品
     */
    private List<Long> orderItemIds;
    /**
     * 从订单页过来的话，因为无法确定单个商品，所以是需要取多个商品
     */
    private List<Item> orderItems;
    /**
     * 会话过程中累计多次发送的商品卡片
     * 如果是外渠，1对多的商品，也放在这。
     */
    private List<Long> cardItemIds;
    /**
     * 会话过程中累计多次发送的商品详情
     */
    private List<Item> cardItems;
//
//    /**
//     * 包含上一轮对话内容的文本输入
//     */
//    private TextBasicData inputsHst;
    /**
     * 历史处理链路的日志，一般就保留前两轮即可
     */
    private List<MainPipelineLog> pipelineLogs;

    private List<MultiChannelPipelineLog> multiChannelPipelineLogs;
    /**
     * 记录交互轮次
     */
    private int turnCount = 0;
    /**
     * 主动推荐次数
     */
    private int activeRcmdCount = 0;
    /**
     * 用户直接输入的历史语句记录
     */
    private List<String> hstTexts;
    /**
     * 24H访问人工次数
     */
    private int userRGCount24H;
    /**
     * 48H访问人工次数
     */
    private int userRGCount48H;
    /**
     * 用户R等级
     */
    private int userR;
    /**
     * 用户v等级
     */
    private int userV;
    /**
     * 工单状态
     */
    private int ticketStatus;
    /**
     * 是新用户，默认是false
     */
    private boolean newUser;
    /**
     * 渠道id
     */
    private long channel;

    // 渠道平台，tb，jd等，为训练需求引入
    private String platform;
    /**
     * 会话ID
     */
    private String multiChannelSessionId;
    /**
     * 平台订单ID
     */
    private String multiChannelOrderId;

    // 主站APP的商品id，因为可能是1对多的关系，所以存在这里
    private List<Long> itemIds;
    // 消息粒度的平台原始商品卡片抽取出来的id，可能是item，也可能是sku
    private String platformRawItemCardId;
    // 消息粒度的平台原始商品卡片抽取出来的id，转换成itemId
    private String platformItemId;
    // 商品的物理类目层次
    private String itemPhyCategoryIdStr;

    // 会话交互类型，0-人工，1-机器人, 2-知识重复检测
    private int sessionInteraction;

    // 咨询时间，这个在测试窗才会使用，为模拟未来的的一个时间，用于测试未来才生效的知识
    private long consultTime;
    // 是否是测试模式
    private boolean testMode;

    public static String buildContextKey(long sessionId) {
        return sessionId + COLON + KEY_CONTEXT;
    }

    public static String buildContextKey(String sessionId) {
        return sessionId + COLON + KEY_CONTEXT;
    }

    public static String buildUserRobotAccessKey(long userId) {
        return userId + COLON + KEY_USER_ACESS;
    }

    public static String buildRequestRGKey(long sessionId) {
        return sessionId + COLON + KEY_REQ_RG;
    }

    public static String buildRequestIntentKey(long sessionId) {
        return sessionId + COLON + KEY_REQ_INTENT;
    }

    public static String buildRequestVisibleRGKey(long sessionId) {
        return sessionId + COLON + KEY_VISIBLE_RG;
    }

    public static String buildUserOrderContextKey(long userId) {
        return userId + COLON + KEY_USER_ORDER;
    }

    public static String buildItemIdContextKey(long sessionId) {
        return sessionId + COLON + KEY_ITEM_ID;
    }

    public static String buildItemIdContextKey(String sessionId) {
        return sessionId + COLON + KEY_ITEM_ID;
    }

    public static String buildItemIdContextVal(String preItemIds, Long itemId) {
        return StringUtils.isEmpty(preItemIds) ? itemId.toString() : itemId + COMMA + preItemIds;
    }

    public static String buildTurnCountContextKey(long sessionId) {
        return sessionId + COMMA + KEY_TURN_COUNT;
    }

    public static String buildTurnCountContextVal(String turnCount) {
        return StringUtils.isEmpty(turnCount) ? "1" : turnCount;
    }

    public static long parseItemId(String itemIdVals) {
        if (itemIdVals == null) {
            return 0;
        }
        String nowItemId = Arrays.asList(itemIdVals.split(COMMA)).get(0);
        return Long.parseLong(nowItemId);
    }

    public static List<Long> parseItemIds(String itemIdVals) {
        if (itemIdVals == null) {
            return new ArrayList<>();
        }
        return Arrays.stream(itemIdVals.split(COMMA)).map(Long::parseLong).collect(Collectors.toList());
    }

    public void setCurInput(TextBasicData textBasicData) {
        List<TextBasicData> inputs = new ArrayList<>();
        inputs.add(textBasicData);

        this.inputs = inputs;

        // 会话历史
        if (CollectionUtils.isEmpty(hstTexts)) {
            this.hstTexts = new ArrayList<>();
        }
        this.hstTexts.add(0, textBasicData.getCleanedText());
    }
}