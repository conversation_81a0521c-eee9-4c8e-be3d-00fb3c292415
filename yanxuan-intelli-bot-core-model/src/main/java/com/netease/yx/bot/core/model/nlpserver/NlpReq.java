package com.netease.yx.bot.core.model.nlpserver;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NlpReq {
    // 输入
    private List<ReqInstance> instances;
    // 上游服务的编码，如yanxuan-intelli-bot
    private String product;
    // 本次服务需要的类型，如 NER_BASE
    private String service;
    // 部分可以自定义的参数
    private Map<String, Object> extra;

    public boolean needHst() {
        boolean flag = true;
        if (CollectionUtils.isNotEmpty(instances)) {
            for (ReqInstance reqInstance : instances) {
                if (StringUtils.isEmpty(reqInstance.getHst())) {
                    flag = false;
                    break;
                }
            }
        } else {
            flag = false;
        }
        return flag;
    }
}
