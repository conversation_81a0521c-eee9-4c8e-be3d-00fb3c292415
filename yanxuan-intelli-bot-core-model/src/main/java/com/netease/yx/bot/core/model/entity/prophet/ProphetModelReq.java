package com.netease.yx.bot.core.model.entity.prophet;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProphetModelReq {

    private String device;
    private String sourceType;
    private String platform;

    private String userR;
    private String userV;

    private String itemId;
    private String itemPhyCategory1Id;
    private String itemPhyCategory2Id;
    private String itemPhyCategory3Id;
    private String itemPhyCategory4Id;

    public void checkValid() {
        if (this.device == null) {
            this.device = "";
        }
        if (this.sourceType == null) {
            this.sourceType = "";
        }
        if (this.platform == null) {
            this.platform = "";
        }

        if (this.userR == null) {
            this.userR = "3";
        }
        if (this.userV == null) {
            this.userV = "1";
        }
    }

}
