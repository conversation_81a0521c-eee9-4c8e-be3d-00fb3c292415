/**
 * @(#)RespBean.java, 2019/11/20.
 * <p/>
 * Copyright 2019 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.workbench;

import com.netease.yx.bot.core.model.constant.ReplyState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SessionResp {
    /**
     * 返回状态
     */
    private ReplyState state;
    /**
     * 推荐结果产生的类型
     */
    private RcmdType rcmdType;
    /**
     * 类目结果列表，默认5个，按下标从0到4，分数依次降低
     */
    private List<CateResult> cateResults;
    /**
     * 需要推荐的总结摘要
     */
    private SummaryResult summaryResult;
    /**
     * 会话的长度
     */
    private Integer sessionLength = 0;
}
