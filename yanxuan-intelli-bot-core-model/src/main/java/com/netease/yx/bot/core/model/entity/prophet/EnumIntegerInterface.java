/**
 * @(#)EnumIntegerInterface.java, 2018/10/17.
 * <p/>
 * Copyright 2018 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

/**
 * 抽象枚举接口，由于数据库中对于枚举类型字段均存储数值，故该接口定义了2个统一的方法： 1.获取枚举对象的int值 2.通过int值获取枚举对象 定义枚举时，统一实现此接口
 *
 * @param <T> 枚举类型
 * <AUTHOR>
 */
public interface EnumIntegerInterface<T> {

    /**
     * 获得当前枚举类的int值
     *
     * @return
     */
    int getValue();

    /**
     * 根据intValue生成对应的Enum对象
     *
     * @param intValue
     * @return
     */
    T genEnumByIntValue(int intValue);
}
