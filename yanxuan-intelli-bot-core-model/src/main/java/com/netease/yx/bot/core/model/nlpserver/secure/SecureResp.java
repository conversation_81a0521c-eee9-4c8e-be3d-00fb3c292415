package com.netease.yx.bot.core.model.nlpserver.secure;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SecureResp {
    /**
     * private static final int PASS = 0;
     * private static final int UNCERTAIN = 1;
     * private static final int REJECT = 2;
     * private static final int ERROR = -1;
     */
    private List<Integer> statusList;
}
