/**
 * @(#)RcmdItem.java, 2020/12/20.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.rcmd;

import com.netease.yx.bot.core.model.entity.item.Item;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RcmdItem {
    private static final String RCMD_TEMPLATE = "为您诚挚推荐 ";
    /**
     * 推荐商品的id
     */
    private Long rcmdItemId;
    /**
     * 推荐语
     */
    private String rcmdMsg;


    public static RcmdItem buildRcmdItem(Item item, String rcmdReason) {
        if (item == null) {
            return null;
        }
        return new RcmdItem(item.getItemId(), RCMD_TEMPLATE + "<" + item.getNickName() + "> :\n" + rcmdReason);
    }

    public static RcmdItem buildSimpleRcmdItem(Item item) {
        if (item == null) {
            return null;
        }
        return new RcmdItem(item.getItemId(), RCMD_TEMPLATE + "<" + item.getNickName() + ">");
    }
}