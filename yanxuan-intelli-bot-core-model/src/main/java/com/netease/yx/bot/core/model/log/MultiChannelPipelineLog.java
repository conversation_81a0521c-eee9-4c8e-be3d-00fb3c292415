/**
 * @(#)PipelineLog.java, 2020/11/20.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.log;

import com.alibaba.fastjson.JSON;
import com.netease.yx.bot.core.model.entity.BotContext;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaAddiReq;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaReq;
import com.netease.yx.bot.core.model.entity.channel.ChannelQaResp;
import com.netease.yx.bot.core.model.entity.text.TextBasicData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.SerializationUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MultiChannelPipelineLog implements Serializable {
    private static final String KEY_INPUT = "input";
    private static final String KEY_ADDI_INPUT = "addi_input";
    private static final String KEY_CONTEXT = "context";
    private static final String KEY_OTHER = "other";
    private static final String KEY_OUTPUT = "output";

    private static final String EMPTY_MAP_STR = "{}";

    /**
     * 输入
     * BotReq
     */
    private ChannelQaReq input;
    /**
     * 基于input 加工出来的额外信息，如商品卡片，订单卡片等
     */
    private ChannelQaAddiReq channelQaAddiReq;

    /**
     * 上下文
     * BotContext
     */
    private BotContext context;
    /**
     * 其他需要被记录的数据
     * Map<String, Object>
     */
    private Map<String, Object> other;

    /**
     * 输出
     * BotResp
     */
    private ChannelQaResp output;


    /**
     * 上轮基本文本解析
     * BotReq
     */
    private List<TextBasicData> hstTextBasicData = new ArrayList<>();

    public static Map<String, String> buildMap(MultiChannelPipelineLog mainPipelineLog) {
        Map<String, String> map = new HashMap<>(10);
        map.put(KEY_INPUT, EMPTY_MAP_STR);
        map.put(KEY_CONTEXT, EMPTY_MAP_STR);
        map.put(KEY_OTHER, EMPTY_MAP_STR);
        map.put(KEY_OUTPUT, EMPTY_MAP_STR);
        if (mainPipelineLog != null) {
            if (mainPipelineLog.getInput() != null) {
                ChannelQaReq channelQaReq = SerializationUtils.clone(mainPipelineLog.getInput());
                map.put(KEY_INPUT, JSON.toJSONString(channelQaReq));
            }
            if (mainPipelineLog.getChannelQaAddiReq() != null) {
                map.put(KEY_ADDI_INPUT, JSON.toJSONString(mainPipelineLog.channelQaAddiReq));
            }
            if (mainPipelineLog.getContext() != null) {
                map.put(KEY_CONTEXT, JSON.toJSONString(mainPipelineLog.getContext()));
            }
            if (mainPipelineLog.getOther() != null) {
                map.put(KEY_OTHER, JSON.toJSONString(mainPipelineLog.getOther()));
            }
            if (mainPipelineLog.getOutput() != null) {
                map.put(KEY_OUTPUT, JSON.toJSONString(mainPipelineLog.getOutput()));
            }
        }
        return map;
    }
}