package com.netease.yx.bot.core.model.entity.text;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 句子经过预处理后得到的信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TextBasicData implements Serializable {
    /**
     * 原始文本
     */
    private String rawText;
    /**
     * 经过文本格式化、文本规范化后的文本
     */
    private String cleanedText;
    /**
     * 分词
     */
    private List<String> words = new ArrayList<String>();
    /**
     * 词性标注
     */
    private List<String> tags = new ArrayList<String>();

    public List<Term> buildTerms() {
        List<Term> terms = new ArrayList<>();
        if (words != null && tags != null && words.size() == tags.size()) {
            int start = 0;
            for (int i = 0; i < words.size(); i++) {
                Term term = new Term(words.get(i), tags.get(i), start);
                start += words.get(i).length();
                terms.add(term);
            }
        }
        return terms;
    }
}
