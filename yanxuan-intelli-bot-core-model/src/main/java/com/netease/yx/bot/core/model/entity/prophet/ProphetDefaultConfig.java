/**
 * @(#)ProphetDefaultConfig.java, 2020/9/23.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@Builder
public class ProphetDefaultConfig {
    /**
     * 展示FAQ列表，有序
     */
    private List<Long> knowledgeIds;
    /**
     * 展示卡片列表，有序
     */
    private List<Long> cardIds;
    /**
     * 快捷短语id列表，有序
     */
    private List<Long> shortcutIds;

    public ProphetDefaultConfig() {
        this.knowledgeIds = new ArrayList<>();
        this.cardIds = new ArrayList<>();
        this.shortcutIds = new ArrayList<>();
    }
}