package com.netease.yx.bot.core.model.entity.channel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChannelQaRslt implements Comparable<ChannelQaRslt>, Serializable {
    private static final String ID_FORMAT = "%d %d %d %d %d";
    private static final int KNOWLEDGE_TYPE_FAQ = 0;
    private static final int KNOWLEDGE_TYPE_GOODS_ATTRIBUTE = 1;
    private static final int KNOWLEDGE_TYPE_GPT = 2;

    public static final int KNOWLEDGE_SOURCE_FAQ = 1;
    public static final int KNOWLEDGE_SOURCE_GOODS_ATTRIBUTE = 2;
    public static final int KNOWLEDGE_SOURCE_UNKNOWN = 3;

    // 知识类型，0 为知识库，1为属性库
    private int knowledgeType = 0;

    // 知识类型，1 为知识库，2为属性库,3 为虚拟id
    private int knowledgeSource = 1;
    // 属性库的source
    private int attrSource;
    private long knowledgeId;

    private long answerId;
    // 主站id， mock会使用
    private long itemId;
    // 末级类目id, 内部排查使用，暂时没有用
    private long cateId;
    // 多层id用逗号,连接
    private String itemPhyCategoryIdStr;
    private double score;
    //
    private String showQuestion;
    private String showAnswer;
    // mock会使用
    private String platformItemId;
    // mock会使用
    private String platformRawItemCardId;

    // 内部排查问题使用
    private String question;
    // 内部排查问题使用
    private String corpusId;

    // 细粒度意图id，无监督产生， 区分“东西坏了” 和“我要退货”
    private long intentId;

    public ChannelQaRslt(long knowledgeId, String question, String corpusId, long answerId, long itemId, long cateId, double score) {
        this.knowledgeType = KNOWLEDGE_TYPE_FAQ;
        this.knowledgeId = knowledgeId;
        this.question = question;
        this.corpusId = corpusId;
        this.answerId = answerId;
        this.itemId = itemId;
        this.cateId = cateId;
        this.score = score;
    }

    public ChannelQaRslt(long itemId, long attributeId, String attributeName, long attrValueId, String attributeValue, int attrSource, double score) {
        this.knowledgeType = KNOWLEDGE_TYPE_GOODS_ATTRIBUTE;
        this.knowledgeId = attributeId;
        this.answerId = attrValueId;
        this.question = attributeName;
        this.itemId = itemId;
        this.score = score;
        this.showQuestion = attributeName;
        this.showAnswer = attributeValue;
        this.attrSource = attrSource;
        this.knowledgeSource = KNOWLEDGE_SOURCE_GOODS_ATTRIBUTE;
    }

    public ChannelQaRslt(String attributeName, String attributeValue) {
        this.knowledgeType = KNOWLEDGE_TYPE_GOODS_ATTRIBUTE;
        this.knowledgeId = 0;
        this.question = attributeName;
        this.itemId = 0;
        this.score = 0;
        this.showQuestion = attributeName;
        this.showAnswer = attributeValue;
        this.knowledgeSource = KNOWLEDGE_SOURCE_FAQ;
    }

    public static void main(String[] args) {
        String ID_FORMAT = "%d %d %d %d";
        System.out.println(String.format(ID_FORMAT, 1, 2, 3, 4));
    }

    public String buildId() {
        return String.format(ID_FORMAT, knowledgeId, answerId, itemId, cateId, intentId);
    }

    @Override
    public int compareTo(ChannelQaRslt o) {
        int scoreCompare = -Double.compare(score, o.score);
        if (scoreCompare != 0) {
            return scoreCompare;
        }
        int knowledgeTypeCompare = -Integer.compare(knowledgeType, o.knowledgeType);
        if (knowledgeTypeCompare != 0) {
            return knowledgeTypeCompare;
        }
        return -Integer.compare(attrSource, o.attrSource);
    }
}
