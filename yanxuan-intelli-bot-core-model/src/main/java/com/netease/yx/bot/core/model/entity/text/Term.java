/**
 * @(#)Term.java, 2020/6/2.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.text;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Term implements Serializable {
    private String word;
    private String nature;
    private int offset;
}