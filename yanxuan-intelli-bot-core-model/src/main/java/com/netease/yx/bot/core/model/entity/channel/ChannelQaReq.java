package com.netease.yx.bot.core.model.entity.channel;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelQaReq implements Serializable {
    @NonNull
    private String platform;
    @NonNull
    private long channelId;
    @JsonAlias("platformSessionId")
    @NonNull
    private String sessionId;
    private String serviceId;
    private String userId;
    @JsonAlias("platformMessageId")
    private String messageId;
    // 正常时间
    private long timestamp;
    @NonNull
    private String rawMsg;

    private int topK = 5;

    private boolean testMode;

    /**
     * customer = 1, // 用户
     * staff = 2, // 客服
     * system = 3, // 系统
     * robot = 4 // 机器人
     */
    private int messageSource;
    /**
     * other = 0, // 其他
     * text = 1, // 文本
     * image = 2, // 图片
     * orderCard = 3, // 订单卡片
     * goodCard = 4, // 商品卡片
     * afterSaleCard = 5, // 售后卡片类型
     * <p>
     * system = 6, // 系统消息
     * emoji = 7, // 表情
     * file = 8, // 文件
     * link = 9, // 链接
     * audio = 10, // 音频
     * video = 11, // 视频
     */
    private int messageType;

    // 结构化的消息，后面都会转成这种格式
    private MessageContent messageContent;

    // 会话交互类型，0-人工，1-机器人
    private int sessionInteraction;

    // 咨询时间，这个在测试窗才会使用，为模拟未来的的一个时间，用于测试未来才生效的知识
    private long consultTime;
    private boolean batch;
    // 控制返回几个，默认3个
    private int limit = 3;
}
