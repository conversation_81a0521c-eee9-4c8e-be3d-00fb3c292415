package com.netease.yx.bot.core.model.entity.rgVisible;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class SmartRGVisibleUserInfo {
    /**
     * 用户直接输入语句
     */
    private List<String> directInput;

    private List<Long> clickFaqs;
    private Integer rgCount24H;
    private Integer rgCount48H;
    private Integer robotCount24H;
    private Integer robotCount48H;
    private Integer interactCount;

    private Integer rgReqCount;
    private Map<String, Long> sessReqIntents;
    private Integer userCredit;
    private Integer userProLevel;
    private int ticketStatus;

    private boolean isPro;

}
