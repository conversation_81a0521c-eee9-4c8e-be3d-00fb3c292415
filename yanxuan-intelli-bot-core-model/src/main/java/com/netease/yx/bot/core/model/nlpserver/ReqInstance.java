package com.netease.yx.bot.core.model.nlpserver;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReqInstance {
    // 为方便追踪和区分，可以传入一个id
    private String id;
    // 主要输入，不能为空
    private String sen;
    // 部分任务如匹配，可能有第二句话输入，大部分情况下为空
    private String hst = StringUtils.EMPTY;
    // 部分任务如 1 VS N 批量匹配，可以输入多句候选
    private List<String> candidates;
    // 部分可以自定义的参数
    private Map<String, Object> extra;

    public ReqInstance(String sen) {
        this.sen = sen;
    }
}
