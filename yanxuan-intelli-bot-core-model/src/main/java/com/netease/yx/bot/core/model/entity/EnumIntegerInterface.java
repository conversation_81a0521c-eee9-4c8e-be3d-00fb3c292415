/**
 * @(#)EnumIntegerInterface.java, 2020/12/1.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity;

/**
 * <AUTHOR> @ corp.netease.com)
 */
public interface EnumIntegerInterface<T> {
    /**
     * 通过value获取对象
     *
     * @param value
     * @return
     */
    T genEnumByIntValue(int value);
}