/**
 * @(#)Knowledge.java, 2020/6/1.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.knowledge;

import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class Knowledge {
    private long id;
    private String stdQuestion;
    private int knowledgeType;
    private String busCate;
    private String kmCate;
    private String itemInfoLabel;
    private String itemCateLabel;
    private String otherLabel;
    private int status;
    /**
     * 二级类目
     */
    private long cate2;

    public void parseCate2() {
        if (busCate != null) {
            try {
                List<Long> ids = Arrays.stream(busCate.split(",")).map(Long::parseLong).collect(Collectors.toList());
                if (ids.size() >= 2) {
                    cate2 = ids.get(1);
                }
            } catch (Exception e) {

            }
        }
    }
}