package com.netease.yx.bot.core.model.entity;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeMatchRslt implements Comparable<KnowledgeMatchRslt>, Serializable {
    @JsonAlias("id")
    private long knowledgeId;
    private String question;
    private double score;

    private String corpusId;
    private long itemId;
    private long cateId;
    private long answerId;
    // 答案用途: 1.对内，2.对外，3-人工和机器人
    private int answerUse;

    public KnowledgeMatchRslt(long knowledgeId, String question, double score) {
        this.knowledgeId = knowledgeId;
        this.question = question;
        this.score = score;
    }

    public KnowledgeMatchRslt(long knowledgeId, String question, double score, String corpusId, long itemId, long answerId) {
        this.knowledgeId = knowledgeId;
        this.question = question;
        this.score = score;
        this.corpusId = corpusId;
        this.itemId = itemId;
        this.answerId = answerId;
    }

    public KnowledgeMatchRslt(long knowledgeId, String question, double score, String corpusId, long itemId, long answerId, int answerUse) {
        this.knowledgeId = knowledgeId;
        this.question = question;
        this.score = score;
        this.corpusId = corpusId;
        this.itemId = itemId;
        this.answerId = answerId;
        this.answerUse = answerUse;
    }

    @Override
    public int compareTo(KnowledgeMatchRslt o) {
        // score越大的排在前面
        int result = -Double.compare(score, o.score);
        if (result == 0){
            // answerUse 为3 的排在后面
            return Integer.compare(answerUse, o.answerUse);
        }else {
            return result;
        }
    }
}
