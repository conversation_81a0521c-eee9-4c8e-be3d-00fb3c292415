package com.netease.yx.bot.core.model.entity.checkSimilar;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CheckSimilarType {
    // 1：非正常长度；2：非正常文本；3：相似问和标问不相关；4：相似问与知识标签不相关；5：相似问与答案不相关.可选，若不传递则校验所有,
    UNKNOWN(-1),
    UNVALID_LENGTH(1),
    UNVALID_TEXT(2),
    UNVALID_WITH_STD_QUESTION(3),
    UNVALID_WITH_KNOWLEDGE_LABEL(4),
    UNVALID_WITH_ANSWER(5);

    private final int code;

    public static CheckSimilarType getByCode(int code) {
        for (CheckSimilarType checkSimilarType : CheckSimilarType.values()) {
            if (checkSimilarType.code == code) {
                return checkSimilarType;
            }
        }
        return UNKNOWN;
    }
}
