package com.netease.yx.bot.core.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@AllArgsConstructor
@Data
public class KnowledgeMatchResp implements Serializable {
    public static final int TYPE_NO_ANSWER = 0;
    public static final int TYPE_FAQ_PRECISE = 1;
    public static final int TYPE_FAQ_FUZZY = 2;

    private int matchType;
    private List<KnowledgeMatchRslt> matchRslts;

    public KnowledgeMatchResp() {
        this.matchType = TYPE_NO_ANSWER;
        this.matchRslts = new ArrayList<>();
    }
}
