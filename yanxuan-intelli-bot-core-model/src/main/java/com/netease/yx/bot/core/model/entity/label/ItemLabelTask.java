package com.netease.yx.bot.core.model.entity.label;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ItemLabelTask {
    private long id;
    private long itemId;
    private long insertTime;
    private Object itemPreviousLabelTask;
    // 存到Mysql需要变成String
    private String itemPreviousLabelTaskStr;
    // 0-默认，还未处理，1-正常已处理
    private int status;
}
