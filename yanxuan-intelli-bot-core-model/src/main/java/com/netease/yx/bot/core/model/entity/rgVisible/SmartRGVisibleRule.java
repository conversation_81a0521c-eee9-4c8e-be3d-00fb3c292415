package com.netease.yx.bot.core.model.entity.rgVisible;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class SmartRGVisibleRule {

    private List<Float> credit;
    private List<Integer> repeatIntent;
    private Float repeatIntentScore;
    private List<Integer> rgRequest;
    private Float rgRequestScore;
    private List<Integer> ticket;
    private Float ticketScore;
    private List<Integer> interactCount;
    private Float interactCountScore;
    private List<Integer> buyPower;
    private Float buyPowerScore;

}
