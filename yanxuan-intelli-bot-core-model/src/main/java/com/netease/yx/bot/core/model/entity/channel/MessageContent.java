package com.netease.yx.bot.core.model.entity.channel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageContent implements Serializable {
    private String text;
    private String url;
    private List<ItemCard> itemList;
    private List<OrderCard> orderList;

    public MessageContent(String text) {
        this.text = text;
    }
}
