package com.netease.yx.bot.core.model.entity.guide;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GuideRslt implements Serializable {
    /**
     * 导购结果
     * (a) itemId, Long， 推荐的商品id
     * (b) itemNickName, String, 推荐的商品中文名称
     * (c) rcmdReason, List<String></String>, 商品推荐语 (可选)
     */

    private static final String ITEM_NICK_NAME = "None";
    private static final String RCMD_REASON = "";
    private static final List<String> RCMD_REASON_LIST = new ArrayList<>();
    private static final String BOT_DEFAULT_TEXT = "小选为您诚心推荐~";

    private Long itemId;
    private String itemNickName = ITEM_NICK_NAME;
    private List<String> rcmdReason = RCMD_REASON_LIST;
    private List<String> rcmdTitle;

    private String note;
    private String title;
    private String url;
    private String picture;
    private String desc;

    public GuideRslt(Long itemId) {

        this.itemId = itemId;
    }

    public void genRcmdReason(String itemNickName, List<String> rcmdReason) {
        this.itemNickName = itemNickName;
        this.rcmdReason = rcmdReason;
    }

}
