/**
 * @(#)ProphetUserStatConfig.java, 2020/10/25.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

import com.netease.yx.bot.core.model.entity.UserOrder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class ProphetUserStatConfig {
    private static final String KEY_NAME_TICKET_STATUS = "ticketStatus";
    private static final String KEY_NAME_APPLY_TYPE_CODE = "applyTypeCode";
    private static final String KEY_NAME_APPLY_STATUS = "applyStatus";
    private static final String KEY_NAME_OUT_STORE_STATUS = "outstoreStatus";
    private static final String KEY_NAME_TRACKING_STATUS = "trackingStatus";

    private static final String KEY_NAME_USER_ID = "userId";
    private static final String KEY_NAME_ITEM_ID = "itemId";
    private static final String KEY_NAME_SPMC_STATUS = "spmcStatus";
    private static final String KEY_NAME_ORDER_STATUS = "orderStatus";
    private static final String KEY_NAME_ORDER_ID = "orderId";
    private static final String KEY_NAME_USER_CREDIT_LEVEL = "userCreditLevel";


    private Map<String, String> conditionMap;
    private List<Long> knowledgeIds;

    public List<Long> check(UserOrder userOrder) {
        List<Long> result = new ArrayList<>();
        if (conditionMap == null || knowledgeIds == null) {
            return result;
        }

        boolean flag = true;
        for (Map.Entry<String, String> entry : conditionMap.entrySet()) {
            String key = entry.getKey();
            switch (key) {
                case KEY_NAME_APPLY_STATUS:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getApplyStatus()));
                    break;
                case KEY_NAME_APPLY_TYPE_CODE:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getApplyTypeCode()));
                    break;
                case KEY_NAME_TICKET_STATUS:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getTicketStatus()));
                    break;
                case KEY_NAME_OUT_STORE_STATUS:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getOutstoreStatus()));
                    break;
                case KEY_NAME_TRACKING_STATUS:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getTrackingStatus()));
                    break;
                case KEY_NAME_USER_ID:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getUserId()));
                    break;
                case KEY_NAME_ITEM_ID:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getItemId()));
                    break;
                case KEY_NAME_SPMC_STATUS:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getSpmcStatus()));
                    break;
                case KEY_NAME_ORDER_STATUS:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getOrderStatus()));
                    break;
                case KEY_NAME_ORDER_ID:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getOrderId()));
                    break;
                case KEY_NAME_USER_CREDIT_LEVEL:
                    flag = StringUtils.equals(entry.getValue(), String.valueOf(userOrder.getUserCreditLevel()));
                    break;
                default:
                    flag = false;
            }
            if (!flag) {
                break;
            }
        }
        if (flag) {
            result.addAll(knowledgeIds);
        }
        return result;
    }
}