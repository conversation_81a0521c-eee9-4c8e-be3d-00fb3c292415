/**
 * @(#)FatigueType.java, 2020/12/1.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

import com.netease.yx.bot.core.model.entity.EnumIntegerInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@AllArgsConstructor
@Getter
public enum FatigueType implements EnumIntegerInterface<FatigueType> {
    /**
     *
     */
    NULL(0, "NULL"),
    /**
     *
     */
    EVERYTIME(1, "每次进入都推送"),
    /**
     *
     */
    ONCE_A_DAY(2, "一天一次"),
    /**
     *
     */
    ONCE_PER_SESSION(3, "一个会话一次");

    private final int value;

    private final String desc;

    @Override
    public FatigueType genEnumByIntValue(int intValue) {
        for (FatigueType fatigueType : FatigueType.values()) {
            if (fatigueType.getValue() == intValue) {
                return fatigueType;
            }
        }
        return FatigueType.NULL;
    }
}