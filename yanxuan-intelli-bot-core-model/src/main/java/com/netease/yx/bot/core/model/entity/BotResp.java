package com.netease.yx.bot.core.model.entity;

import com.netease.yx.bot.core.model.constant.ReplyState;
import com.netease.yx.bot.core.model.constant.StandardReply;
import com.netease.yx.bot.core.model.constant.StatType;
import com.netease.yx.bot.core.model.entity.faq.FaqIdRslt;
import com.netease.yx.bot.core.model.entity.guide.GuideRCMDListRslt;
import com.netease.yx.bot.core.model.entity.guide.GuideRslt;
import com.netease.yx.bot.core.model.entity.intent.IntentRslt;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/12/19.
 * 简化BotFinalResult的参数版本，无用的字段剔除。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BotResp implements Serializable {
    public static final String CHAT_GPT = "ChatGPT";
    /**
     * 参数说明：
     * (a) state, 请求返回状态字段
     * (b) text, String, 展示文案、话术、文本
     * (c) faqRslt, FaqIdResult, faq结果
     * (d) statType, int, 统计组统计时需要区分结果类型
     */

    // 明文返回
    private String text;
    // FAQ返回的结果
    private FaqIdRslt faqRslt;
    // 接口状态
    private ReplyState state = ReplyState.SUCCESS;
    // 统计类型
    private int statType;
    // 意图结果
    private IntentRslt intentRslt;
    // 是否完全无结果，算法侧完全不知道如何回复
    private boolean noAnswer;

    /**
     * 导购新增
     * (a) guideRslt, GuideRslt， 导购内容
     * (c) guideRcmdReason, string , 统一推荐语，用于多list的商品推荐
     */
    private String guideRcmdReason;
    private List<GuideRslt> guideRslt;
    /**
     * 智能转人工按钮（判断是否出人工按钮）
     */
    private boolean isRGVisible;

    /**
     * 闲聊类别
     */
    private String chatIntent;

    public static BotResp buildOnlyStateRslt(ReplyState replyState) {
        return BotResp.builder().statType(StatType.UNKNOWN).state(replyState).noAnswer(true).build();
    }

    public static BotResp buildTextUseLessRslt() {
        return BotResp.builder().statType(StatType.TEXT_DEFAULT_UESLESS).text(StandardReply.DEFAULT_REPLY).state(ReplyState.SUCCESS).build();
    }

    public static BotResp buildTextGreetingRslt() {
        return BotResp.builder().statType(StatType.TEXT_DEFAULT_UESLESS).text(StandardReply.DEFAULT_CHITCHAT_REPLY).state(ReplyState.SUCCESS).build();
    }

    public static BotResp buildTextChitClassifyRslt(String text, String chatName) {
        return BotResp.builder().statType(StatType.TEXT_DEFAULT_UESLESS).text(text).chatIntent(chatName).state(ReplyState.SUCCESS).noAnswer(false).build();
    }

    public static BotResp buildTextWorkRelatedRslt(boolean noAnswer) {
        return BotResp.builder().statType(StatType.TEXT_DEFAULT_UESLESS).text(StandardReply.DEFAULT_WORK_RELATED_REPLY).state(ReplyState.SUCCESS).noAnswer(noAnswer).build();
    }

    public static BotResp buildTextRslt(String text, int statType, IntentRslt intentRslt, boolean noAnswer) {
        return BotResp.builder().statType(statType).text(text).intentRslt(intentRslt).state(ReplyState.SUCCESS).noAnswer(noAnswer).build();
    }

    public static BotResp buildFaqIdRslt(FaqIdRslt faqIdResult, IntentRslt intentRslt) {
        if (faqIdResult.getType() == FaqIdRslt.TYPE_FAQ_PRECISE) {
            return BotResp.builder().faqRslt(faqIdResult).intentRslt(intentRslt).statType(StatType.FAQ_UNIQUE).state(ReplyState.SUCCESS).build();
        } else {
            return BotResp.builder().faqRslt(faqIdResult).intentRslt(intentRslt).statType(StatType.FAQ_MULTI).state(ReplyState.SUCCESS).text(StandardReply.MULTI_FAQ_REPLY).build();
        }
    }

    public static BotResp buildOrderRcmdKnowledgeRslt(List<Long> knowledgeIds) {
        FaqIdRslt faqIdRslt = new FaqIdRslt(knowledgeIds);
        if (knowledgeIds.size() == 1) {
            faqIdRslt.setType(FaqIdRslt.TYPE_FAQ_PRECISE);
        }
        return BotResp.builder().faqRslt(faqIdRslt).statType(StatType.FAQ_MULTI).state(ReplyState.SUCCESS).text(StandardReply.MULTI_FAQ_REPLY).build();
    }

    public static BotResp buildParamErrorRslt() {
        return BotResp.builder().state(ReplyState.PARAM_ERROR).build();
    }

    public static BotResp buildIntalnalErrorRslt() {
        return BotResp.builder().state(ReplyState.INTERNAL_ERROR).build();
    }

    public void genGuideRslt(GuideRslt guideRslt) {
        this.guideRslt = new ArrayList<>(Collections.nCopies(1, guideRslt));
        this.intentRslt = null;
        this.faqRslt = null;
        this.text = null;
    }

    public void genGuideRslt(List<GuideRslt> guideRslt) {
        this.guideRslt = guideRslt;
        this.intentRslt = null;
        this.faqRslt = null;
        this.text = null;
    }

    public void genProZeroActiveRslt(GuideRCMDListRslt guideRCMDListRslt) {
        this.guideRslt = guideRCMDListRslt.getGuideRsltList();
        this.guideRcmdReason = guideRCMDListRslt.getRcmdReason();
    }

    public void resetFAQRslt() {
        this.intentRslt = null;
        this.faqRslt = null;
        this.text = null;
    }
}
