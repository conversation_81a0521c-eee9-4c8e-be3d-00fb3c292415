/**
 * @(#)ErrorID.java, 2021/12/15.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.qualityInspection;

import lombok.Data;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class InspectionErrorID {
    public static final int ISCONTAINTERM = 1001;
    public static final int ISCOMTAINTYPO = 1002;
    public static final int ISCONTAINSPECIALLCHAR = 1003;
    public static final int ISNEGATIVEQA = 1004;
    public static final int ISIMPATIENCE = 1005;
    public static final int ISNEGATIVEMOTION = 1006;
    public static final int NOTCONTAINAPPEASING = 1007;
    public static final int ISCOMPLAINT = 1008;
    public static final int NOTRELATEDRECOMMEND = 1009;

}

