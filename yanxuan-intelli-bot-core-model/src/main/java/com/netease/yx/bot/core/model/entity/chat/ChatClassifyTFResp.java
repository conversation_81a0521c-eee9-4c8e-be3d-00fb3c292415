package com.netease.yx.bot.core.model.entity.chat;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@AllArgsConstructor
public class ChatClassifyTFResp {
    private ChatClassifyTFDataResp DEFAULT_CHAT_RESP = new ChatClassifyTFDataResp("其它", 1f, "strong");

    private List<ChatClassifyTFDataResp> predictions;

    public ChatClassifyTFResp() {
        this.predictions = new ArrayList<>(Arrays.asList(DEFAULT_CHAT_RESP));
    }

    public String getChat() {
        return predictions.get(0).getChatCH();
    }

    public Float getScore() {
        return predictions.get(0).getScore();
    }

    public String getConfidence() {
        return predictions.get(0).getConfidence();
    }
}
