package com.netease.yx.bot.core.model.entity.item;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ItemBaseInfoReq {
    private static final String SPU_COPY_TYPE = "spuCopyType";
    private static final String SRC_SPU_ID = "srcSpuId";

    private long id;
    private List<String> spuProps;

    public static ItemBaseInfoReq buildSimpleReq(long id) {
        List<String> spuProps = new ArrayList<String>() {{
            add(SPU_COPY_TYPE);
            add(SRC_SPU_ID);
        }};

        return new ItemBaseInfoReq(id, spuProps);
    }
}
