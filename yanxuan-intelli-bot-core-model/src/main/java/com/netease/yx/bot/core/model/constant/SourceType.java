/**
 * @(#)SourceType.java, 2020/9/3.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.constant;

/**
 * <AUTHOR> @ corp.netease.com)
 */
public enum SourceType {
    /**
     * NULL
     */
    NULL,
    /**
     * ALL， 这里是给主动预测配置使用
     */
    ALL,
    /**
     * APP 订单入口
     */
    ORDER_DETAIL,
    /**
     * APP 售后进度页入口
     */
    AFTER_SALE_PROCESS,
    /**
     * APP 售后入口
     */
    AFTER_SALE_SERVICE,
    /**
     * APP 个人中心入口
     */
    USER_CENTER,
    /**
     * APP 商品详情页入口
     */
    ITEM_DETAIL,
    /**
     * APP 消息中心入口
     */
    MESSAGE_CENTER,
    /**
     * APP 商品评论页
     */
    CUSTOMER_REVIEWS,
    /**
     * 微信
     */
    WECHAT,
    /**
     * WEB
     */
    WEB,
    /**
     * 入口余额页
     */
    BALANCE,
    /**
     * pro会员权益页
     */
    PRO_MEMBERSHIP,
    /**
     * 众筹商品页
     */
    CROWDFUNDING_ITEM;

    public static SourceType get(String sourceTypeStr) {
        // 用NULL 做兜底可能更好， 不然会误触发一些主动预测里面的入口兜底策略
        SourceType sourceType = USER_CENTER;
        try {
            if (sourceTypeStr != null) {
                sourceType = SourceType.valueOf(sourceTypeStr);
            }
        } catch (Exception e) {
            sourceType = USER_CENTER;
        }
        return sourceType;
    }
}