/**
 * @(#)UserOrder.java, 2020/10/16.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 * 跟 数仓 dm.dm_yx_kf_intelligence_d 对应
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UserOrder implements Comparable<UserOrder>, Serializable {
    public static final int APPLY_TYPE_CODE_CHANGE = 1;
    public static final int APPLY_TYPE_CODE_RETURN = 2;
    public static final int APPLY_TYPE_CODE_PRICE = 3;
    public static final int APPLY_TYPE_CODE_FIX = 4;
    public static final int APPLY_TYPE_CODE_REJECT = 5;
    public static final String USER_CREDIT_LEVEL_R1 = "r1";
    public static final String USER_CREDIT_LEVEL_R2 = "r2";
    public static final String USER_CREDIT_LEVEL_R3 = "r3";
    public static final String USER_CREDIT_LEVEL_R4 = "r4";
    public static final String USER_CREDIT_LEVEL_R5 = "r5";
    private static final int PROPERTY_NUM = 33;
    /**
     * 订单id
     */
    private long orderId;
    /**
     * 订单状态
     * <p>
     * 0-未付款 1-已付款 2-系统取消 3-用户取消 4-客服取消 6-用户付款后取消
     * 实际使用：1,4,6
     */
    private int orderStatus;
    /**
     * 订单创建时间
     */
    private long createTime;
    /**
     * 订单支付时间
     */
    private long payTime;
    /**
     * 用户账号id
     */
    private long userId;

    /**
     * 超级会员状态
     * <p>
     * 0:未试用,
     * 1:试用中,
     * 2:试用结束未购买,
     * 3:已购买且在有效期内,
     * 4:购买后过期,
     * 11:亲友卡使用中,
     * 12:亲友卡使用过期
     * <p>
     * 主站超会对应status=3
     */
    private int spmcStatus;
    /**
     * 用户信誉等级
     * <p>
     * R1,R2,R3,R4,R5,审核通过
     */
    private String userCreditLevel;
    /**
     * 商品id
     */
    private long itemId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * skuid
     */
    private long skuId;
    /**
     * sku数量
     */
    private long count;
    /**
     * 订单+sku的实际支付金额
     */
    private double realPriceAmount;
    /**
     * 仓库id
     */
    private long storehouseId;
    /**
     * 仓库名称
     */
    private String storehouseName;
    /**
     * 出库批次号
     */
    private String outstoreNo;
    /**
     * 包裹id
     */
    private String packageId;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人手机号
     */
    private String receiverMobile;
    /**
     * 省份名称
     */
    private String receiverProvince;
    /**
     * 城市名称
     */
    private String receiverCity;
    /**
     * 区域名称
     */
    private String receiverDistrict;
    /**
     * 具体街道地址
     */
    private String address;
    /**
     * 出库状态
     * 已取消,待仓库处理,	审核通过
     */
    private String outstoreStatus;
    /**
     * 物流承运商
     */
    private String carrier;
    /**
     * 物流状态
     * <p>
     * 7:等待发货
     * 8:已发货(等待收货)
     * 9:已收货(等待评价)
     * 10:已评价
     * 12:系统取消
     * 13:用户取消
     * 14:客服取消
     * 15:用户付款后取消
     */
    private long trackingStatus;
    /**
     * 运单号
     */
    private String trackingNum;
    /**
     * 确认收货时间
     */
    private long confirmTime;
    /**
     * 【申请单】申请单类型
     * <p>
     * 1 换货单，
     * 2 退货单，
     * 3 价保，
     * 4 维修，
     * 5 拒收
     */
    private long applyTypeCode;
    /**
     * 【申请单】申请单状态：
     * <p>
     * 换货：1-待审核，2-待用户寄回，3-待仓库收货质检，4-退货成功(已确认收货)，5-客服审核不通过，6-用户取消，7-系统取消，
     * 8-客服取消，9-等待客服确认，10-等待用户收货，11-客服拒绝，12-审核通过，直接退款，13-换新待发货，14-换新失败，待仓库收取新件，15-待拆卸商品，16-异常关闭，31-修改单号成功
     * 实际使用：1,2,3,5,6,7,8,9,10,11
     * <p>
     * 退货：1-待审核，2-待用户寄回，3-待仓库收货质检，4-退货成功(已确认收货)，5-客服审核不通过，6-用户取消，7-系统取消，
     * 8-客服取消，9-等待客服确认，10-等待用户收货，11-客服拒绝，12-审核通过，直接退款，13-换新待发货，14-换新失败，待仓库收取新件，15-待拆卸商品，16-异常关闭，31-修改单号成功
     * 实际使用：1,2,3,5,6,8,9,11,15
     * <p>
     * 价保：1-待审核，2-审核通过，3-审核驳回，4-价保完成，5-用户端申请中，6-用户端申请失败 7-未知
     * 实际使用：1,2,3,4,5,6,7
     * <p>
     * 维修：1-待客服审核，2-待用户寄回商品，3-客服审核不通过，4-系统取消，5-用户取消，6-客服取消，7-仓库收货待客服确认，
     * 8-待供应商收货并鉴定，9-换新待供应商安排发货，10-换新待客服安排发货，11-待用户收货，12-无法换新待客服确认，13-维修结束，
     * 14-无法换新已处理，15-客服拒绝，16-换新待严选出库，17-换新待严选回寄，18-供应商维修中，19-已寄送（待仓库收货）
     * 实际使用：1,2,3,4,5,8,11,13,14,18
     * <p>
     * 拒收：	1-待审核，2-待用户寄回，3-待仓库收货质检，4-退款完成，5-客服审核不通过，6-用户取消，7-系统取消，
     * 8-客服取消，9-等待客服确认，10-等待用户收货，11-客服拒绝，12-换货成功，13-换新待发货，14-换新失败，待仓库收取新件，15-待拆卸商品，16-异常关闭，31-修改单号成功
     * 实际使用：1,3,5,6
     */
    private long applyStatus;
    /**
     * 【申请单】Id
     */
    private long applyId;
    /**
     * 【工单】全局id
     */
    private long globalId;
    /**
     * 【工单详情】主题
     */
    private String ticketContentTitle;
    /**
     * 【工单详情】纯文本详细内容
     */
    private String ticketDescPlainText;
    /**
     * 工单流转状态
     * <p>
     * -1:删除状态,
     * 0:创建 (新建未提交),
     * 1:转交待处理（转交到工单池或到业务组或业务部门） ,
     * 2:待二次处理(一线撤回、提交到受理组被拒、转交到其他组撤回，转交到其他组被拒，转交到业务部门被拒),
     * 100:处理中,
     * 200:完结
     */
    private int ticketStatus;
    /**
     * 普通会员等级
     * 0,1,2,3,4,5,6 对应v0,v1,v2,v3,v4,v5,v6
     */
    private int mbrLevel;

    public static UserOrder buildUserOrderFromDqsObj(List<Object> dqsObjList) {
        if (dqsObjList == null || dqsObjList.size() != PROPERTY_NUM) {
            return null;
        }
        return UserOrder.builder()
                .orderId(obj2Long(dqsObjList.get(0)))
                .orderStatus(obj2int(dqsObjList.get(1)))
                .createTime(obj2Long(dqsObjList.get(2)))
                .payTime(obj2Long(dqsObjList.get(3)))
                .userId(obj2Long(dqsObjList.get(4)))

                .spmcStatus(obj2int(dqsObjList.get(5)))
                .userCreditLevel(obj2String(dqsObjList.get(6)))
                .itemId(obj2Long(dqsObjList.get(7)))
                .itemName(obj2String(dqsObjList.get(8)))
                .skuId(obj2Long(dqsObjList.get(9)))

                .count(obj2Long(dqsObjList.get(10)))
                .realPriceAmount(obj2Double(dqsObjList.get(11)))
                .storehouseId(obj2Long(dqsObjList.get(12)))
                .storehouseName(obj2String(dqsObjList.get(13)))
                .outstoreNo(obj2String(dqsObjList.get(14)))

                .packageId(obj2String(dqsObjList.get(15)))
                .receiverName(obj2String(dqsObjList.get(16)))
                .receiverMobile(obj2String(dqsObjList.get(17)))
                .receiverProvince(obj2String(dqsObjList.get(18)))
                .receiverCity(obj2String(dqsObjList.get(19)))

                .receiverDistrict(obj2String(dqsObjList.get(20)))

//                .address(dqsObjList.get(21))

                .outstoreStatus(obj2String(dqsObjList.get(21)))
                .carrier(obj2String(dqsObjList.get(22)))
                .trackingStatus(obj2Long(dqsObjList.get(23)))

                .trackingNum(obj2String(dqsObjList.get(24)))
                .confirmTime(obj2Long(dqsObjList.get(25)))
                .applyTypeCode(obj2Long(dqsObjList.get(26)))
                .applyStatus(obj2Long(dqsObjList.get(27)))
                .applyId(obj2Long(dqsObjList.get(28)))

                .globalId(obj2Long(dqsObjList.get(29)))
                .ticketContentTitle(obj2String(dqsObjList.get(30)))
                .ticketDescPlainText(obj2String(dqsObjList.get(31)))
                .ticketStatus(obj2int(dqsObjList.get(32)))

                .build();
    }

    private static int obj2int(Object object) {
        int result = 0;
        if (object == null) {
            return result;
        }
        try {
            result = Integer.parseInt(object.toString());
        } catch (Exception e) {
        }
        return result;
    }

    private static long obj2Long(Object object) {
        long result = 0;
        if (object == null) {
            return result;
        }
        try {
            result = Long.parseLong(object.toString());
        } catch (Exception e) {
        }
        return result;
    }

    private static double obj2Double(Object object) {
        double result = 0;
        if (object == null) {
            return result;
        }
        try {
            result = Double.parseDouble(object.toString());
        } catch (Exception e) {
        }
        return result;
    }

    private static String obj2String(Object object) {
        if (object == null) {
            return null;
        } else {
            return object.toString();
        }
    }

    /**
     * 降序，从大到小
     *
     * @param o
     * @return
     */
    @Override
    public int compareTo(UserOrder o) {
        return -Long.compare(createTime, o.createTime);
    }
}