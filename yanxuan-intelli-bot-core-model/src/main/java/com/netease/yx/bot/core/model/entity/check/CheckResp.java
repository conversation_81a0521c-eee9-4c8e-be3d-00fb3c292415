package com.netease.yx.bot.core.model.entity.check;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CheckResp {
    // 检查相似的结果，map的长度跟入参的needCheckKnowledgeMap一致
    // map的key就是输入需要检查的知识的id，每个知识的检查结果可能会有多条
    private Map<Long, List<CheckKnowledge>> checkMap;
}
