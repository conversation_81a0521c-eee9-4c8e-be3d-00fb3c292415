/**
 * @(#)BotRespWithLog.java, 2020/11/23.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.log;

import com.netease.yx.bot.core.model.entity.BotResp;
import lombok.Data;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class BotRespWithLog {
    private BotResp botResp;
    private MainPipelineLog mainPipelineLog;

    public BotRespWithLog(BotResp botResp, MainPipelineLog mainPipelineLog) {
        this.botResp = botResp;
        this.mainPipelineLog = mainPipelineLog;
        if (mainPipelineLog != null) {
            mainPipelineLog.setOutput(botResp);
        }
    }
}