package com.netease.yx.bot.core.model.constant;

public class StatType {
    public static final int UNKNOWN = 0;


    /**
     * 文本回复：无意义的默认回复
     */
    public static final int TEXT_DEFAULT_UESLESS = 1;

    /**
     * 文本回复：闲聊模块-自我闲聊
     */
    public static final int TEXT_SELF_CHITCHAT = 2;
    /**
     * 文本回复：闲聊模块-大规模闲聊
     */
    public static final int TEXT_CORPUS_CHITCHAT = 3;

    /**
     * 文本回复：kbqa回复，或有尺码图片
     */
    public static final int TEXT_PIC_KBQA = 4;
    /**
     * 不再使用
     */
    public static final int ONLY_PIC_KBQA = 41; //

    /**
     * 文本回复：导购回复
     */
    public static final int TEXT_GUIDE = 5;

    /**
     * 转人工
     */
    public static final int TURN_MANUAL = 6;

    /**
     * 单条faq回复
     */
    public static final int FAQ_UNIQUE = 7;
    /**
     * 多条faq回复
     */
    public static final int FAQ_MULTI = 8;


    /**
     * 匹配单条流程线回复
     */
    public static final int BOT_UNIQUE = 9;
    /**
     * 匹配多条流程线回复
     */
    public static final int BOT_MULTI = 10;

    /**
     * 匹配单条faq + 单条流程线
     */
    public static final int FAQ_UNIQUE_BOT_UNIQUE = 11;
    /**
     * 匹配单条faq + 多条流程线
     */
    public static final int FAQ_UNIQUE_BOT_MULTI = 12;


    /**
     * 匹配多条faq + 单条流程线
     */
    public static final int FAQ_MULTI_BOT_UNIQUE = 13;
    /**
     * 匹配多条faq + 多条流程线
     */
    public static final int FAQ_MULTI_BOT_MULTI = 14;

    /**
     * 多答案算法答案  （目前不可用）
     */
    public static final int TEXT_MULTI_ANSWER = 15;

}
