/**
 * @(#)SummaryResult.java, 2019/11/23.
 * <p/>
 * Copyright 2019 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.workbench;

import lombok.Data;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class SummaryResult {
    // 总结摘要内容文本
    private String summary = "REVERSED";
    // 总结摘要的效果评价，默认为BAD
    private SummaryConfidence summaryConfidence = SummaryConfidence.BAD;

    public static SummaryResult buildDefaultResult() {
        return new SummaryResult();
    }
}