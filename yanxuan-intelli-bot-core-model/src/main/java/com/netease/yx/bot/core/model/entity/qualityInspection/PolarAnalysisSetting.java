/**
 * @(#)PolarAnalysisSetting.java, 2021/12/15.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.qualityInspection;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> @ corp.netease.com)
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "smartworkfunction.polaranalysis")
public class PolarAnalysisSetting {

    private String url;
    private String host;
}