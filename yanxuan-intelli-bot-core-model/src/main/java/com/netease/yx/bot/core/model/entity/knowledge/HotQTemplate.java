/**
 * @(#)HotQTemplate.java, 2020/9/15.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.knowledge;

import lombok.Data;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class HotQTemplate {
    private long id;
    private String templateName;
    private String introduceText;
    private String crmUserCategoryId;
    private int defaultFlag;
    private int status;
}