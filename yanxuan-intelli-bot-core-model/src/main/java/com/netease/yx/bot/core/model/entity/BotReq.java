package com.netease.yx.bot.core.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BotReq implements Serializable {
    public static final int TYPE_TEXT = 1;
    public static final int TYPE_IMAGE = 2;
    public static final int TYPE_ORDER_CARD = 3;
    public static final int TYPE_ITEM_CARD = 4;
    public static final int TYPE_LINK = 9;

    // 会话id
    private String sessionId;
    // 消息id
    private String messageId;
    // 用户id
    private long userId;
    // 用户名
    private String userName;
    // 消息类型，枚举定义参考 百灵前端 messageType
    private int type;
    // 消息内容
    private String data;
    // 最近一次的商品卡片id
    private Long itemId;
    // 订单id
    private long orderId;
    // 订单的包裹id
    private long packageId;
    // 是否订单推荐问，暂时没有用
    private boolean isOrderQues;
    // 点击的FAQ（猜想+模糊匹配）
    private List<Long> faqIds;
    // 用户所属客服组ID
    private long kfGroupId;
}
