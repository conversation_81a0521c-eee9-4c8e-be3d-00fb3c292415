package com.netease.yx.bot.core.model.entity.kfkm;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AlgoItemAttrWithId {
    /**
     * {
     * "itemId":470208152,
     * "attribute":{
     * "id":470219470,
     * "level":1,
     * "name":"自定义测试诶",
     * "inputType":2,
     * "valueList":[
     * {
     * "id":100000001011,
     * "value":"123123",
     * "rank":0
     * }
     * ],
     * "source":600
     * },
     * "required":2,
     * "rank":0,
     * "visibleStatus":0,
     * "source":600,
     * "createTime":1697436686443
     * }
     */
    private long itemId;
    private AlgoItemAttr attribute;

    private int required;
    private int visibleStatus;
}
