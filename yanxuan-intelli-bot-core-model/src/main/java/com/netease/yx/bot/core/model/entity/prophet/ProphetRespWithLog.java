/**
 * @(#)ProphetRespWithLog.java, 2020/12/1.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

import lombok.Data;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class ProphetRespWithLog {
    private ProphetResp prophetResp;
    private ProphetPipelineLog prophetPipelineLog;

    public ProphetRespWithLog(ProphetRes<PERSON> prophetResp, ProphetPipelineLog prophetPipelineLog) {
        this.prophetResp = prophetResp;
        this.prophetPipelineLog = prophetPipelineLog;

        if (this.prophetPipelineLog != null) {
            this.prophetPipelineLog.setOutput(prophetResp);
        }
    }
}