/**
 * @(#)ProphetRuleSchemaNode.java, 2020/12/1.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.prophet;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class RuleSchemaNode {
    private String id;
    private String desc;
    private List<RuleSchemaType> childList;
}