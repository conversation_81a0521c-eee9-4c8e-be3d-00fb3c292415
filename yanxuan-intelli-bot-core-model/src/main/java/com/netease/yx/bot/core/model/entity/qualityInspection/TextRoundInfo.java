/**
 * @(#)TextRoundInfo.java, 2021/12/15.
 * <p/>
 * Copyright 2021 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity.qualityInspection;

import lombok.Data;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
public class TextRoundInfo {
    //session包含的对话文本
    private String inputText;
    //每条文本对应的标志，"0"表示用户，"1"表示客服
    private Long textType;
    //每条对话文本对应的七鱼客服id，-1为机器人客服
    private Long staffId;
    //每条对话文本的对应的状态, 消息状态：1-正常, 2-已撤回
    private Long msgStatus;
    // 每条对话文本是否自动回复， 自动回复：0-否,1-是
    private Long isAutoReply;
    // 每条对话文本的创建时间
    private Long messageCreateTs;

}