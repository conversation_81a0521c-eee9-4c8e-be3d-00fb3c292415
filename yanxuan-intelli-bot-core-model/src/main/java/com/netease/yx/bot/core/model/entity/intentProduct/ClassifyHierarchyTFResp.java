package com.netease.yx.bot.core.model.entity.intentProduct;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@AllArgsConstructor
public class ClassifyHierarchyTFResp {
    private ClassifyHierarchyTFDataResp DEFAULT_CLS_HIRCY_RESP = new ClassifyHierarchyTFDataResp("其它", "other", "strong", 1f);
    private List<ClassifyHierarchyTFDataResp> predictions;

    public ClassifyHierarchyTFResp() {
        this.predictions = new ArrayList<>(Arrays.asList(DEFAULT_CLS_HIRCY_RESP));
    }

    public String getIntent() {
        return predictions.get(0).getIntent();
    }

    public String getIntentCH() {
        return predictions.get(0).getIntentCH();
    }

    public String getConfidence() {
        return predictions.get(0).getConfidence();
    }

    public Float getScore() {
        return predictions.get(0).getScore();
    }
}
