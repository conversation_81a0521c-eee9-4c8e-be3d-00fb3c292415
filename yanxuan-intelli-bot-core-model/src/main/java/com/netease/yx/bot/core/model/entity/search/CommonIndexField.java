package com.netease.yx.bot.core.model.entity.search;

public enum CommonIndexField {

    GENDER(1L, "性别"),
    MANUFACTURE(2L, "制造商"),
    ITEMBOOST(3L, "单品权重"),
    CATEGORYBOOST(4L, "类目权重"),
    PHYCATEGORYID(5L, "物理类目ID"),
    PHYCATEGORYNAME(6L, "物理类目名称"),

    NICKNAME_ORIGIN(12L, "商品艺名（原标题未分词）"),
    //老版本qp,已废弃
    NICKNAME(13L, "商品艺名（前台名字）"),

    ALLOW_RED_PACKET(14L, "商品是否不能用红包"),
    ALLOW_COUPON(15L, "商品是否不能用优惠券"),


    ZHONGCHOU(16L, "众筹商品"),
    SALECATEGORY(20L, "销售类目"),
    ITEMTAGFORSEARCH(18L, "召回标签"),
    ITEMATTRIBUTE(21L, "商品属性"),
    ITEMATTRIBUTENAME(53L, "商品属性名称"),  //注意：格式为1000054_1042008#口味_其他
    MAKERID(19L, "制造商id"),
    SHIPAREA(22L, "配送区域id"),
    SHIPAREANAME(54L, "配送区域名称"),
    //众筹活动状态=1时支持露出
    ZHONGCHOU_ACT(56l, "众筹活动状态"),
    LINGYUANLING(57l, "0元领库存状态"),

    //新版qp
    WORD_SEG(32L, "商品名称分词2"),
    NICKNAME_ATTR(33L, "商品销售标题分词2"),

    BRAND_ID(40l, "品牌id"),

    ;


    long type;
    String name;

    CommonIndexField(long type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getType() {
        return type;
    }

    public void setType(long type) {
        this.type = type;
    }
}