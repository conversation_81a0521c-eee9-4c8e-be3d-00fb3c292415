package com.netease.yx.bot.core.model.entity.channel;


import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum ChannelType {
    APP(0, "qy"),
    TAOBAO(1, "tb"),
    DOUYIN(2, "dy"),
    J<PERSON>(3, "jd"),
    WX(4, "wx"),
    PDD(5, "pdd"),
    KUAISHOU(6, "ks"),
    ALL(99, "all"),
    OTHER(100, "other");

    private final int code;
    private final String name;

    public static ChannelType getByCode(int code) {
        switch (code) {
            case 0:
                return APP;
            case 1:
                return TAOBAO;
            case 2:
                return DOUYIN;
            case 3:
                return JD;
            case 4:
                return WX;
            case 5:
                return PDD;
            case 6:
                return KUAISHOU;
            case 99:
                return ALL;
        }
        return OTHER;
    }

    public static ChannelType getByName(String name) {
        for (ChannelType channelType : ChannelType.values()) {
            if (StringUtils.equals(name.toLowerCase(), channelType.name)) {
                return channelType;
            }
        }
        return TAOBAO;
    }
}

