package com.netease.yx.bot.core.model.entity.kfkm;

//import com.beust.ah.A;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
public class AlgoAnswer implements Comparable<AlgoAnswer>{
    public static final int ANSWER_TYPE_INTELLI_SOLUTION = 5;
    public static final int ANSWER_TYPE_SOP_FLOW = 3;
    public static final int ANSWER_TYPE_SOP_NODE = 2;

    public static final int ANSWER_USE_INTERNAL = 1;
    public static final int ANSWER_USE_OUT = 2;
    public static final int ANSWER_USE_INTERNAL_AND_OUT = 3;

    // 答案id
    private long answerId;
    // 答案内容，可能是富媒体
    private String answerContent;
    // 答案类型: 1.富文本,2.SOP节点,3.SOP流程,4.<PERSON><PERSON><PERSON>,5.智能解决方案流程
    private int answerType;
    // 答案用途: 1.对内，2.对外，3-人工和机器人
    private int answerUse;
    // 商品粒度的标签
    private String itemInfoLabel;
    // 类目粒度的标签, 物理类目
    private List<Integer> itemCateLabel;
    // 类目粒度的标签, 物理类目
    private String itemCateLabelStr;
    // 其他标签
    private List<String> otherLabel;
    // 渠道ID信息，1表示主站，多渠道用逗号分隔
    private List<Integer> channel;
    // 生效时间
    private long effectiveTime;
    // 失效时间
    private long expiryTime;
    // 删除状态: 0.未删除，1.已删除
    private int deleteFlag;
    // 相似的快捷短语,json格式， 主要是在APP机器人里使用，当展示当前答案时，一起在下方展示关联的快捷短语
    private String relevanceShortCut;
    // 关联的FAQ json格式， 主要是在APP机器人里使用，当展示当前答案时，一起在下方展示关联的其他FAQ
    private String relevanceKnowledge;
    // 平台
    private List<String> platform;
    // 很多是0, 毫秒，下同
    private long createTime;
    private long editTime;
    private long updateTime;

    // 优先级：商品答案、物理类目答案、通用答案
    // 如果优先级一样，则按editTime 更近处理
    @Override
    public int compareTo(AlgoAnswer o) {
        int labelCompareResult = compareAnswerLabel(o);
        if (labelCompareResult == 0){
            // 越大越好
            return -Long.compare(editTime, o.editTime);
        }else {
            return labelCompareResult;
        }
    }

    private int compareAnswerLabel(AlgoAnswer o){
        if (StringUtils.isNotEmpty(itemInfoLabel)){
            if (StringUtils.isNotEmpty(o.itemInfoLabel)){
                return 0;
            }
            return -1;
        }else if (CollectionUtils.isNotEmpty(itemCateLabel)) {
            if (StringUtils.isNotEmpty(o.itemInfoLabel)) {
                return 1;
            } else if (CollectionUtils.isNotEmpty(o.itemCateLabel)) {


                return -Integer.compare(itemCateLabel.size(), o.itemCateLabel.size());
            } else {
                return -1;
            }
        }else {
            if (StringUtils.isNotEmpty(o.itemInfoLabel) || CollectionUtils.isNotEmpty(o.itemCateLabel)){
                return 1;
            }else {
                return 0;
            }
        }
    }

    public static void main(String[] args) {
        AlgoAnswer algoAnswer1 = new AlgoAnswer();
        algoAnswer1.setAnswerId(1L);
        algoAnswer1.setItemInfoLabel("1");
        algoAnswer1.setEditTime(10);

        AlgoAnswer algoAnswer2 = new AlgoAnswer();
        algoAnswer2.setAnswerId(2L);
        algoAnswer2.setItemInfoLabel("2");

        AlgoAnswer algoAnswer3 = new AlgoAnswer();
        algoAnswer3.setAnswerId(3L);
        List<Integer> cateIds = new ArrayList<>();
        cateIds.add(1);
        cateIds.add(2);

        algoAnswer3.setItemCateLabel(cateIds);

        AlgoAnswer algoAnswer4 = new AlgoAnswer();
        algoAnswer4.setAnswerId(4L);
        List<Integer> cateIds2 = new ArrayList<>();
        cateIds2.add(1);
        cateIds2.add(2);
        cateIds2.add(3);
        algoAnswer4.setItemCateLabel(cateIds2);

        AlgoAnswer algoAnswer5 = new AlgoAnswer();
        algoAnswer5.setAnswerId(5L);
        algoAnswer5.setEditTime(29);

        AlgoAnswer algoAnswer6 = new AlgoAnswer();
        algoAnswer6.setAnswerId(6L);

        algoAnswer6.setEditTime(20);
        List<AlgoAnswer> algoAnswers = new ArrayList<>();
        algoAnswers.add(algoAnswer3);
        algoAnswers.add(algoAnswer5);
        algoAnswers.add(algoAnswer2);
        algoAnswers.add(algoAnswer4);
        algoAnswers.add(algoAnswer6);
        algoAnswers.add(algoAnswer1);

        Collections.sort(algoAnswers);
        for (AlgoAnswer algoAnswer:algoAnswers){
            System.out.println(algoAnswer);
        }
    }
}
