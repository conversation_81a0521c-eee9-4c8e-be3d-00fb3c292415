/**
 * @(#)KbqaResp.java, 2020/11/20.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.core.model.entity;

import com.netease.yx.bot.core.model.constant.KbqaType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KbqaResp implements Serializable {
    private String showText;
    private KbqaType kbqaType;
    private double score;
}