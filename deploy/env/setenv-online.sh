java_home=/home/<USER>/packages/jdk1.8.0_91
jvm_opts_property="-Dspring.profiles.active=online -Dserver.port=8080"
jvm_opts_base="-cp . -ea -Xms4096m -Xmx4096m "
jvm_opts_gc="-verbose:gc -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:G1HeapRegionSize=16m -XX:G1ReservePercent=15 -XX:InitiatingHeapOccupancyPercent=45 -XX:MaxTenuringThreshold=7 -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=368m -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+UnlockExperimentalVMOptions -XX:G1LogLevel=finest -XX:+PrintHeapAtGC -XX:+HeapDumpOnOutOfMemoryError -XX:-OmitStackTraceInFastThrow  -Xloggc:/home/<USER>/yanxuan-query-parser/gc.log "
consul_service_port=8080
consul_service_tags=online
consul_health_url="/i/health"
