java_home=/home/<USER>/packages/jdk1.8.0_91
jvm_opts_property="-Dspring.profiles.active=release -Dserver.port=8083"
jvm_opts_base="-cp . -ea -Xms2048m -Xmx2048m "
jvm_opts_gc="-verbose:gc -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:G1HeapRegionSize=16m -XX:G1ReservePercent=15 -XX:InitiatingHeapOccupancyPercent=45 -XX:MaxTenuringThreshold=7 -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=368m -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+UnlockExperimentalVMOptions -XX:G1LogLevel=finest -XX:+PrintHeapAtGC -XX:+HeapDumpOnOutOfMemoryError -XX:-OmitStackTraceInFastThrow  -Xloggc:/home/<USER>/yanxuan-intelli-bot/gc.log "
consul_service_port=8083
consul_service_tags=release
consul_health_url="/i/health"
