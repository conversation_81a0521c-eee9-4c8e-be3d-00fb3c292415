package com.netease.yx.bot.common.util;

import org.apache.commons.lang3.SerializationUtils;

import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @class Utils
 * @description TODO
 * @date 2018/12/28 下午4:08
 **/
public class Utils {
    /**
     * 用于格式化Date对象
     */
    private static final SimpleDateFormat ALL_FORMAT;
    private static final SimpleDateFormat DATE_FORMAT;

    static {

        ALL_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    }


    /**
     * 从首字母大写驼峰命名，变成首字母小写驼峰命名
     *
     * @param text
     * @return
     */
    public static String upperCamelTolowerCamel(String text) {
        if (text == null || text.length() == 0) {
            return text;
        }
        if (text.length() == 1) {
            return text.toLowerCase();
        } else {
            StringBuilder stringBuilder = new StringBuilder(text);
            stringBuilder.replace(0, 1, stringBuilder.substring(0, 1).toLowerCase());
            return stringBuilder.toString();
        }
    }

    /**
     * 获取一个range的int
     *
     * @param start
     * @param end
     * @return
     */
    public static List<Integer> getRangeIntList(int start, int end) {
        List<Integer> integers = new ArrayList<>();
        for (int i = start; i < end; i++) {
            integers.add(i);
        }
        return integers;
    }

    /**
     * 获取两个Date之间时间的差距
     *
     * @param endDate
     * @param nowDate
     * @return
     */
    public static String getDatePoor(Date endDate, Date nowDate) {

        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = Math.abs(endDate.getTime() - nowDate.getTime());
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 获取两个Date之间小时的差距
     *
     * @param endDate
     * @param nowDate
     * @return
     */
    public static double getDateHourPoor(Date endDate, Date nowDate) {
        long nh = 1000 * 60 * 60;
        long diff = Math.abs(endDate.getTime() - nowDate.getTime());
        return diff / nh;
    }

    /**
     * 将一串string 进行hash
     *
     * @param strings
     * @return
     */
    public static String hash(List<String> strings) {
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return String.join("_", strings);
        }

        for (String str : strings) {
            md5.update(str.getBytes());
        }

        BigInteger bigInt = new BigInteger(1, md5.digest());
        return bigInt.toString(16);
    }

    /**
     * 将一个object 进行hash
     *
     * @param object
     * @return
     */
    public static String hash(Object object) {
        String string = IoUtils.toJsonString(object, false);

        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return string;
        }

        md5.update(string.getBytes());

        BigInteger bigInt = new BigInteger(1, md5.digest());
        return bigInt.toString(16);
    }

    /**
     * 采用对象的序列化完成对象的深克隆
     *
     * @param obj 待克隆的对象
     * @return
     * @autor:chenssy
     * @date:2014年8月9日
     */
    @SuppressWarnings("unchecked")
    public static <T extends Serializable> T cloneObject(T obj) {
        return SerializationUtils.clone(obj);
    }

    /**
     * 利用序列化完成集合的深克隆
     *
     * @param collection 待克隆的集合
     * @return
     * @throws ClassNotFoundException
     * @throws IOException
     * @autor:chenssy
     * @date:2014年8月9日
     */
    @SuppressWarnings("unchecked")
    public static <T> Collection<T> cloneCollection(Collection<T> collection) {
        if (collection == null) {
            return null;
        }

        Collection<T> dest = null;
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(collection);
            out.close();

            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream in = new ObjectInputStream(byteIn);
            dest = (Collection<T>) in.readObject();
            in.close();
        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }

        return dest;
    }

    public static boolean doubleEqualZero(double num) {
        BigDecimal data1 = new BigDecimal(num);
        BigDecimal data2 = new BigDecimal(0);
        return data1.compareTo(data2) == 0;
    }

//    public static String getYesterDay() {
//        return getNDayAgo(-1);
//    }

    public static String getNDayAgo(int delta) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, delta);//-1.昨天时间 0.当前时间 1.明天时间 *以此类推
        return sdf.format(c.getTime());
    }

    public static String second2Date(long seconds) {
        return DATE_FORMAT.format(new Date(seconds * 1000));
    }

    public static String getYesterday() {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, -24);
        return dateFormat.format(calendar.getTime());
    }

    public static void main(String[] args) throws IOException {
        String time = getYesterday();
        System.out.println(time);
    }
}

