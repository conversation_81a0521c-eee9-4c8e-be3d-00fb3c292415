/**
 * @(#)SucResponse.java, 2020/4/9.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.common.util.web;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SucResp<T> extends BaseResp {
    private T data;

    public SucResp(T data) {
        super(HttpStatus.OK.value());
        this.data = data;
    }
}