package com.netease.yx.bot.common.util.preprocess;

import com.github.houbb.segment.support.segment.result.impl.SegmentResultHandlers;
import com.github.houbb.segment.util.SegmentHelper;
import com.hankcs.hanlp.HanLP;
import com.netease.yx.bot.common.util.IoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class Clean {
    private static final Pattern PATTERN_PUNC = Pattern.compile("[\\p{P}+~$`^=|<>～｀＄＾＋＝｜＜＞￥× 。\\.？\\?，,\\t\\n]");
    private static final Pattern PATTERN_DIGIT = Pattern.compile("\\d+");
    private static final String DIGIT_TOKEN = "digit";
    private Set<String> stopWords;

    @Value("${dict.stop_words}")
    private String stopWordsPath;

    public static boolean isEmojiCharacter(char codePoint) {
        return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA)
                || (codePoint == 0xD)
                || ((codePoint >= 0x20) && (codePoint <= 0xD7FF))
                || ((codePoint >= 0xE000) && (codePoint <= 0xFFFD));
    }

    public static String removeEmoji(String sen) {
        StringBuilder newSen = new StringBuilder(sen.length());
        for (int i = 0; i < sen.length(); i++) {
            char senPoint = sen.charAt(i);
            if (isEmojiCharacter(senPoint)) {
                newSen.append(senPoint);
            }
        }
        return newSen.toString();
    }

    public static String toDBC(String input) {
        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    public static String toSimple(String input) {
        return HanLP.convertToSimplifiedChinese(input);
    }

    public static String removePunc(String input) {
        return PATTERN_PUNC.matcher(input).replaceAll("");
    }

    public static String toLower(String input) {
        return input.toLowerCase();
    }

    public static String replaceDigit(String input) {
        return PATTERN_DIGIT.matcher(input).replaceAll(DIGIT_TOKEN);
    }

    @PostConstruct
    private void init() {
        try {
            log.info("Preprocess Clean stopword path {}", stopWordsPath);
            stopWords = IoUtils.getLineSetFromFile(stopWordsPath);
            log.info("stopwords :{}", stopWords.size());
        } catch (IOException e) {
            stopWords = new HashSet<>();
            log.error("load TextProcessor4BertMatch error", e);
        }
    }

    public List<String> segment(String sen) {
        return SegmentHelper.segment(sen, SegmentResultHandlers.word());
    }

    public String removeStopWords(String sen) {
        List<String> segmentResult = segment(sen);
        return segmentResult.stream().filter(x -> !stopWords.contains(x)).collect(Collectors.joining());
    }
}
