package com.netease.yx.bot.common.util;


import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.lang3.ObjectUtils;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $Id: GuavaCacheUtil, v0.1 2020-11-09 10:59 AM <EMAIL>
 */
public class GuavaCacheUtil {
    /**
     * 暂时设为10000，缓存失效时间设置为60*60*6
     */
    public static LoadingCache<String, String> strCache = CacheBuilder
            .newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(60 * 60 * 48, TimeUnit.SECONDS)
            .build(
                    new CacheLoader<String, String>() {
                        @Override
                        public String load(String s) throws Exception {
                            return null;
                        }
                    }
            );

    /**
     * 将值存入缓存
     *
     * @param key
     * @param val
     */
    public static boolean setStr(String key, String val) {
        strCache.put(key, val);
        return true;
    }

    /**
     * 从缓存中取值，不调用load
     *
     * @param key
     * @return
     */
    public static String getStr(String key) {
        return strCache.getIfPresent(key);
    }

    public static boolean deleteStr(String key) {
        return ObjectUtils.isNotEmpty(strCache.asMap().remove(key));
    }

}
