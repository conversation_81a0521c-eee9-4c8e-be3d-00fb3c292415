package com.netease.yx.bot.common.util.qianRpc;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;

/**
 * <AUTHOR> on 2019-04-15.
 */
@Configuration
@PropertySources(@PropertySource(value = {
        "classpath:config/${spring.profiles.active}/qianRpc.properties"
}))
public class QianRpcBaseConfig {
}
