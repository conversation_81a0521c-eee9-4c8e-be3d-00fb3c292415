package com.netease.yx.bot.common.util.preprocess;

import com.netease.yx.bot.core.model.entity.preprocess.NgramText;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Service
@Slf4j
public class Ngram {

    private final static int maxLength = 32;
    private final static String pad = "[PAD]";
    private final static String sep = "[SEP]";
    private final static String emptyQuery = String.join(sep, Collections.nCopies(maxLength, pad));
    private Pattern digitPattern = Pattern.compile("[0-9]+");
    private Pattern enPattern = Pattern.compile("[a-zA-Z]+");
    private Pattern nonDigitEnPattern = Pattern.compile("[^0-9a-zA-Z]+");

    public NgramText generate(String text) {
        List<String> triGram = new ArrayList<>();
        List<String> uniGram = new ArrayList<>();
        List<String> biGram = new ArrayList<>();

        // digit
        Matcher digitMatch = digitPattern.matcher(text);
        while (digitMatch.find()) {
            String digit = "#" + text.substring(digitMatch.start(), digitMatch.end()) + "#";
            List<String> digitTriGram = reproduceTriGram(digit);
            if (digitTriGram.size() > 0) {
                triGram.addAll(digitTriGram);
            }
        }

        // en
        Matcher enMatch = enPattern.matcher(text);
        while (enMatch.find()) {
            String en = "#" + text.substring(enMatch.start(), enMatch.end()) + "#";
            List<String> enTriGram = reproduceTriGram(en);
            if (enTriGram.size() > 0) {
                triGram.addAll(enTriGram);
            }
        }

        // zh \ punct
        Matcher textMatch = nonDigitEnPattern.matcher(text);
        while (textMatch.find()) {
            StringBuilder ch = new StringBuilder(text.substring(textMatch.start(), textMatch.end()));
            uniGram.addAll(new ArrayList<>(Arrays.asList(ch.toString().split(""))));
            ch = new StringBuilder("^" + ch + "$");
            List<String> chBiGram = reproduceBiGram(ch.toString());
            if (chBiGram.size() > 0) {
                biGram.addAll(chBiGram);
            }

        }


        List<String> finalGram = uniGram;
        finalGram.addAll(biGram);
        finalGram.addAll(triGram);
        Integer length = finalGram.size();

        if (finalGram.size() < maxLength) {
            finalGram.addAll(Collections.nCopies(maxLength - finalGram.size(), pad));
        } else {
            finalGram = finalGram.subList(0, maxLength);
        }

        if (finalGram.size() != maxLength) {
            return new NgramText(emptyQuery, 0);
        }

        return new NgramText(String.join(sep, finalGram), length);

    }

    private List<String> reproduceTriGram(String text) {
        List<String> triGram = new ArrayList<>();

        for (int i = 0; i < text.length() - 2; i++) {
            triGram.add(text.substring(i, i + 3));
        }

        return triGram;
    }

    private List<String> reproduceBiGram(String text) {
        List<String> biGram = new ArrayList<>();

        for (int i = 0; i < text.length() - 1; i++) {
            biGram.add(text.substring(i, i + 2));
        }

        return biGram;
    }
}
