package com.netease.yx.bot.common.util;

import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.SocketConfig;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


@Component
public class HttpPool {
    public static int MAX_CONN_NUM = 90;
    public static int DOMAIN_NUMBER = 3; // add get, two interfaces
    public static int TIME_OUT = 5000; //MS
    public static long DEFAULT_KEEP_ALIVE_MS = 5000;
    public static int CONN_REQ_TIMEOUT = 1000;
    public static int STALE_VLD_SECONDS = 5;
    private static HttpPool single = null;
    private static Logger LOG = LoggerFactory.getLogger(HttpPool.class);
    /**
     * HTTP链接pool
     */
    private static PoolingHttpClientConnectionManager AD_POOL_CONN_MNGR;
    private static ScheduledExecutorService SCHDLD_EXECUTOR;
    private static CloseableHttpClient httpClient;

    static {
        /** 配置HTTP链接pool */
        AD_POOL_CONN_MNGR = new PoolingHttpClientConnectionManager();
        AD_POOL_CONN_MNGR.setMaxTotal(MAX_CONN_NUM);
        AD_POOL_CONN_MNGR.setDefaultMaxPerRoute(MAX_CONN_NUM / DOMAIN_NUMBER);
        SocketConfig socketConfig = SocketConfig.custom().setSoTimeout(TIME_OUT)
                .setSoKeepAlive(true).setTcpNoDelay(true).setSoReuseAddress(true).build();
        AD_POOL_CONN_MNGR.setDefaultSocketConfig(socketConfig);
        /**
         * 获取keepAlive策略；优先使用Response Header里的数据，否则用默认时间
         */
        ConnectionKeepAliveStrategy keepAliveStrategy = new ConnectionKeepAliveStrategy() {
            public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
                HeaderElementIterator it = new BasicHeaderElementIterator(response.headerIterator(HTTP.CONN_KEEP_ALIVE));
                while (it.hasNext()) {
                    HeaderElement he = it.nextElement();
                    String param = he.getName();
                    String value = he.getValue();
                    if (value != null && param.equalsIgnoreCase("timeout")) {
                        try {
                            return Long.parseLong(value) * 1000;
                        } catch (NumberFormatException ignore) {
                            LOG.warn("Keep alive time data error, param={}, value={}", new Object[]{param, value});
                        }
                    }
                }
                return DEFAULT_KEEP_ALIVE_MS;
            }
        };
        RequestConfig reqConfig = RequestConfig.custom()
                .setSocketTimeout(TIME_OUT).setConnectTimeout(TIME_OUT)
                .setConnectionRequestTimeout(CONN_REQ_TIMEOUT)
                .setStaleConnectionCheckEnabled(true).build();
        httpClient = HttpClients.custom().setConnectionManager(AD_POOL_CONN_MNGR)
                .setDefaultRequestConfig(reqConfig).setKeepAliveStrategy(keepAliveStrategy).build();
        SCHDLD_EXECUTOR = Executors.newScheduledThreadPool(1);
        HttpConnStaleVldRunnable vldStaleRunnable = new HttpConnStaleVldRunnable(AD_POOL_CONN_MNGR);
        SCHDLD_EXECUTOR.scheduleWithFixedDelay(vldStaleRunnable, STALE_VLD_SECONDS, STALE_VLD_SECONDS, TimeUnit.SECONDS);
    }

    private HttpPool() {

    }

    public static HttpPool getInstance() {
        if (single == null)
            createSingle();
        return single;
    }

    private static synchronized void createSingle() {
        if (single == null)
            single = new HttpPool();
    }

    /**
     * 获取广告信息，如出错，会返回null, 记录日志。成功也会记录日志。
     *
     * @param theURL
     * @param oriParams
     * @return
     */
    public String getHttpResult(String theURL, Map<String, String> oriParams) {
        String getUrl = HttpUtil.getGetRequestUrl(theURL, oriParams);
        LOG.info("Send request url = {}", new Object[]{getUrl});
        HttpPost httpPost = new HttpPost(theURL);
        ArrayList<NameValuePair> params = new ArrayList<NameValuePair>();
        for (String paramName : oriParams.keySet()) {
            params.add(new BasicNameValuePair(paramName, oriParams.get(paramName)));
        }

        String ret = null;
        InputStream in = null;
        CloseableHttpResponse response = null;
        HttpEntity entity = null;
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
            response = httpClient.execute(httpPost);
            StatusLine status = response.getStatusLine();
            if (status.getStatusCode() < 200 || status.getStatusCode() >= 300) {
                LOG.error("AD HTTP error:{}, httpGetUrl={}",
                        new Object[]{status, getUrl});
                return null;//"{\"error\":\"get Ad from AD-backend fail, illegal response code.\"}";
            }
            entity = response.getEntity();
            in = entity.getContent();

            //BufferedReader bd =new BufferedReader(new InputStreamReader(in, "UTF-8"));
            byte[] allBytes = HttpUtil.readBytes(in);
            ret = new String(allBytes, "UTF-8");
            LOG.debug("GET ad_info success. getUrl={}", new Object[]{getUrl});
            return ret;

        } catch (Exception e) {
            LOG.error("error in http. getUrl={}", new Object[]{getUrl});
            LOG.error("", e);
            return null;//"{\"error\":\"get Ad from AD-backend fail, IOException.\"}";
        } finally {
            httpPost.releaseConnection();
            EntityUtils.consumeQuietly(entity);
            try {
                response.close();
            } catch (Exception e) {
                LOG.debug("error in close response.", e);
            }
        }

    }

    private static class HttpConnStaleVldRunnable implements Runnable {
        private final PoolingHttpClientConnectionManager cm;

        public HttpConnStaleVldRunnable(PoolingHttpClientConnectionManager cm) {
            this.cm = cm;
        }

        @Override
        public void run() {
            cm.closeExpiredConnections();
            cm.closeIdleConnections(5, TimeUnit.SECONDS);
        }
    }
}
