/**
 * @(#)EsUtil.java, 2020/3/25.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.common.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.netease.search.client.common.Result;
import com.netease.search.client.vo.AppInfo;
import com.netease.search.search.ESSearchRequest;
import com.netease.search.search.ESSearchResult;
import com.netease.search.search.util.SearchResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.util.List;

/**
 * <AUTHOR> @ corp.netease.com)
 */
@Slf4j
public class EsUtil {
    public static SearchResponse search(String searchUrl, String appName, String indexName, QueryBuilder queryBuilder, int maxSize) {
        try {
            ESSearchRequest esSearchRequest = new ESSearchRequest();
            AppInfo appInfo = new AppInfo(appName);
            esSearchRequest.setAppInfo(appInfo);
            //传入indice对象
            esSearchRequest.setIndexName(indexName);
            //构建searchBuilder对象
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            searchSourceBuilder.size(maxSize).query(queryBuilder);

            log.warn("query serial:{}", IoUtils.toJsonString(searchSourceBuilder.toString()));
            esSearchRequest.setSearchRequest(searchSourceBuilder.toString());
            log.warn("searchResquest serial:{}", IoUtils.toJsonString(esSearchRequest));

            //构建HTTP参数对象
            NameValuePair nameValuePair = new BasicNameValuePair("data", IoUtils.toJsonString(esSearchRequest));
            List<NameValuePair> parameters = Lists.newArrayList(nameValuePair);
            String response = HttpUtils.executePost(searchUrl, parameters);
            //解析HTTP结果
            Result<ESSearchResult> httpResult = IoUtils.parseJson(response, new TypeReference<Result<ESSearchResult>>() {
            });
            if (!httpResult.isSuccess()) {
                log.error("get index error，param result msg ");
                throw new RuntimeException(httpResult.getErrorMsg());
            }
            //获取搜索结果
            ESSearchResult esSearchResult = httpResult.getModel();
            //转换为通用对象
            return SearchResponseUtil.getSearchResponse(esSearchResult);
        } catch (Exception e) {
            log.error("search error");
            throw new RuntimeException(e);
        }
    }

    public static SearchResponse search(String searchUrl, String appName, String indexName, SearchSourceBuilder searchSourceBuilder) {
        try {
            ESSearchRequest esSearchRequest = new ESSearchRequest();
            AppInfo appInfo = new AppInfo(appName);
            esSearchRequest.setAppInfo(appInfo);
            //传入indice对象
            esSearchRequest.setIndexName(indexName);

            esSearchRequest.setSearchRequest(searchSourceBuilder.toString());
            log.warn("searchResquest serial:{}", IoUtils.toJsonString(esSearchRequest));

            //构建HTTP参数对象
            NameValuePair nameValuePair = new BasicNameValuePair("data", IoUtils.toJsonString(esSearchRequest).replace(",\\\"seq_no_primary_term\\\":false", ""));
            List<NameValuePair> parameters = Lists.newArrayList(nameValuePair);
            String response = HttpUtils.executePost(searchUrl, parameters);
            log.info(response);
            //解析HTTP结果
            Result<ESSearchResult> httpResult = IoUtils.parseJson(response, new TypeReference<Result<ESSearchResult>>() {
            });
            if (!httpResult.isSuccess()) {
                log.error("get index error，param result msg {}", httpResult.getErrorMsg());
                throw new RuntimeException(httpResult.getErrorMsg());
            }
            //获取搜索结果
            ESSearchResult esSearchResult = httpResult.getModel();
            //转换为通用对象
            return SearchResponseUtil.getSearchResponse(esSearchResult);
        } catch (Exception e) {
            log.error("search error");
            throw new RuntimeException(e);
        }
    }
}