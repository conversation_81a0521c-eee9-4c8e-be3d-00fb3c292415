package com.netease.yx.bot.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * http操作类
 *
 * <AUTHOR>
 */
public class HttpUtils {
    private static final Logger LOG = LoggerFactory.getLogger(HttpUtils.class);
    private static final int TIME_OUT = 6000; //超时设置.默认为2s
    private static final int MAX_POOL_SIZE = 200; //最大连接数
    private static final int MAX_PER_ROUTE = 100; //每个路由最大连接数(最大连接数/host个数)
    private static final int STALE_VLD_INTERVAL = 15; //检测无效连接的间隔。默认为15s
    private static final int CON_STALE_TIME = 30; //闲置线程的最大时间。默认为30s
    private static final int KEEP_ALIVE_TIME = 60; //长连接保持时间。默认为60s
    private static final String DEFAULT_CHARSET = "utf-8";
    /**
     * Http 连接池,复用连接，减少握手开销
     */
    private static PoolingHttpClientConnectionManager connectionManager;
    /**
     * 定时执行stale验证的executor
     */
    private static ScheduledExecutorService executor;

    static {
        getHttpClient();
    }

    /**
     * 单例模式获取HttpClient。可以提供给外部使用
     */
    public static CloseableHttpClient getHttpClient() {
        return HttpClientHolder.httpClient;
    }

    private static CloseableHttpClient initHttpConnPool() {
        LOG.info("Start to init http connection pool.");

        connectionManager = new PoolingHttpClientConnectionManager();

        // 设置Host缓存连接数
        connectionManager.setMaxTotal(MAX_POOL_SIZE);
        connectionManager.setDefaultMaxPerRoute(MAX_PER_ROUTE);

        // HttClient 生成和配置
        RequestConfig reqConfig = gnrtRequestConfig();
        ConnectionKeepAliveStrategy keepAliveStrategy = gnrtConnKeepAliveStrategy();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(reqConfig)
                .setKeepAliveStrategy(keepAliveStrategy)
                .build();

        // 周期性验证连接
        executor = Executors.newScheduledThreadPool(1);
        HttpConnStaleVldRunnable vldStaleRunnable = new HttpConnStaleVldRunnable(
                connectionManager);
        executor.scheduleWithFixedDelay(vldStaleRunnable, STALE_VLD_INTERVAL, STALE_VLD_INTERVAL, TimeUnit.SECONDS);

        LOG.info("http connection pool init success.");

        return httpClient;
    }

    /**
     * 生成Req请求配置对象
     */
    private static RequestConfig gnrtRequestConfig() {
        RequestConfig defaultRequestConfig = RequestConfig.custom()
                .setSocketTimeout(TIME_OUT).setConnectTimeout(TIME_OUT)
                .setConnectionRequestTimeout(TIME_OUT).build();

        return defaultRequestConfig;
    }

    /**
     * 获取keepAlive策略；优先使用Response Header里的数据，否则用默认时间
     */
    private static ConnectionKeepAliveStrategy gnrtConnKeepAliveStrategy() {
        ConnectionKeepAliveStrategy myStrategy = new ConnectionKeepAliveStrategy() {
            @Override
            public long getKeepAliveDuration(HttpResponse response,
                                             HttpContext context) {
                // Honor 'keep-alive' header
                HeaderElementIterator it = new BasicHeaderElementIterator(
                        response.headerIterator(HTTP.CONN_KEEP_ALIVE));
                while (it.hasNext()) {
                    HeaderElement he = it.nextElement();
                    String param = he.getName();
                    String value = he.getValue();

                    if (value != null && param.equalsIgnoreCase("timeout")) {
                        try {
                            return Long.parseLong(value) * 1000;
                        } catch (NumberFormatException ignore) {
                            LOG.warn(
                                    "Keep alive time data error, param={}, value={}",
                                    new Object[]{param, value});
                        }
                    }
                }

                // Server端未定义，使用默认时间
                return KEEP_ALIVE_TIME * 1000;
            }
        };
        return myStrategy;
    }

    /**
     * 使用默认编码执行POST请求并将响应实体以字符串返回
     */
    public static String executePost(String url, List<NameValuePair> parameters)
            throws IOException {
        return executePost(url, parameters, null);
    }

    /**
     * 执行POST请求并将响应实体以字符串返回
     */
    public static String executePost(String url, List<NameValuePair> parameters,
                                     String charset) throws IOException {
        charset = charset != null ? charset : DEFAULT_CHARSET;
        HttpPost postRequest = makePostRequest(url, parameters, charset, null);
        String result = requestAndParse(postRequest, charset);
        return result;
    }

    /**
     * 使用默认的编码执行无参数的GET请求并将响应实体以字符串返回
     */
    public static String executeGet(String url) throws IOException {
        return executeGet(url, null);
    }

    /**
     * 使用默认的编码执行GET请求并将响应实体以字符串返回
     */
    public static String executeGet(String url, List<NameValuePair> parameters)
            throws IOException {
        return executeGet(url, parameters, null);
    }

    /**
     * 执行GET请求并将响应实体以字符串返回
     */
    public static String executeGet(String url, List<NameValuePair> parameters,
                                    String charset) throws IOException {
        charset = charset != null ? charset : DEFAULT_CHARSET;
        HttpGet getRequest = makeGetRequest(url, parameters, charset);
        String result = requestAndParse(getRequest, charset);

        return result;
    }

    /**
     * 根据给定的url、参数和编码方式构建一个GET请求
     */
    private static HttpGet makeGetRequest(String url, List<NameValuePair> parameters,
                                          String charset) {
        String queryString = null;
        if (parameters != null && parameters.size() > 0) {
            charset = charset != null ? charset : DEFAULT_CHARSET;
            queryString = URLEncodedUtils.format(parameters, charset);
        }
        String requestUrl = url;
        if (queryString != null) {
            if (!url.contains("?")) {
                requestUrl += "?" + queryString;
            } else {
                requestUrl += "&" + queryString;
            }
        }

        return getHttpGet(requestUrl);
    }

    /**
     * 根据给定的url、参数和编码方式构建一个POST请求
     */
    private static HttpPost makePostRequest(String url, List<NameValuePair> parameters,
                                            String charset) throws IOException {
        return makePostRequest(url, parameters, charset, null);
    }

    /**
     * 根据给定的url、参数和编码方式构建一个POST请求
     */
    private static HttpPost makePostRequest(String url, List<NameValuePair> parameters,
                                            String charset, Map<String, String> headers) throws IOException {
        HttpPost post = getHttpPost(url);
        if (parameters != null && parameters.size() > 0) {
            charset = charset != null ? charset : DEFAULT_CHARSET;
            UrlEncodedFormEntity urfe = new UrlEncodedFormEntity(parameters, charset);
            post.setEntity(urfe);
        }

        if (headers != null) {
            for (Map.Entry<String, String> e : headers.entrySet()) {
                post.addHeader(e.getKey(), e.getValue());
            }
        }

        return post;
    }

    /**
     * 生成POST请求，使用配置的参数
     */
    private static HttpPost getHttpPost(String url) {
        return getHttpPost(url, TIME_OUT);
    }

    private static HttpPost getHttpPost(String url, int timeout) {
        HttpPost postMethod = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).setRedirectsEnabled(false).build();
        postMethod.setConfig(requestConfig);
        return postMethod;
    }

    /**
     * 生成GET请求，使用配置的参数
     */
    private static HttpGet getHttpGet(String url) {
        HttpGet getMethod = new HttpGet(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(TIME_OUT).setConnectionRequestTimeout(TIME_OUT)
                .setSocketTimeout(TIME_OUT).setRedirectsEnabled(false).build();
        getMethod.setConfig(requestConfig);
        return getMethod;
    }

    /**
     * 执行请求并获取响应
     * 这里也可以用CloseableHttpResponse，执行完关闭连接。但是在连接池的使用下，不需要关闭。
     */
    private static String requestAndParse(HttpUriRequest httpRequest, String charset)
            throws IOException {
        HttpResponse httpResponse = HttpClientHolder.httpClient.execute(httpRequest);
        return getResponseContentStr(httpResponse, charset);
    }

    /**
     * 使用指定编码将响应实体转为字符串
     * 响应实体必须关闭，如果使用EntityUtils会自动关闭。
     * 使用EntityUtils.toString前提是知道返回体长度，否则存在会存在缓存区溢出风险，因为toString默认长度是int最大值
     */
    private static String getResponseContentStr(HttpResponse httpResponse,
                                                String charset) throws IOException {
        HttpEntity entity = getResponseContentEntity(httpResponse);
        if (null == entity) {
            return null;
        }
        return EntityUtils.toString(entity, DEFAULT_CHARSET);
    }

    /**
     * 获取响应实体
     */
    private static HttpEntity getResponseContentEntity(HttpResponse httpResponse)
            throws IOException {
        StatusLine statusLine = httpResponse.getStatusLine();
        if (null == statusLine) {
            throw new IOException("status not specified");
        }
        int statusCode = statusLine.getStatusCode();
        if (statusCode < 200 || statusCode > 299) {
            EntityUtils.consumeQuietly(httpResponse.getEntity());
            throw new IOException("status code: " + statusCode);
        }
        return httpResponse.getEntity();
    }

    /**
     * 预热连接池
     */
    public static void warmupHttpConnPool() {
        getHttpClient();
        LOG.info("Warmup http connection pool success");
    }

    /**
     * 关闭连接池
     */
    public static void shutdown() {
        try {
            executor.shutdownNow();
            HttpClientHolder.httpClient.close();
            connectionManager.close();

            LOG.info("Shutdown http connection pool success.");
        } catch (IOException e) {
            LOG.warn("Shutdown http connection pool error.", e);
        }
    }

    /**
     * 执行POST请求并将响应实体以字符串返回
     */
    public static String executePost(String url, String stringEntity, String charset)
            throws IOException {

        return executePost(url, stringEntity, charset, null);
    }

    /**
     * 执行POST请求并将响应实体以字符串返回
     */
    public static String executePost(String url, String stringEntity, String charset, Map<String, String> headers)
            throws IOException {

        return executePost(url, stringEntity, charset, headers, TIME_OUT);
    }

    public static String executePost(String url, String stringEntity, String charset, Map<String, String> headers, int timeout)
            throws IOException {
        charset = charset != null ? charset : DEFAULT_CHARSET;
        HttpPost postRequest = makePostRequest(url, stringEntity, charset, headers, timeout);

        String result = requestAndParse(postRequest, charset);

        return result;
    }

    /**
     * 根据给定的url、参数和编码方式构建一个POST请求
     */
    private static HttpPost makePostRequest(String url, String stringEntity,
                                            String charset, Map<String, String> headers) throws IOException {
        return makePostRequest(url, stringEntity, charset, headers, TIME_OUT);
    }

    private static HttpPost makePostRequest(String url, String stringEntity,
                                            String charset, Map<String, String> headers, int timeout) throws IOException {
        HttpPost post = getHttpPost(url, timeout);
        if (!StringUtils.isEmpty(stringEntity)) {
            //charset = charset != null ? charset : DEFAULT_CHARSET;
            StringEntity se = new StringEntity(stringEntity,
                    ContentType.APPLICATION_JSON);
            post.setEntity(se);

        }

        if (headers != null) {
            for (Map.Entry<String, String> e : headers.entrySet()) {
                post.addHeader(e.getKey(), e.getValue());
            }
        }

        return post;
    }

    /**
     * 使用默认编码执行执行无参数的POST请求并将响应实体以字符串返回
     */
    public String executePost(String url) throws IOException {
        return executePost(url, null);
    }

    /**
     * 执行GET请求并将响应实体以字符串返回
     */
    public HttpResponse executeGetHttpResponse(String url,
                                               List<NameValuePair> parameters, String charset) throws IOException {
        charset = charset != null ? charset : DEFAULT_CHARSET;
        HttpGet getRequest = makeGetRequest(url, parameters, charset);

        return HttpClientHolder.httpClient.execute(getRequest);
    }

    /**
     * 执行通过url获得相应
     */
    public HttpResponse getHttpResponse(String url)
            throws IOException {
        HttpPost httpPost = getHttpPost(url);
        return HttpClientHolder.httpClient.execute(httpPost);
    }

    private static class HttpClientHolder {
        static final CloseableHttpClient httpClient = initHttpConnPool();
    }

    /**
     * 周期性的检查Http连接
     */
    private static class HttpConnStaleVldRunnable implements Runnable {

        private final PoolingHttpClientConnectionManager cm;

        public HttpConnStaleVldRunnable(PoolingHttpClientConnectionManager cm) {
            this.cm = cm;
        }

        @Override
        public void run() {
            cm.closeExpiredConnections();  //检测过期线程
            cm.closeIdleConnections(CON_STALE_TIME, TimeUnit.SECONDS); //检测闲置线程,超过设置时间就关闭掉
            LOG.debug("Validate stale http connetions.");
        }

    }
}
