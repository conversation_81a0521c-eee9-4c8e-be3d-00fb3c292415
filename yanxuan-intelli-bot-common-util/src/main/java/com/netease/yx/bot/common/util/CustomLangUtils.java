/**
 * @(#)Utils.java, 2020/4/9.
 * <p/>
 * Copyright 2020 Netease, Inc. All rights reserved.
 * NETEASE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.netease.yx.bot.common.util;

import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.nlpcn.commons.lang.standardization.WordUtil;

import java.util.Arrays;

/**
 * <AUTHOR> @ corp.netease.com)
 */
public class CustomLangUtils {

    public static final char TAB = '\t';
    /**
     * ASCII表中可见字符从!开始，偏移位值为33(Decimal)
     * 半角!
     */
    private static final char DBC_CHAR_START = 33;
    /**
     * ASCII表中可见字符到~结束，偏移位值为126(Decimal)
     * 半角~
     */
    private static final char DBC_CHAR_END = 126;
    /**
     * 全角对应于ASCII表的可见字符从！开始，偏移值为65281
     * 全角！
     */
    private static final char SBC_CHAR_START = 65281;
    /**
     * 全角对应于ASCII表的可见字符到～结束，偏移值为65374
     * 全角～
     */
    private static final char SBC_CHAR_END = 65374;
    /**
     * ASCII表中除空格外的可见字符与对应的全角字符的相对偏移
     * 全角半角转换间隔
     */
    private static final int CONVERT_STEP = 65248;
    /**
     * 全角空格的值，它没有遵从与ASCII的相对偏移，必须单独处理
     * 全角空格 12288
     */
    private static final char SBC_SPACE = 12288;
    /**
     * 半角空格的值，在ASCII中为32(Decimal)
     * 半角空格
     */
    private static final char DBC_SPACE = ' ';

    public static boolean isNumStr(String string) {
        return NumberUtils.isParsable(string);
    }

    /**
     * 是纯英文字母
     *
     * @param string
     * @return
     */
    public static boolean isAlphaStr(String string) {
        if (StringUtils.isEmpty(string)) {
            return false;
        } else {
            int sz = string.length();

            for (int i = 0; i < sz; ++i) {
                if (!CharUtils.isAsciiAlpha(string.charAt(i))) {
                    return false;
                }
            }

            return true;
        }
    }

    // 有至少一个中文字
    public static boolean isChiStr(String string) {
        if (StringUtils.isEmpty(string)) {
            return false;
        } else {
            int sz = string.length();

            for (int i = 0; i < sz; ++i) {
                if (isChiCh(string.charAt(i))) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isAllChiStr(String string) {
        if (StringUtils.isEmpty(string)) {
            return false;
        } else {
            int sz = string.length();

            for (int i = 0; i < sz; ++i) {
                if (!isChiCh(string.charAt(i))) {
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean isAllValidStr(String string) {
        if (StringUtils.isEmpty(string)) {
            return false;
        } else {
            int sz = string.length();

            for (int i = 0; i < sz; ++i) {
                char ch = string.charAt(i);
                if (!(Character.isLetter(ch) || Character.isDigit(ch) || isChiCh(ch))) {
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean isValidStr(String string) {
        if (StringUtils.isEmpty(string)) {
            return false;
        } else {
            int sz = string.length();

            for (int i = 0; i < sz; ++i) {
                char ch = string.charAt(i);
                if (Character.isLetter(ch) || Character.isDigit(ch) || isChiCh(ch)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isChiCh(char cp) {
        return ((cp >= 0x4E00 && cp <= 0x9FFF) ||
                (cp >= 0x3400 && cp <= 0x4DBF) ||
                (cp >= 0x20000 && cp <= 0x2A6DF) ||
                (cp >= 0x2A700 && cp <= 0x2B73F) ||
                (cp >= 0x2B740 && cp <= 0x2B81F) ||
                (cp >= 0x2B820 && cp <= 0x2CEAF) ||
                (cp >= 0xF900 && cp <= 0xFAFF) ||
                (cp >= 0x2F800 && cp <= 0x2FA1F));
    }

    /**
     * <PRE>
     * 半角字符->全角字符转换
     * 只处理空格，!到˜之间的字符，忽略其他
     * </PRE>
     */
    private static String bj2qj(String src) {
        if (src == null) {
            return src;
        }
        StringBuilder buf = new StringBuilder(src.length());
        char[] ca = src.toCharArray();
        for (int i = 0; i < ca.length; i++) {
            if (ca[i] == DBC_SPACE) {
                // 如果是半角空格，直接用全角空格替代
                buf.append(SBC_SPACE);
            } else if ((ca[i] >= DBC_CHAR_START) && (ca[i] <= DBC_CHAR_END)) {
                // 字符是!到~之间的可见字符
                buf.append((char) (ca[i] + CONVERT_STEP));
            } else {
                // 不对空格以及ascii表中其他可见字符之外的字符做任何处理
                buf.append(ca[i]);
            }
        }
        return buf.toString();
    }

    /**
     * <PRE>
     * 全角字符->半角字符转换
     * 只处理全角的空格，全角！到全角～之间的字符，忽略其他
     * </PRE>
     */
    public static String qj2bj(String src) {
        if (src == null) {
            return src;
        }
        StringBuilder buf = new StringBuilder(src.length());
        char[] ca = src.toCharArray();
        for (int i = 0; i < src.length(); i++) {
            if (ca[i] >= SBC_CHAR_START && ca[i] <= SBC_CHAR_END) {
                // 如果位于全角！到全角～区间内
                buf.append((char) (ca[i] - CONVERT_STEP));
            } else if (ca[i] == SBC_SPACE) {
                // 如果是全角空格
                buf.append(DBC_SPACE);
            } else {
                // 不处理全角空格，全角！到全角～区间外的字符
                buf.append(ca[i]);
            }
        }
        return buf.toString();
    }


    public static void main(String[] args) {
        WordUtil wordUtil = new WordUtil('1', 'A');
        System.out.println(wordUtil.str2Elements("123中国CHINA456你好!"));
        System.out.println(Arrays.toString(wordUtil.str2Chars("123中国CHINA456你好!")));
        System.out.println(wordUtil.str2Str("123中国CHINA456你好!"));

        System.out.println(StringUtils.trimToEmpty(" a,b ,c "));
        String s = "nihaoｈｋ　｜　　　ｎｉｈｅｈｅ　，。　７８　　７　";
        s = CustomLangUtils.qj2bj(s);
        System.out.println(s);
        System.out.println(CustomLangUtils.bj2qj(s));

        System.out.println(CustomLangUtils.isNumStr("xx"));
        System.out.println(CustomLangUtils.isNumStr("11"));
        System.out.println(CustomLangUtils.isAllValidStr("123中国CHINA456你好!"));
        System.out.println(CustomLangUtils.isAllValidStr("123中国CHINA456你好"));
        System.out.println(CustomLangUtils.isAllChiStr("123中国CHINA456你好"));
        System.out.println(CustomLangUtils.isAllChiStr("你好"));

    }
}