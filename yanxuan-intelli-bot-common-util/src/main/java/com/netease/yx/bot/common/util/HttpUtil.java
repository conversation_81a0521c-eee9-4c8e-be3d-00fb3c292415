package com.netease.yx.bot.common.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HttpUtil {
    public static String readAsString(InputStream in, String charset) throws IOException {
        return new String(readBytes(in), charset);
    }

    public static String readAsString(InputStream in, Charset charset) throws IOException {
        return new String(readBytes(in), charset);
    }

    public static String readAsString(String filePath, String charset) throws IOException {
        InputStream in = new FileInputStream(filePath);
        return readAsString(in, charset);
    }

    public static byte[] readBytes(String filePath) throws Exception {
        InputStream is = new FileInputStream(filePath);
        return readBytes(is);
    }

    public static byte[] readBytes(InputStream in) throws IOException {
        BufferedInputStream bufin = new BufferedInputStream(in);
        int buffSize = 1024;
        ByteArrayOutputStream out = new ByteArrayOutputStream(buffSize);

        // System.out.println("Available bytes:" + in.available());

        byte[] temp = new byte[buffSize];
        int size = 0;
        while ((size = bufin.read(temp)) != -1) {
            out.write(temp, 0, size);
        }
        bufin.close();

        byte[] content = out.toByteArray();
        return content;
    }

    public static String getGetRequestUrl(String url, Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        sb.append(url);
        if (url.lastIndexOf("?") != (url.length() - 1))//�?后一个非问号
            sb.append("?");

        for (String key : params.keySet()) {
            String val = params.get(key);
            sb.append(key);
            sb.append("=");
            sb.append(val);
            sb.append("&");
        }

        String ret = sb.toString();

        String last = ret.substring(ret.length() - 1);

        if (last.equals("?") || last.equals("&"))
            ret = ret.substring(0, ret.length() - 1);

        return ret;

    }


    public static String getGetRequestUrl(String url, String... params) {
        List<String> arr = new ArrayList<String>();

        for (String str : params) {
            arr.add(str);
        }

        Map<String, String> map = new HashMap<String, String>();
        for (int i = 0; i < arr.size(); i += 2) {
            String key = arr.get(i);
            String val = arr.get(i + 1);
            map.put(key, val);
        }

        return getGetRequestUrl(url, map);
    }

    /**
     * 从url中取得返回内容，以charset编码
     *
     * @param url
     * @param charset
     * @return null, 失败；非null, 成功
     */
    public static String getContentByGet(String url, String charset) {
        String ret = "";
        URL httpUrl = null;
        ;
        HttpURLConnection conn = null;
        try {
            httpUrl = new URL(url);

            conn = (HttpURLConnection) httpUrl.openConnection();
            conn.setDoOutput(true);
            conn.setRequestMethod("GET");
            conn.setRequestProperty("User-agent", "Mozilla/4.0");

            conn.connect();
            int code = conn.getResponseCode();
            if (code == HttpURLConnection.HTTP_OK) {
                InputStream is = conn.getInputStream();
                byte[] bts = HttpUtil.readBytes(is);
                ret = new String(bts, charset);
            } else//连接广告后台端失�?
            {
                //TODO: 返回错误代码
                //log.error("get ad_info failed, url={}, responseCode={}", new Object[]{url, code});
                ret = null;
            }
        } catch (Exception e) {
            ret = null;
            //LOG.error("get ad_info error:url={}, error={}", new Object[]{url, e.getMessage()});
            //log.error("\"desc\":\"get ad_info error\",\"url\":\"{}\", \"errorMessage\":\"{}\"", new Object[]{url, e.getMessage()});
        } finally {
            if (conn != null)
                conn.disconnect();
        }

        return ret;
    }

    public static void main(String[] args) {
        String url = "www.163.com/do";

        String getUrl = getGetRequestUrl(url, "name", "shenyan", "age", "13");

        System.out.println(getUrl);

        getUrl = getGetRequestUrl(url);
        System.out.println(getUrl);


    }
}
