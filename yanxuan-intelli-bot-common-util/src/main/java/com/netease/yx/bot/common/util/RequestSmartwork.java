package com.netease.yx.bot.common.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class RequestSmartwork {

    public static String requestSmartwork(String requestContent, Map<String, String> header, String url) {
        String result = "";

        try {
            log.info("request url: " + url + " request content: " + requestContent + " request header: " + IoUtils.toJsonString(header));
            result = HttpUtils.executePost(url, requestContent, null, header);
        } catch (Exception e) {
            log.error("error in RequestSmartwork: smartwork request error", e);

        }

        return result;
    }
}
