package com.netease.yx.bot.common.util.qianRpc;

import com.netease.qian.rpc.consumer.RpcScannerConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> on 2019-04-15.
 */
@Configuration
public class QianRpcScannerConfigurer {
    @Bean("rpcScannerConfigurer")
    public RpcScannerConfigurer rpcScannerConfigurer() {
        RpcScannerConfigurer rpcScannerConfigurer = new RpcScannerConfigurer();
        rpcScannerConfigurer.setBasePackage(
                "com.netease.mail.yanxuan.supermc.rpc;");
        rpcScannerConfigurer.setConnectionRequestTimeout(5000);
        rpcScannerConfigurer.setMaxTotalConnections(500);
        rpcScannerConfigurer.setMaxConnectionsPerRoute(100);
        rpcScannerConfigurer.setConnectTimeout(5000);
        rpcScannerConfigurer.setTimeout(5000);
        rpcScannerConfigurer.setOrder(2);
        return rpcScannerConfigurer;
    }
}
