package com.netease.yx.bot.common.util;

import com.alibaba.fastjson.JSON;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 杭研签名校验工具类
 */
public class Signature {

    private static final String DEFAULT_CHARSET = "UTF-8";
    private static final String NONCE = "yanxuan";

    /**
     * 1. 请求参数对按key进行字典升序排序
     *
     * @param params
     */
    private static Map keysort(Map params) {
        if (params == null || params.isEmpty()) {
            return null;
        }
        Map sortMap = new TreeMap<>(Comparator.naturalOrder());
        sortMap.putAll(params);
        return sortMap;
    }


    /**
     * 2. 将列表中的参数对按URL键值对的格式拼接成字符串，value部分需要URL编码，并且拼接appkey得到字符串key1=value1&key2=value2&appkey=密钥
     *
     * @param params
     * @return
     */
    private static String contactEncode(Map params, String appKey) {
        if (params == null || params.isEmpty()) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        params.forEach((key, value) -> {
            try {

                if (value instanceof ArrayList) {
                    ArrayList objects = (ArrayList) value;
                    for (Object obj : objects) {
                        String str = String.valueOf(obj);
                        sb.append(key).append("=").append(URLEncoder.encode(str, DEFAULT_CHARSET)).append("&");
                    }
                    return;
                }

                if (value != null && value != "") {
                    sb.append(key).append("=").append(URLEncoder.encode(String.valueOf(value), DEFAULT_CHARSET)).append("&");
                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        });
        sb.append("appkey").append("=").append(appKey);
        System.out.println(sb.toString());
        return sb.toString();
    }

    /**
     * 3.字符串进行MD5运算，并转成大写
     *
     * @param str
     * @return
     */
    private static String md5(String str) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(str.getBytes());
            byte[] b = md.digest();

            int temp;
            StringBuffer sb = new StringBuffer();
            for (int offset = 0; offset < b.length; offset++) {
                temp = b[offset];
                if (temp < 0) {
                    temp += 256;
                }
                if (temp < 16) {
                    sb.append("0");
                }
                sb.append(Integer.toHexString(temp));
            }
            str = sb.toString().toUpperCase();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return str;
    }


    /**
     * 计算请求签名
     *
     * @param params 参数列表，包含业务参数以及header参数
     * @param appKey 应用密钥
     * @return
     */
    private static String getSign(Map params, String appKey) {
        if (params == null || params.isEmpty()) {
            return null;
        }
        return md5(contactEncode(keysort(params), appKey));
    }

    public static String hangyanSign(String appId, String appKey, String nonce, long timestamp) {
        Map<String, String> params = new HashMap<>(3);
        params.put("appId", appId);
        params.put("timestamp", String.valueOf(timestamp));
        params.put("nonce", nonce);
        return getSign(params, appKey);
    }

    public static void version2() {
        // 此版本要求head中增加参数: version=v2
        Map<String, Object> params = new HashMap<>();
        // 请求头参数
        String appId = "yanxuan-kf-query-similarity-online";
        String appKey = "Y4oMb6EAvG4e6dcS";
        params.put("appId", appId);
        long timestamp = System.currentTimeMillis() / 1000;
        params.put("timestamp", String.valueOf(timestamp));
        System.out.println("timestamp: " + timestamp);
        params.put("nonce", NONCE);
        System.out.println(getSign(params, appKey));
    }

    public static void main(String[] args) {
        Map<String, String> params = new HashMap<>();
        // 请求头参数
        String appId = "yanxuan_itx23b5yfp";
        String appKey = "lj883d6s28fjbegtnhui5sa10pptug";

        String nonce = "123456";
        long timestamp = System.currentTimeMillis() / 1000;
        System.out.println(timestamp);
        params.put("appId", appId);
        params.put("timestamp", String.valueOf(timestamp));
        params.put("nonce", nonce);

        System.out.println(JSON.toJSONString(params));

        System.out.println(hangyanSign(appId, appKey, nonce, timestamp));
    }
}




