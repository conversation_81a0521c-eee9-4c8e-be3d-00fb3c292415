package com.netease.yx.bot.common.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class IoUtils {
    public static final String COMMENT_PREFIX = "#";
    public static final ObjectMapper mapper = new ObjectMapper();

    static {
        // 忽略未定义的属性
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 忽略null值
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public static String toJsonString(Object object) {
        return toJsonString(object, false);
    }

    public static String toJsonString(Object object, boolean needPretty) {
        try {
            if (needPretty) {
                return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
            } else {
                return mapper.writeValueAsString(object);
            }
        } catch (Exception e) {
            log.error("toJsonString", e);
            return null;
        }
    }

    public static boolean exists(String filePath) {
        File file = new File(filePath);
        return file.exists();
    }

    public static <T> T loadJsonFromFile(String path, TypeReference<T> typeOfT) throws IOException {
        return parseJson(getLineFromFile(path), typeOfT);
    }

    public static <T> T loadJsonFromFile(String path, Class<T> tClass) throws IOException {
        return parseJson(getLineFromFile(path), tClass);
    }

    public static String getLineFromFile(String filePath) throws IOException {
        return String.join(System.lineSeparator(), getLinesFromFile(filePath));
    }

    public static List<String> getLinesFromFile(String filePath) throws IOException {
        return getLinesFromFile(filePath, false);
    }

    public static Set<String> getLineSetFromFile(String filePath) throws IOException {
        return getLineSetFromFile(filePath, false);
    }

    public static Set<String> getLineSetFromFile(String filePath, boolean removeComment) throws IOException {
        return new HashSet<>(getLinesFromFile(filePath, removeComment));
    }

    public static List<String> getLinesFromFile(String filePath, boolean removeComment) throws IOException {
        Path path = Paths.get(filePath);
        List<String> lines = Files.readAllLines(path);
        if (removeComment) {
            return lines;
        } else {
            return lines.stream().filter(line -> !line.startsWith(COMMENT_PREFIX)).collect(Collectors.toList());
        }
    }

    public static <T> T parseJson(String json, JavaType typeOfT) throws IOException {
        return mapper.readValue(json, typeOfT);
    }


    public static JsonNode parseJson(String json) throws IOException {
        return mapper.readTree(json);
    }

    public static <T> T parseJson(String json, TypeReference<T> typeOfT) throws IOException {
        return mapper.readValue(json, typeOfT);
    }

    public static <T> T parseJson(String json, Class<T> tClass) throws IOException {
        return mapper.readValue(json, tClass);
    }
}
