package com.netease.yx.bot.common.util.preprocess.berttokenizer;

import com.netease.yx.bot.core.model.entity.faq.MatchTokenizerInput;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class FullTokenizer extends Tokenizer {
    private static final boolean DEFAULT_DO_LOWER_CASE = false;
    private static final String SEPARATOR_TOKEN = "[SEP]";
    private static final String START_TOKEN = "[CLS]";

    private static final int SEPARATOR_TOKEN_ID = 102;
    private static final int START_TOKEN_ID = 101;
    private final BasicTokenizer basic;
    private final Map<String, Integer> vocabulary;
    private final WordpieceTokenizer wordpiece;

    /**
     * @param vocabulary the BERT vocabulary file to use for tokenization
     * @since 1.0.3
     */
    public FullTokenizer(final File vocabulary) {
        this(Paths.get(vocabulary.toURI()), DEFAULT_DO_LOWER_CASE);
    }

    /**
     * @param vocabulary  the BERT vocabulary file to use for tokenization
     * @param doLowerCase whether to convert sequences to lower case during tokenization
     * @since 1.0.3
     */
    public FullTokenizer(final File vocabulary, final boolean doLowerCase) {
        this(Paths.get(vocabulary.toURI()), doLowerCase);
    }

    /**
     * @param vocabularyPath the path to the BERT vocabulary file to use for tokenization
     * @since 1.0.3
     */
    public FullTokenizer(final Path vocabularyPath) {
        this(vocabularyPath, DEFAULT_DO_LOWER_CASE);
    }

    /**
     * @param vocabularyPath the path to the BERT vocabulary file to use for tokenization
     * @param doLowerCase    whether to convert sequences to lower case during tokenization
     * @since 1.0.3
     */
    public FullTokenizer(final Path vocabularyPath, final boolean doLowerCase) {
        vocabulary = loadVocabulary(vocabularyPath);
        basic = new BasicTokenizer(doLowerCase);
        wordpiece = new WordpieceTokenizer(vocabulary);
    }

    public FullTokenizer(final Path vocabularyPath, Integer maxLength) {
        vocabulary = loadVocabulary(vocabularyPath);
        basic = new BasicTokenizer(true);
        wordpiece = new WordpieceTokenizer(vocabulary, "[UNK]", maxLength);
    }

    /**
     * @param vocabularyResource the resource path to the BERT vocabulary file to use for tokenization
     * @since 1.0.3
     */
    public FullTokenizer(final String vocabularyResource) {
        this(toPath(vocabularyResource), DEFAULT_DO_LOWER_CASE);
    }

    /**
     * @param vocabularyResource the resource path to the BERT vocabulary file to use for tokenization
     * @param doLowerCase        whether to convert sequences to lower case during tokenization
     * @since 1.0.3
     */
    public FullTokenizer(final String vocabularyResource, final boolean doLowerCase) {
        this(toPath(vocabularyResource), doLowerCase);
    }

    private static Map<String, Integer> loadVocabulary(final Path file) {
        final Map<String, Integer> vocabulary = new HashMap<>();
        try (BufferedReader reader = Files.newBufferedReader(file, Charset.forName("UTF-8"))) {
            int index = 0;
            String line;
            while ((line = reader.readLine()) != null) {
                vocabulary.put(line.trim(), index++);
            }
        } catch (final IOException e) {
            throw new RuntimeException(e);
        }
        return vocabulary;
    }

    private static Path toPath(final String resource) {
        return Paths.get(resource);
    }

    /**
     * Converts BERT sub-tokens into their inputIds
     *
     * @param tokens the tokens to convert
     * @return the inputIds for the tokens
     * @since 1.0.3
     */
    public int[] convert(final String[] tokens) {
        return Arrays.stream(tokens).mapToInt(vocabulary::get).toArray();
    }

    @Override
    public String[] tokenize(final String sequence) {
        return Arrays.stream(wordpiece.tokenize(basic.tokenize(sequence)))
                .flatMap(Stream::of)
                .toArray(String[]::new);
    }

    @Override
    public String[][] tokenize(final String... sequences) {
        return Arrays.stream(basic.tokenize(sequences))
                .map((final String[] tokens) -> Arrays.stream(wordpiece.tokenize(tokens))
                        .flatMap(Stream::of)
                        .toArray(String[]::new))
                .toArray(String[][]::new);
    }

    /**
     * convert sen into bert input format
     */
    public List<MatchTokenizerInput> getInputs(List<String> senList, Integer maxLength) {
        return senList.parallelStream().map(x -> getInputs(x, maxLength)).collect(Collectors.toList());
    }

    public MatchTokenizerInput getInputs(String sen, Integer maxLength) {
        String[] tokens = tokenize(sen);
        int[] ids = convert(tokens);
        int length = 2;
        List<Integer> inputIds = new ArrayList<>(Collections.nCopies(maxLength, 0));
        inputIds.set(0, START_TOKEN_ID);
        for (int j = 0; j < ids.length && j < maxLength - 2; j++) {
            inputIds.set(j + 1, ids[j]);
            length++;
        }

        inputIds.set(length - 1, SEPARATOR_TOKEN_ID);
        return new MatchTokenizerInput(inputIds, length);

    }

    public MatchTokenizerInput getInputs(String sen, String hst, Integer maxLength) {
        String[] tokens = tokenize(sen);
        int[] ids = convert(tokens);
        String[] tokenHst = tokenize(hst);
        int[] idHst = convert(tokenHst);
        int newLength = 2;

        int length = 2;
        List<Integer> inputIds = new ArrayList<>(Collections.nCopies(maxLength, 0));
        inputIds.set(0, START_TOKEN_ID);
        for (int j = 0; j < ids.length && j < maxLength - 2; j++) {
            inputIds.set(j + 1, ids[j]);
            length++;
            newLength++;
        }

        inputIds.set(length - 1, SEPARATOR_TOKEN_ID);
        newLength++;
        if (length < maxLength - 2) {
            for (int j = length; j < length + idHst.length && j < maxLength - 1; j++) {
                inputIds.set(j, idHst[j - length]);
                newLength++;
            }
            inputIds.set(newLength - 1, SEPARATOR_TOKEN_ID);
        }


        return new MatchTokenizerInput(inputIds, newLength);

    }

}
