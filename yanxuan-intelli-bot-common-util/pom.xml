<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.netease.yx</groupId>
        <artifactId>yanxuan-intelli-bot</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <properties>
        <apolloy.version>1.0.2-RELEASE</apolloy.version>
        <dqs-client.version>2.4.13</dqs-client.version>
    </properties>
    <artifactId>yanxuan-intelli-bot-common-util</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netease.yx</groupId>
            <artifactId>yanxuan-intelli-bot-core-model</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.70</version>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netease.yanxuan.log</groupId>
            <artifactId>log-java-client</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apolloY</groupId>
            <artifactId>apolloY-client</artifactId>
            <version>${apolloy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.45</version>
        </dependency>
        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>com.netease.mail.holmes</groupId>
            <artifactId>waiter.client</artifactId>
            <version>1.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.netease.mail.dp</groupId>
            <artifactId>dqs-client</artifactId>
            <version>${dqs-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>29.0-jre</version>
        </dependency>
        <dependency>
            <groupId>org.nlpcn</groupId>
            <artifactId>nlp-lang</artifactId>
            <version>1.7.8</version>
        </dependency>
        <dependency>
            <groupId>com.netease.search</groupId>
            <artifactId>easy-search-client</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.netease.search</groupId>
            <artifactId>easy-search-search</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.netease.yanxuan.feast</groupId>
            <artifactId>feast-client</artifactId>
            <version>1.0.9.4-write</version>
            <classifier>shade</classifier>
        </dependency>
        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>segment</artifactId>
            <version>0.1.8</version>
        </dependency>
        <dependency>
            <groupId>com.netease.yanxuan.cradle</groupId>
            <artifactId>yanxuan-cradle-base</artifactId>
            <version>1.0.19-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.netease.yanxuan</groupId>
            <artifactId>ic-query</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.netease.mail</groupId>
            <artifactId>yanxuan-supermc-api</artifactId>
            <version>1.0.60-RELEASE</version>
        </dependency>
        <!-- q-rpc start-->
        <dependency>
            <groupId>com.netease.qian</groupId>
            <artifactId>qian-rpc-framework</artifactId>
            <version>1.2.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- q-rpc end-->
        <dependency>
            <groupId>com.netease.yx.essearch</groupId>
            <artifactId>essearch-client</artifactId>
            <version>0.0.8-SNAPSHOT</version>
            <classifier>shade</classifier>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.netease.mail.dp</groupId>
            <artifactId>ras-waiter</artifactId>
            <version>1.1.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.netease.mail.yanxuan</groupId>
            <artifactId>eudemon-all</artifactId>
            <version>1.0.8-RELEASE</version>
        </dependency>
    </dependencies>

</project>